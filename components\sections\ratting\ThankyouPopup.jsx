import React from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Image,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { AppImages } from "../../../utils/AppImages";
import { useNavigation } from "@react-navigation/native";

function ThankyouPopup({ modalVisible, setModalVisible }) {
  const navigation = useNavigation();

  const handleCloseModal = () => {
    setModalVisible(false);
    navigation.navigate("Profile");
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={handleCloseModal}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={handleCloseModal}
      >
        <View style={styles.modalContent}>
          <TouchableOpacity style={styles.closeIcon} onPress={handleCloseModal}>
            <Ionicons name="close" size={24} color="#000000" />
          </TouchableOpacity>

          <Image source={AppImages.PATTERN} style={styles.backgroundIMG} />
          <Image source={AppImages.Thankyou_heart} style={styles.successIMG} />
          <Text style={styles.successMessage}>Thank you for the Feedback!</Text>
          <View style={styles.orderInfo}>
            <Text style={styles.thankYouText}>Thanks for choosing</Text>
            <Text style={styles.plentiText}>Plenti</Text>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "90%",
    maxWidth: 400,
    backgroundColor: "#FFF",
    borderRadius: 20,
    padding: 20,
    alignItems: "center",
    elevation: 10,
    shadowColor: "#000",
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },
  closeIcon: {
    position: "absolute",
    top: 10,
    right: 10,
    zIndex: 1,
  },
  backgroundIMG: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    borderRadius: 20,
    resizeMode: "cover",
  },
  successIMG: {
    width: 150,
    height: 150,
    marginVertical: 20,
    resizeMode: "contain",
  },
  successMessage: {
    fontSize: 20,
    color: "#6B50F6",
    marginVertical: 10,
  },
  orderInfo: {
    flexDirection: "row",
    gap: 5,
    alignItems: "center",
  },
  thankYouText: {
    fontSize: 16,
  },
  plentiText: {
    color: "#6B50F6",
    fontSize: 16,
  },
});

export default ThankyouPopup;
