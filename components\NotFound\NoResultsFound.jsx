import React, { useState } from "react";
import { View, Text, Image, StyleSheet } from "react-native";
import { AppImages } from "../../utils/AppImages";

const NoResultsFound = ({ navigation }) => {
  return (
    <View style={styles.noResultsContainer}>
      <View style={styles.imageContainer}>
        <Image style={styles.backgroundImage} source={AppImages.BACKGROUND} />
        <Image
          style={styles.searchIconCentered}
          source={AppImages.SEARCHICON}
        />
      </View>
      <Text style={styles.noResultsText}>No Results Found</Text>
      <Text style={styles.subText}>No results found. Please try again.</Text>
    </View>
  );
};

export default NoResultsFound;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 15,
    paddingRight: 15,
  },
  inputContainer: {
    position: "relative",
    flexDirection: "row",
    alignItems: "center",
  },

  input: {
    minHeight: 51,
    minWidth: 256,
    borderColor: "#EFEFEF",
    borderWidth: 1,
    paddingLeft: 35,
    paddingRight: 15,
    marginBottom: 20,
    borderRadius: 10,
    fontSize: 17,
  },
  filter: {
    height: 51,
    width: 51,
    borderRadius: 14,
    backgroundColor: "#F2F2F2",
    justifyContent: "center",
    alignItems: "center",
    padding: 5,
    shadowColor: "#4d4242",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },

  column: {
    flexDirection: "column",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
    color: "#8C9099",
  },
  locationText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    color: "#5F22D9",
  },
  arrowDown: {
    marginLeft: 3,
    width: 14,
    height: 14,
    resizeMode: "contain",
  },
  profileIMG: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  searchIcon: {
    position: "absolute",
    left: 10,
    top: "35%",
    marginRight: 5,
    transform: [{ translateY: -10 }],
    width: 20,
    height: 20,
  },

  noResultsContainer: {
    backgroundColor: "#FFF",
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 30,
  },
  imageContainer: {
    position: "relative",
    width: 300, // Adjust width as per design
    height: 300, // Adjust height as per design
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
    resizeMode: "contain", // Ensures the background fits without distortion
  },
  searchIconCentered: {
    position: "absolute",
    right: 30,
    width: 170, // Adjust size as needed
    height: 170,
    resizeMode: "contain", // Ensures proper scaling
  },
  noResultsText: {
    textAlign: "center",
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    marginTop: 10,
    color: "#333333",
  },
  subText: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#838282",
    marginTop: 5,
    textAlign: "center",
  },
});
