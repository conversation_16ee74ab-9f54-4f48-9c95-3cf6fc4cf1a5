import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import restaurants from "../Data/Restaurants";
import { AppImages } from "../utils/AppImages";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import EmptyCart from "../components/Empty/EmptyCart";

const Favrites = ({ navigation }) => {
  const [showEmptyFav, setShowEmptyFav] = useState(false);

  const renderRestaurantCard = ({ item }) => (
    <View style={styles.cardContainer}>
      <View style={styles.imageContainer}>
        <Image source={item.image} style={styles.mainImage} />

        <View style={styles.ratingBadge}>
          <View
            style={{
              marginRight: 5,
            }}
          >
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>
          <View
            style={{
              flexDirection: "row",
              gap: 1,
            }}
          >
            <MaterialIcons name="star" size={16} color="#FFD700" />
            <Text
              style={{
                fontFamily: "Poppins_400Regular",
                fontSize: 10,
                color: "#9796A1",
              }}
            >
              (25+)
            </Text>
          </View>
        </View>

        <View style={styles.heartContainer}>
          <Image source={AppImages.LIKE} />
        </View>

        <View style={styles.priceBadge}>
          <Text style={styles.quantityText}>{item.quantity}</Text>
          <Text style={styles.priceText}>Rs{item.price}</Text>
        </View>
      </View>

      <View style={styles.infoContainer}>
        <Image source={item.logo} style={styles.logoImage} />
        <View style={styles.textContainer}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <Text style={styles.restaurantName}>{item.name}</Text>
            <View
              style={{
                flexDirection: "row",
                width: 15,
                height: 15,
                borderRadius: 20,
                backgroundColor: "#029094",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image
                source={AppImages.CHECKED}
                style={{
                  tintColor: "white",
                }}
              />
            </View>
          </View>
          <View style={styles.detailsContainer}>
            <MaterialIcons name="location-on" size={16} color="#5F22D9" />
            <Text style={styles.detailsText}>{item.distance}</Text>
            <MaterialIcons name="access-time" size={16} color="#5F22D9" />
            <Text style={styles.detailsText}>{item.timing}</Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <View>
      <SafeAreaView
        style={[GlobalStyles.androidSafeArea, { backgroundColor: "#fff" }]}
      >
        <View style={styles.container}>
          <BackButton navigation={navigation} />
          <View style={styles.titleContainer}>
            <Text
              style={{
                fontFamily: "Poppins_500Medium",
                fontSize: 18,
                color: "#323643",
              }}
            >
              My Favorites
            </Text>
          </View>
        </View>

        {showEmptyFav ? (
          <EmptyCart />
        ) : (
          <FlatList
            data={restaurants}
            renderItem={renderRestaurantCard}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 15,
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  backButton: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
    position: "absolute",
    left: 16,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },

  cardContainer: {
    marginBottom: 15,
    borderRadius: 20,
    backgroundColor: "#FFFFFF",
    shadowColor: "#D3D1D840",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 1.84,
    elevation: 3,
  },
  imageContainer: {
    width: "100%",
    height: 182,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: "hidden",
    position: "relative",
  },
  mainImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
    borderRadius: 20,
  },
  ratingBadge: {
    position: "absolute",
    top: 10,
    left: 10,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 2,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#000",
  },
  heartContainer: {
    position: "absolute",
    top: 10,
    right: 10,
    backgroundColor: "#5F22D9",
    padding: 8,
    borderRadius: 20,
  },
  priceBadge: {
    position: "absolute",
    bottom: 10,
    right: 10,
    backgroundColor: "white",
    padding: 8,
    borderRadius: 15,
    flexDirection: "row",
    alignItems: "center",
  },
  quantityText: {
    marginRight: 3,
    textDecorationLine: "line-through",
    marginTop: 3,
    color: "#666",
    fontSize: 11,
  },
  priceText: {
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9",
    fontWeight: 700,
    fontSize: 14,
  },
  infoContainer: {
    flexDirection: "row",
    padding: 15,
    alignItems: "center",
  },
  logoImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    resizeMode: "contain",
  },
  textContainer: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 5,
  },
  detailsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailsText: {
    color: "#666",
    marginRight: 10,
    marginLeft: 3,
  },
});

export default Favrites;
