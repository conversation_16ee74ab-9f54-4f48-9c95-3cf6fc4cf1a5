# Payment Integration with Razorpay

This document describes the Razorpay payment integration implemented in the Plenti mobile app.

## Overview

The payment flow is integrated into the cart checkout process and uses Razorpay for secure payment processing.

## Flow

1. **Cart to Checkout**: User clicks "Pay" button in cart
2. **Personal Details**: User enters personal information (name, email, phone)
3. **Payment Order Creation**: App creates payment order via backend API
4. **Razorpay Checkout**: Razorpay payment interface opens
5. **Payment Processing**: User completes payment via Razorpay
6. **Payment Verification**: Backend verifies payment signature
7. **Order Completion**: Cart is cleared and success message shown

## Files Modified/Created

### API Layer
- `api/Payment.js` - Payment API functions
  - `createPaymentOrder()` - Creates payment order
  - `verifyPayment()` - Verifies payment after completion
  - `dismissPayment()` - Cancels/dismisses payment order

### Components
- `components/sections/checkout/PersonalDetailsPopup.jsx` - Enhanced with payment flow
- `screens/Cart.jsx` - Integrated payment popup

### Configuration
- `config/razorpay.js` - Razorpay configuration and keys

### Tests
- `__tests__/Payment.test.js` - Unit tests for payment API

## Configuration

### Razorpay Keys
Update the keys in `config/razorpay.js`:

```javascript
export const RAZORPAY_CONFIG = {
  KEY_ID: 'rzp_test_your_key_here', // Replace with actual key
  COMPANY_LOGO: 'https://your-logo-url.com/logo.png', // Replace with logo URL
  // ... other config
};
```

### Environment Setup
- Development: Uses test keys
- Production: Uses live keys (configured in `getRazorpayConfig()`)

## API Endpoints Used

1. **Create Payment Order**
   - `POST /v1/user/payment/order/create`
   - Body: `{ checkout_id: string }`
   - Returns: Payment order details including Razorpay order ID

2. **Verify Payment**
   - `POST /v1/user/payment/verify`
   - Body: `{ razorpay_order_id, razorpay_payment_id, razorpay_signature }`
   - Returns: Verification result

3. **Dismiss Payment**
   - `DELETE /v1/user/payment/dismiss?payment_order_id={id}`
   - Returns: Dismissal confirmation

## Error Handling

- Network errors are caught and displayed to user
- Payment cancellation is handled gracefully
- Failed payments trigger order dismissal
- Form validation ensures required fields are filled

## Security

- Payment verification uses Razorpay signature verification
- Sensitive payment data is handled by Razorpay, not stored locally
- API calls use existing authentication from AxiosClient

## Testing

Run payment tests:
```bash
npm test Payment.test.js
```

## Dependencies

- `react-native-razorpay`: ^2.3.0 (already installed)
- Redux for state management
- Existing API infrastructure

## Next Steps

1. **Configure Razorpay Keys**: Update actual keys in config
2. **Test Payment Flow**: Test with Razorpay test cards
3. **Add Loading States**: Enhance UX with better loading indicators
4. **Error Recovery**: Add retry mechanisms for failed payments
5. **Analytics**: Add payment tracking and analytics
