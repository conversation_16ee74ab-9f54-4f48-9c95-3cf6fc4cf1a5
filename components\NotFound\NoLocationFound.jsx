import { View, Text, Image, StyleSheet } from "react-native";
import React, { useState } from "react";
import { AppImages } from "../../utils/AppImages";

const NoLocationFound = ({ navigation }) => {
  return (
    <View
      style={{
        flex: 1,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        gap: 10,
        paddingLeft: 30,
        paddingRight: 30,
        marginBottom: 200,
      }}
    >
      <Image source={AppImages.NOLOCATION} />
      <Text
        style={{
          textAlign: "center",
          fontSize: 14,
          fontFamily: "Poppins_500Medium",
        }}
      >
        Sorry, no restaurants or bakeries are available at your location right
        now!
      </Text>
    </View>
  );
};

export default NoLocationFound;

const styles = StyleSheet.create({});
