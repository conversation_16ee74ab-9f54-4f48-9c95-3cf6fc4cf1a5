import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  Alert,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  StatusBar,
} from "react-native";
import React, { useEffect } from "react";
import { AppImages } from "../../utils/AppImages";
import { AppColors } from "../../utils/AppColors";
import { s } from "react-native-size-matters";
import Terms from "../../components/sections/getstarted/Terms";
import AppleSignin from "../../components/sections/getstarted/AppleSignin";
import GoogleSignin from "../../components/sections/getstarted/GoogleSignin";
import PhoneField from "../../components/common/fields/PhoneField";
import CommonButton from "../../components/common/buttons/CommonButton";
import { LoginApi } from "../../api/Auth";
import Divider from "../../components/sections/getstarted/Divider";
import { ActivityIndicator } from "react-native";

const Getstarted = (props) => {
  const [code, setCode] = React.useState("+91");
  const [number, setNumber] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  const handleLogin = async () => {
    setLoading(true);
    if (number.length !== 10) {
      Alert.alert("Invalid Phone Number", "Please enter a valid phone number");
      return;
    }

    try {
      // const res = await LoginApi(code + number);
      await LoginApi(number);
      setLoading(false);
      props.navigation.navigate("VerifyNumber", { phone: number });
    } catch (error) {
      console.error(error);
    }
  };

  // useEffect(() => {
  //   LocationHelper.getLocationWithPermission();
  // }, []);

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.topContainer}>
          <Image
            style={styles.getStartedImage}
            resizeMode="cover"
            source={AppImages.GET_STARTED}
          />
        </View>

        <View style={styles.secondContainer}>
          <Text style={styles.tagline}>
            India’s First Food Surplus Marketplace
          </Text>

          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={{
              width: "100%",
              alignItems: "center",
              flexDirection: "column",
            }}
          >
            <Divider text="Login or Sign Up" />
            <PhoneField
              code={code}
              setCode={setCode}
              number={number}
              setNumber={setNumber}
            />

            <View style={{ width: "80%", marginVertical: 10 }}>
              <CommonButton
                title={
                  loading ? (
                    <ActivityIndicator
                      size="small"
                      color={AppColors.whiteColor}
                    />
                  ) : (
                    "Continue"
                  )
                }
                pressHandler={handleLogin}
                disable={loading}
              />
            </View>
          </KeyboardAvoidingView>

          <Divider text="or" />

          {/* apple sign in */}
          <AppleSignin
            buttonStyle={styles.button}
            iconStyle={styles.googleIcon}
          />

          {/* Google Sign in */}
          <GoogleSignin
            buttonStyle={styles.button}
            iconStyle={styles.googleIcon}
          />

          {/* Terms */}
          <Terms />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: AppColors.whiteColor,
    flexDirection: "column",
  },
  topContainer: {
    height: "30%",
    width: "100%",
    backgroundColor: AppColors.whiteColor,
    borderBottomLeftRadius: 72,
    borderBottomRightRadius: 72,
    shadowColor: "#000",
    shadowOpacity: 0.25,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 3 },
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
  },
  getStartedImage: {
    height: "120%",
    width: "100%",
    marginTop: -45,
  },
  button: {
    width: "80%",
    borderRadius: 100,
    flexDirection: "row",
    padding: 14,
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 6,
    shadowColor: "#000",
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 5, height: 3 },
  },
  googleIcon: {
    height: 24,
    width: 24,
  },
  tagline: {
    color: AppColors.blackColor,
    fontFamily: "Poppins_500Medium",
    fontSize: s(18),
    marginBottom: 16,
    marginTop: 21,
    paddingHorizontal: 20,
    textAlign: "center",
  },
  secondContainer: {
    width: "100%",
    alignItems: "center",
    flex: 1,
    backgroundColor: AppColors.whiteColor,
  },
});

export default Getstarted;
