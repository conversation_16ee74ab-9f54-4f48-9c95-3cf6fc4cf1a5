import { AppImages } from "../utils/AppImages";

const categories = [
  { id: 1, name: "<PERSON><PERSON>", image: AppImages.MEAL, value: "MEAL" },
  { id: 2, name: "Baked Goods", image: AppImages.BAKED, value: "BAKED_GOODS" },
  {
    id: 3,
    name: "Snacks & Desserts",
    image: AppImages.GROCERY,
    value: "SNACKS_AND_DESSERT",
  },
];

export default categories;

export const getCategoryName = (value) => {
  const category = categories.find((cat) => cat.value === value);
  return category ? category.name : null;
};
