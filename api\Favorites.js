import axiosClient from "../AxiosClient";

export const addFavorite = async (vendor_id) => {
  const response = await axiosClient.post(
    `/user/favorite/toggle?vendor_id=${vendor_id}`
  );
  console.log();
  return response.data;
};

export const getFavorite = async (latitude, longitude) => {
  const section = "favorites";

  const url = `/user/discover/get/${section}?latitude=${latitude}&longitude=${longitude}`;

  const body = {
    favorite: true,
  };

  try {
    const response = await axiosClient.post(url, body);

    return response.data;
  } catch (error) {
    console.error("Error while fetching favorites:");

    throw error;
  }
};

export const removeFavorite = async (vendor_id) => {
  const response = await axiosClient.post(
    `/user/favorite/toggle?vendor_id=${vendor_id}`
  );
  return response.data;
};
