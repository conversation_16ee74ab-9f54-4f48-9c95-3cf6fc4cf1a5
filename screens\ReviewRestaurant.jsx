import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  TextInput,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import restaurants from "../Data/Restaurants";
import { AppImages } from "../utils/AppImages";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { ratingText } from "../utils/Constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import ThankyouPopup from "../components/sections/ratting/ThankyouPopup";
import { addReview } from "../api/Orders";

const reviewTagMap = {
  "Great taste": "GREAT_TASTE",
  "Fair portion": "FAIR_PORTION",
  "Quick service": "QUICK_SERVICE",
  "Friendly staff": "FRIENDLY_STAFF",
  "Clean environment": "CLEAN_ENVIRONMENT",
  "Affordable price": "AFFORDABLE_PRICE",
  "Great location": "GREAT_LOCATION",
  "Great food quality": "GREAT_FOOD_QUALITY",
};

const reviewText = Object.keys(reviewTagMap);

const ReviewRestaurants = ({ navigation, route }) => {
  const order = route?.params?.order;
  const [rating, setRating] = useState(3);
  const [selectedButtons, setSelectedButtons] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [review, setReview] = useState("");

  const handlePress = (index) => {
    if (selectedButtons.includes(index)) {
      setSelectedButtons(selectedButtons.filter((i) => i !== index));
    } else {
      setSelectedButtons([...selectedButtons, index]);
    }
  };

  const handleAddReview = async (data) => {
    console.log("data inside handle add review function");
    console.log(data);
    try {
      const response = await addReview(data);
      if (response) {
        setRating(0);
        setReview("");
        setSelectedButtons([]);
        setModalVisible(true);
      }
    } catch (error) {
      if (error.response) {
        console.error("Error status:", error.response.status);
        console.error("Error data:", error.response.data);
        console.error("Error headers:", error.response.headers);
      } else if (error.request) {
        console.error("No response received:", error.request);
      } else {
        console.error("Error setting up request:", error.message);
      }
    }
  };

  const handleRating = (rate) => {
    setRating(rate);
  };

  return (
    <KeyboardAwareScrollView contentContainerStyle={{ flexGrow: 1 }}>
      <View style={GlobalStyles.androidSafeArea}>
        <View style={styles.outerContainer}>
          <View style={styles.container}>
            <BackButton navigation={navigation} />
          </View>
          <View style={styles.centerLogo}>
            <TouchableOpacity style={styles.logoBtn}>
              <Image source={{ uri: order.logo_url }} style={styles.logo} />
            </TouchableOpacity>
          </View>
          <Text style={styles.text}>
            How was your last Surprise Bag from {order.vendor_name}?
          </Text>
          <View>
            <Text style={styles.ratingText}>
              {rating > 0 ? ratingText[rating - 1] : "Rate us"}
            </Text>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <TouchableOpacity
                  key={star}
                  onPress={() => handleRating(star)}
                  style={styles.star}
                >
                  <View
                    style={
                      star <= rating ? styles.activeStar : styles.inactiveStar
                    }
                  >
                    <Ionicons
                      name={star <= rating ? "star" : "star-outline"}
                      size={30}
                      color="#51D6CA"
                    />
                  </View>
                </TouchableOpacity>
                
              ))}
            </View>
          </View>
          <Text style={styles.sectext}>What did you like the most?</Text>
          <View style={styles.container1}>
            {reviewText.map((text, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.button,
                  selectedButtons.includes(index) && styles.activeButton,
                ]}
                onPress={() => handlePress(index)}
              >
                <Text
                  style={[
                    styles.buttonText,
                    selectedButtons.includes(index) && styles.activeButtonText,
                  ]}
                >
                  {text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <Text style={styles.thrtext}>
            What was in your Plenti Surprise Bag?
          </Text>
          <View style={styles.textInputContainer}>
            <TextInput
              style={styles.furtext}
              placeholder="Write review"
              placeholderTextColor="#C7C7CD"
              multiline={true}
              value={review}
              onChangeText={setReview}
            />
          </View>
          <View style={styles.submitButtonContainer}>
            <TouchableOpacity
              style={styles.submitButton}
              activeOpacity={0.8}
              onPress={() => {
                if (rating === 0 || review.trim() === "") {
                  alert(
                    "Please provide a rating and review before submitting."
                  );
                  return;
                }
                const payload = {
                  vendor_id: order.vendor_id,
                  order_id: order.order_id,
                  rating,
                  review,
                  tags: selectedButtons.map(
                    (index) => reviewTagMap[reviewText[index]]
                  ),
                };
                handleAddReview(payload);
              }}
            >
              <Text style={styles.submitButtonText}>Submit</Text>
            </TouchableOpacity>
          </View>
        </View>
        {modalVisible && (
          <ThankyouPopup
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
          />
        )}
      </View>
    </KeyboardAwareScrollView>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    backgroundColor: "#FFFFFF",
    height: "100%",
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  centerLogo: {
    flexDirection: "row",
    justifyContent: "center",
    alignContent: "center",
  },
  logoBtn: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
  },
  logo: {
    width: 50,
    height: 40,
  },
  text: {
    marginTop: 15,
    paddingLeft: 20,
    paddingRight: 20,
    fontSize: 25,
    fontFamily: "Poppins_300Light",
    textAlign: "center",
    color: "#000000",
  },
  ratingText: {
    marginTop: 15,
    textAlign: "center",
    fontSize: 20,
    fontFamily: "Poppins_400Medium",
    color: "#5F22D9",
  },
  starsContainer: {
    flexDirection: "row",
    marginTop: 10,
    flexDirection: "row",
    justifyContent: "center",
  },
  activeStar: {
    shadowColor: "#FFC52966",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.8,
    shadowRadius: 6,
    elevation: 5,
    borderRadius: 20,
    padding: 5,
  },
  inactiveStar: {
    padding: 5,
  },
  sectext: {
    marginTop: 20,
    fontSize: 16,
    fontFamily: "Poppins_400Medium",
    paddingLeft: 25,
    color: "#393A3C",
  },
  thrtext: {
    marginTop: 10,
    fontSize: 16,
    fontFamily: "Poppins_400Medium",
    paddingLeft: 25,
    color: "#393A3C",
  },
  furtext: {
    height: 120, // Adjust height for multiline
    borderWidth: 1,
    borderColor: "#E5E5E5",
    borderRadius: 12,
    padding: 10,
    fontSize: 16,
    color: "#000",
    backgroundColor: "#FFF",
  },
  textInputContainer: {
    padding: 20,
  },
  container1: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 20,
    marginRight: 30,
    marginTop: 5,
  },
  button: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: "#5F22D9",
    borderRadius: 20,
    margin: 5,
    backgroundColor: "#FFF",
  },
  buttonText: {
    color: "#5F22D9",
    fontSize: 14,
    textAlign: "center",
    fontFamily: "Poppins_500Medium",
  },
  activeButton: {
    backgroundColor: "#5F22D9",
  },
  activeButtonText: {
    color: "#FFF",
  },
  submitButtonContainer: {
    position: "absolute",
    bottom: 30,
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
  },
  submitButton: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#5F22D940",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ReviewRestaurants;
