import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from "react-native";

const Dropdown = ({ selectedFilter, setSelectedFilter }) => {
  const [isOpen, setIsOpen] = useState(false);

  const options = [
    { label: "Sort by Latest Review", value: "latest" },
    { label: "Sort by Oldest Review", value: "oldest" },
  ];

  const handleSelect = (option) => {
    setSelectedFilter(option.value);
    setIsOpen(false);
  };

  const getLabel = (value) => {
    const found = options.find((opt) => opt.value === value);
    return found ? found.label : "";
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.dropdownButton}
        onPress={() => setIsOpen(!isOpen)}
      >
        <Text style={styles.buttonText}>{getLabel(selectedFilter)}</Text>
        <Ionicons
          name={isOpen ? "chevron-up-outline" : "chevron-down-outline"}
          size={20}
          color="#000"
        />
      </TouchableOpacity>

      {isOpen && (
        <View style={styles.dropdownList}>
          <FlatList
            data={options}
            keyExtractor={(item) => item.value}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={() => handleSelect(item)}
              >
                <Text style={styles.itemText}>{item.label}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      )}
    </View>
  );
};

export default Dropdown;

const styles = StyleSheet.create({
  container: {
    width: "60%",
    marginTop: 10,
    paddingLeft: 20,
    position: "relative",
  },
  dropdownButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 8,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  buttonText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    color: "#000",
  },
  dropdownList: {
    position: "absolute",
    top: "100%",
    left: 20,
    width: "100%",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    zIndex: 100,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  itemText: {
    fontSize: 16,
    color: "#000",
  },
});
