{"name": "plenti-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/poppins": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/messaging": "^22.2.1", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "@reduxjs/toolkit": "^2.5.0", "axios": "^1.7.9", "expo": "^53.0.9", "expo-apple-authentication": "~7.2.4", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.8", "expo-checkbox": "~4.1.4", "expo-clipboard": "~7.1.4", "expo-dev-client": "~5.1.8", "expo-font": "^13.0.2", "expo-location": "~18.1.4", "expo-notifications": "~0.31.4", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-web-browser": "~14.1.6", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-native": "0.79.2", "react-native-country-codes-picker": "^2.3.5", "react-native-gesture-handler": "~2.24.0", "react-native-google-maps": "^1.0.0", "react-native-google-places-autocomplete": "^1.9.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "1.20.1", "react-native-otp-entry": "^1.8.2", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-size-matters": "^0.4.2", "react-native-svg": "15.11.2", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-country-codes-picker", "react-native-keyboard-aware-scroll-view", "expo-checkbox", "react-native-google-maps"], "listUnknownPackages": false}}}}