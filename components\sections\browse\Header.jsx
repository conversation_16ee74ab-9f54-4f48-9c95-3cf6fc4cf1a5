import { View, Text, StyleSheet } from "react-native";
import React from "react";
import FULL_WIDTH from "../../../utils/Constants";
import BackButton from "../../common/buttons/BackButton";

const Header = ({ navigation }) => {
  return (
    <View style={styles.container}>
      <BackButton navigation={navigation} />
      <Text style={styles.text}>Choose a location</Text>
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    width: FULL_WIDTH,
    height: 88,
    padding: 16,
    backgroundColor: "white",
    borderBottomRightRadius: 36,
    borderBottomLeftRadius: 36,
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    position: "absolute",
    left: 100,
    right: 100,
    textAlign: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#323643",
  },
});
