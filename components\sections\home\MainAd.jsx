import { View, Text, StyleSheet, Image, Animated } from "react-native";
import { useEffect, useRef, useState } from "react";

const MainAd = ({ image }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const shimmerAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (!imageLoaded && !imageError) {
      const shimmer = Animated.loop(
        Animated.sequence([
          Animated.timing(shimmerAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(shimmerAnimation, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      shimmer.start();
      return () => shimmer.stop();
    }
  }, [imageLoaded, imageError, shimmerAnimation]);

  const shimmerOpacity = shimmerAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const SkeletonLoader = () => (
    <View style={styles.skeletonContainer}>
      <Animated.View
        style={[styles.shimmerEffect, { opacity: shimmerOpacity }]}
      />
      <View style={styles.placeholderContent}></View>
    </View>
  );

  return (
    <View style={styles.heroContainer}>
      {!imageLoaded && !imageError && <SkeletonLoader />}

      {image && !imageError && (
        <Image
          source={{ uri: image }}
          style={[
            styles.bannerImage,
            {
              position: "absolute",
              top: 0,
              left: 15,
              right: 15,
              opacity: imageLoaded ? 1 : 0,
            },
          ]}
          resizeMode="cover"
          onLoad={() => setImageLoaded(true)}
          onError={() => {
            setImageError(true);
            setImageLoaded(false);
          }}
        />
      )}

      {imageError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load banner</Text>
        </View>
      )}
    </View>
  );
};

export default MainAd;

const styles = StyleSheet.create({
  heroContainer: {
    paddingHorizontal: 15,
    marginTop: 10,
    position: "relative",
    height: 200,
    justifyContent: "center",
  },

  bannerImage: {
    width: "100%",
    height: 200,
    borderRadius: 10,
  },
  skeletonContainer: {
    width: "100%",
    height: 200,
    borderRadius: 10,
    backgroundColor: "#E1E9EE",
    position: "relative",
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
  },
  shimmerEffect: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "#F7F8FA",
    borderRadius: 10,
  },
  placeholderContent: {
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1,
  },
  placeholderText: {
    color: "#A0A0A0",
    fontSize: 14,
    fontWeight: "500",
  },
  errorContainer: {
    width: "100%",
    height: 200,
    borderRadius: 10,
    backgroundColor: "#F5F5F5",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderStyle: "dashed",
  },
  errorText: {
    color: "#999",
    fontSize: 14,
    fontWeight: "500",
  },
  heroContainer1: {
    flexDirection: "row",
    alignItems: "center",
    paddingLeft: 15,
    height: 148,
    borderRadius: 20,
    backgroundColor: "#5F22D9",
    overflow: "hidden",
  },
  heroText: {
    color: "white",
    fontSize: 18,
    fontWeight: "800",
    letterSpacing: -0.67,
    width: "100%",
  },
  heroText1: {
    color: "white",
    fontWeight: 400,
    fontSize: 14,
    lineHeight: 16,
    marginVertical: 8,
  },
  heroButton: {
    color: "white",
    fontSize: 14,
    fontWeight: "900",
    lineHeight: 21.29,
    marginLeft: 5,
  },
});
