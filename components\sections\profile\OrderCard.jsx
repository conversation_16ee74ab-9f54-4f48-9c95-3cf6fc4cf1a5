import React from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import { AppImages } from "../../../utils/AppImages";

const OrderCard = ({ title, logo, total, quantity, navigation, order }) => (
  <View style={styles.card}>
    <View style={styles.row}>
      <TouchableOpacity style={styles.logoBtn}>
        <Image source={{ uri: logo }} style={styles.logo} />
      </TouchableOpacity>
      <View style={styles.details}>
        <Text style={styles.subtitle}>
          {quantity} Surprise Bag{quantity > 1 ? "s" : ""}
        </Text>
        <Text style={styles.title}>{title}</Text>
      </View>
      <View style={styles.price}>
        <Text style={styles.priceText}>Grand Total</Text>
        <Text style={styles.priceValue}>Rs {total}</Text>
      </View>
    </View>
    <View style={styles.action}>
      <TouchableOpacity
        style={styles.rateButton}
        onPress={() => navigation.navigate("ReviewRestaurant", { order })}
      >
        <Text style={styles.rateButtonText}>Rate</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.orderButton}
        onPress={() => navigation.navigate("OrderStatus")}
      >
        <Text style={styles.orderButtonText}>Order Details</Text>
      </TouchableOpacity>
    </View>
  </View>
);

const RecentOrders = ({ navigation, activeOrders }) => {
  const isLoading = activeOrders === undefined || activeOrders === null;
  const isEmpty = activeOrders && activeOrders.length === 0;

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.noOrdersContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : isEmpty ? (
        <View style={styles.noOrdersContainer}>
          <Text style={styles.noOrdersText}>No recent orders</Text>
        </View>
      ) : (
        <ScrollView
          contentContainerStyle={{ paddingBottom: 750 }}
          showsVerticalScrollIndicator={false}
        >
          {activeOrders.map((order, index) => (
            <OrderCard
              key={order.order_id || index}
              navigation={navigation}
              title={order.vendor_name}
              logo={order.logo_url}
              total={order.amount_paid}
              quantity={order.items?.length || 1}
              order={order}
            />
          ))}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 10,
    backgroundColor: "#FFFFFF",
    height: "100%",
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 16,
    gap: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },

  noOrdersContainer: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  noOrdersText: {
    fontSize: 16,
    color: "#666",
    fontWeight: "500",
  },
  loadingText: {
    fontSize: 16,
    color: "#333",
    fontWeight: "600",
  },

  card: {
    backgroundColor: "#fff",
    borderRadius: 15,
    padding: 16,
    margin: 5,
    shadowColor: "#000", // Black shadow
    shadowOpacity: 0.05, // Lower opacity for a lighter shadow
    shadowRadius: 2, // Keep radius small for a subtle spread
    shadowOffset: { width: 0, height: 2 }, // Smaller vertical offset for a soft drop
    elevation: 1,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  logoBtn: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
  },
  logo: {
    width: 50,
    height: 40,
  },
  details: {
    flex: 1,
    marginLeft: 10,
  },
  title: {
    fontFamily: "Poppins_500Medium",
    fontSize: 16,
    color: "#000000",
  },
  subtitle: {
    fontFamily: "Poppins_400Regular",
    fontSize: 12,
    color: "#9796A1",
  },
  price: {
    alignItems: "flex-end",
  },
  priceText: {
    fontFamily: "Poppins_400Regular",
    fontSize: 12,
    color: "#9796A1",
  },
  priceValue: {
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color: "#111719",
  },
  action: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
    paddingLeft: 20,
  },
  rateButton: {
    flex: 1,
    backgroundColor: "#fff",
    paddingVertical: 12,
    marginHorizontal: 5,
    borderRadius: 20,
    shadowColor: "#000", // Black shadow
    shadowOpacity: 0.05, // Lower opacity for a lighter shadow
    shadowRadius: 2, // Keep radius small for a subtle spread
    shadowOffset: { width: 0, height: 2 }, // Smaller vertical offset for a soft drop
    elevation: 1,
    alignItems: "center",
  },
  rateButtonText: {
    fontFamily: "Poppins_500Medium",
    color: "#111719",
    fontSize: 12,
    fontWeight: "bold",
  },
  orderButton: {
    flex: 1,
    backgroundColor: "#6C63FF",
    paddingVertical: 12,
    marginHorizontal: 5,
    borderRadius: 14,
    alignItems: "center",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  orderButtonText: {
    color: "#FFFFFF",
    fontFamily: "Poppins_500Medium",
    fontSize: 12,
  },
});

export default RecentOrders;
