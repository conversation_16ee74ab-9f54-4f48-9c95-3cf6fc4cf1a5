import { View, Text, Image } from "react-native";
import React, { useEffect } from "react";
import StarRating from "../rattings/RattingsStars";

const UserReview = ({ review }) => {
  const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getInitials = (name) => {
    if (!name) return "??";
    const parts = name.trim().split(" ");
    return (parts[0]?.[0] || "") + (parts[1]?.[0] || "");
  };

  const userName = review?.user_name || "Anonymous";
  const initials = getInitials(userName);
  const userImage = review?.user_profile_picture_url;

  return (
    <View
      style={{
        paddingLeft: 30,
        flexDirection: "column",
        justifyContent: "flex-start",
        gap: 10,
        paddingRight: 30,
      }}
    >
      <Text
        style={{
          color: "#858585",
          fontFamily: "Poppins_500Medium",
          fontSize: 14,
          marginTop: 10,
        }}
      >
        {formatDate(review?.created_at)}
      </Text>

      <StarRating rating={review?.rating} />

      <View
        style={{
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
        }}
      >
        {userImage ? (
          <Image
            source={{ uri: userImage }}
            style={{
              height: 40,
              width: 40,
              borderRadius: 20,
              backgroundColor: "#c1c6f4",
            }}
          />
        ) : (
          <View
            style={{
              backgroundColor: "#c1c6f4",
              height: 40,
              width: 40,
              borderRadius: 20,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: "#5465FF",
                fontFamily: "Poppins_500Medium",
                fontSize: 16,
              }}
            >
              {initials.toUpperCase()}
            </Text>
          </View>
        )}

        <Text
          style={{
            fontFamily: "Poppins_500Medium",
            fontSize: 16,
            color: "#0D0C22",
          }}
        >
          {userName}
        </Text>
      </View>

      <Text
        style={{
          fontFamily: "Poppins_400Regular",
          fontSize: 14,
          color: "#0D0C22",
        }}
      >
        {review?.review || "No review text."}
      </Text>

      <View
        style={{
          marginTop: 5,
          marginBottom: 5,
          width: "100%",
          height: 2,
          backgroundColor: "#F4F5F7",
        }}
      />
    </View>
  );
};

export default UserReview;
