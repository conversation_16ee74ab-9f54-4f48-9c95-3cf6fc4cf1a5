import * as WebBrowser from "expo-web-browser";

export const openPrivacyPolicy = async () => {
  const result = await WebBrowser.openBrowserAsync(
    "https://plenti.food/privacy"
  );
};

export const openTermsAndConditions = async () => {
  const result = await WebBrowser.openBrowserAsync(
    "https://www.plenti.co.in/terms"
  );
};

export const openContentPolicites = async () => {
  const result = await WebBrowser.openBrowserAsync(
    "https://www.plenti.co.in/terms"
  );
};

export const OpenWebsite = async () => {
  const result = await WebBrowser.openBrowserAsync("https://www.plenti.co.in/");
};

export const SignUpStore = async () => {
  const result = await WebBrowser.openBrowserAsync(
    "https://partner.plenti.co.in/verify_email"
  );
};
