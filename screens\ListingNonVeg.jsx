import React, { useRef, useState, useEffect, use } from "react";
import {
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
  ScrollView,
  Dimensions,
  Modal,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import GlobalStyles from "../styles/GlobalStyles";
import { AppImages } from "../utils/AppImages";
import QuantityPopup from "../components/sections/Cart/QuantityPopup";
import { GetItemApi, GetVendorReviewsApi } from "../api/Discover";
import { useFocusEffect } from "@react-navigation/native";
import Header from "../components/sections/listing/Header";
import RestaurantDetails from "../components/sections/listing/RestaurantDetails";
import { convertTime } from "../components/sections/home/<USER>";
import { getCategoryName } from "../Data/Categories";
import Address from "../components/sections/listing/Address";

function ListingNonVeg({ navigation, route }) {
  const { item, vendor } = route.params;
  const [modalVisible, setModalVisible] = useState(false);
  const [selected, setSelected] = useState("veg");
  const [reviews, setReviews] = useState(null);
  const animationsRef = useRef(
    new Array(5).fill(null).map(() => new Animated.Value(0))
  );

  const fetchReviews = async () => {
    if (!vendor?.vendor_id) return;
    try {
      const response = await GetVendorReviewsApi(vendor.vendor_id);

      setReviews(response);
    } catch (error) {
      console.error("Error fetching reviews:", error);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, [vendor?.vendor_id]);

  const calculateBarWidths = () => {
    if (!reviews) return [0, 0, 0, 0, 0];
    const totalReviews = reviews.num_reviews || 1;

    return [
      (reviews.five_star_reviews || 0) / totalReviews,
      (reviews.four_star_reviews || 0) / totalReviews,
      (reviews.three_star_reviews || 0) / totalReviews,
      (reviews.two_star_reviews || 0) / totalReviews,
      (reviews.one_star_reviews || 0) / totalReviews,
    ];
  };

  const animateBars = (barWidths) => {
    barWidths.forEach((width, index) => {
      Animated.timing(animationsRef.current[index], {
        toValue: width,
        duration: 1000,
        useNativeDriver: false,
      }).start();
    });
  };

  useEffect(() => {
    const barWidths = calculateBarWidths();
    animateBars(barWidths);
  }, [reviews]);

  const handleToggle = (value) => {
    setSelected(value);
  };

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          backgroundColor: "#FFFFFF",
          height: "100%",
        }}
      >
        <Header bgImage={vendor?.backcover_url} navigation={navigation} />
        <View style={styles.padding}>
          <RestaurantDetails
            logo={vendor?.logo_url}
            name={vendor?.vendor_name}
            mealType={getCategoryName(item?.item_type)}
            actualPrice={item?.actual_price}
            price={item?.price}
            pickupWindow={`${convertTime(
              item?.window_start_time
            )} - ${convertTime(item?.window_end_time)}`}
          />
        </View>
        <View style={styles.line1}></View>
        <Address address={vendor.address} />

        <View style={{ flex: 1, paddingBottom: 70 }}>
          <ScrollView>
            <View style={styles.scrollContainer}>
              <Text style={styles.headingTXT}>What you could get</Text>
              <Text style={styles.subheadingTXT}>{item?.description}</Text>
              <View style={styles.innerConatiner}>
                <Text style={styles.dietHeading}>Diet Preference</Text>
                <View style={styles.Radiocontainer}>
                  {item?.veg && (
                    <TouchableOpacity
                      onPress={() => handleToggle("veg")}
                      style={styles.radioButtonContainer}
                    >
                      <Text style={styles.radioText}>Veg</Text>
                      <View style={styles.outerCircle}>
                        {selected === "veg" && (
                          <View style={styles.innerCircle} />
                        )}
                      </View>
                    </TouchableOpacity>
                  )}
                  {item?.non_veg && (
                    <TouchableOpacity
                      onPress={() => handleToggle("non_veg")}
                      style={styles.radioButtonContainer}
                    >
                      <Text style={styles.radioText}>Non-Veg</Text>
                      <View style={styles.outerCircle}>
                        {selected === "non_veg" && (
                          <View style={styles.innerCircle} />
                        )}
                      </View>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <View style={styles.line2}></View>
              <View style={styles.marketContainer}>
                <Text style={styles.heading}>Reviews</Text>
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate("Reviews", { vendor: vendor })
                  }
                  style={styles.recommendConatiner}
                >
                  <Text style={styles.viewAllText}>View all</Text>
                  <Image
                    style={styles.viewAllArrow}
                    source={AppImages.VIEW_ALL}
                  />
                </TouchableOpacity>
              </View>
              <View style={styles.rattingContainer}>
                <View style={styles.textContainer}>
                  <Text style={styles.ratingText}>
                    {reviews?.avg_rating
                      ? reviews.avg_rating.toFixed(1)
                      : "0.0"}
                  </Text>
                  <Text style={styles.subText}>
                    {reviews?.num_reviews} ratings
                  </Text>
                </View>
                <View style={styles.barsContainer}>
                  {animationsRef.current.map((anim, index) => (
                    <View key={index} style={styles.row}>
                      <View style={styles.track}>
                        <Animated.View
                          style={[
                            styles.bar,
                            {
                              width: anim.interpolate({
                                inputRange: [0, 1],
                                outputRange: ["0%", "100%"],
                              }),
                            },
                          ]}
                        />
                      </View>
                      <Text style={styles.label}>{5 - index}</Text>
                    </View>
                  ))}
                </View>
              </View>
              <View style={styles.CardsConatiner}>
                <View style={styles.cardsRow}>
                  <Image source={AppImages.TASTE} />
                  <Text style={styles.cardTXT}>Greate Taste</Text>
                </View>
                {/* <Text style={styles.rattingTXT}>200 rattings+</Text> */}
              </View>
              <View style={styles.CardsConatiner}>
                <View style={styles.cardsRow}>
                  <Image source={AppImages.CUP} />
                  <Text style={styles.cardTXT}>Fair Postion</Text>
                </View>
                {/* <Text style={styles.rattingTXT}>200 rattings+</Text> */}
              </View>
            </View>
          </ScrollView>
          <View style={styles.floatingButtonContainer}>
            <TouchableOpacity
              style={styles.button}
              activeOpacity={0.8}
              onPress={() => setModalVisible(true)}
            >
              <Text style={styles.buttonText}>Reserve</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {modalVisible && (
        <QuantityPopup
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          navigation={navigation}
          item={item}
          vendor={vendor}
          diet={selected}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  padding: {
    paddingTop: 12,
    paddingLeft: 20,
    paddingRight: 20,
  },
  rattingTXT: {
    fontSize: 14,
    color: "#94979F",
    fontFamily: "Poppins_400Medium",
  },
  cardTXT: {
    fontSize: 14,
    color: "#181E22",
    fontFamily: "Poppins_400Medium",
  },
  cardsRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 13,
  },
  CardsConatiner: {
    backgroundColor: "#FFF",
    marginTop: 20,
    borderRadius: 16,
    shadowColor: "#0D5EF933",
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 3,
    padding: 10,
    flexDirection: "row",
    gap: 5,
    justifyContent: "space-between",
    alignItems: "center",
  },
  dietHeading: {
    fontFamily: "Poppins_400Regular",
    color: "#393A3C",
    fontSize: 16,
  },
  innerConatiner: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 10,
    paddingBottom: 10,
  },
  subheadingTXT: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#8B8B8C",
    marginTop: 3,
  },
  headingTXT: {
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color: "#393A3C",
  },
  scrollContainer: {
    backgroundColor: "#FFF",
    shadowColor: "#000",
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    shadowOffset: { width: -2, height: -2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
    padding: 20,
    marginTop: 20,
    position: "relative",
  },

  restaurantLOGO: {
    height: 35,
    width: 35,
    resizeMode: "contain",
  },
  recommendConatiner: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  marketContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
  },
  heading: {
    fontFamily: "Poppins_400Regular",
    color: "#393A3C",
    fontSize: 16,
  },
  viewAllText: {
    fontFamily: "Poppins_400Regular",
    color: "#5F22D9",
  },
  viewAllArrow: {
    resizeMode: "contain",
    marginLeft: 5,
  },
  buttonText: {
    color: "white",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  line1: {
    marginLeft: 30,
    marginRight: 30,
    height: 1,
    marginTop: 10,
    backgroundColor: "rgba(176, 176, 176, 0.33)",
  },
  line2: {
    height: 1,
    marginTop: 5,
    backgroundColor: "rgba(176, 176, 176, 0.33)",
  },
  line3: {
    height: 1,
    marginTop: 10,
    backgroundColor: "rgba(176, 176, 176, 0.33)",
  },

  logoLocation: {
    height: 30,
    width: 30,
    resizeMode: "contain",
  },

  location: {
    fontSize: 14,
    fontFamily: "Poppins_400Medium",
    color: "#858992",
    marginTop: 1,
  },
  distance: {
    fontSize: 14,
    fontFamily: "Poppins_400Medium",
    color: "#858992",
    marginTop: 1,
  },
  toggleButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 7,
    borderRadius: 25,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  orangeBackground: {
    backgroundColor: "#D93722",
  },
  greenBackground: {
    backgroundColor: "#28a745",
  },
  toggleText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
    marginRight: 10,
  },
  rattingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 10,
  },
  textContainer: {
    marginRight: 20,
  },
  ratingText: {
    fontSize: 50,
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9", // Purple color
  },
  subText: {
    fontSize: 16,
    color: "#888",
  },
  barsContainer: {
    flex: 1,
    justifyContent: "space-evenly",
    paddingVertical: 10,
  },

  row: {
    flexDirection: "row",
    alignItems: "center",
  },

  track: {
    width: 230, // Set a fixed width for animation to work
    height: 8,
    backgroundColor: "#E3E9ED",
    borderRadius: 5,
    overflow: "hidden",
  },

  bar: {
    height: "100%",
    backgroundColor: "#5F22D9",
    borderRadius: 5,
  },

  label: {
    marginLeft: 8,
    fontSize: 14,
  },

  circle: {
    width: 20,
    height: 20,
    backgroundColor: "#fff",
    borderRadius: 10,
  },
  floatingButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: 10,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },

  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 18,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#FE724C33",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  popupContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  reserveButton: {
    backgroundColor: "#6C5CE7",
    padding: 16,
    borderRadius: 30,
    alignItems: "center",
  },
  buttonText: {
    color: "#FFF",
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },

  Radiocontainer: {
    flexDirection: "row",
    marginVertical: 5,
  },
  radioButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  radioText: {
    marginRight: 10,
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
  },
  outerCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#5F22D9",
    alignItems: "center",
    justifyContent: "center",
  },
  innerCircle: {
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: "#5F22D9",
  },
});

export default ListingNonVeg;
