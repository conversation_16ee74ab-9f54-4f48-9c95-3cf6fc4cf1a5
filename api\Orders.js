import axiosClient from "../AxiosClient";

export const getRecentOrders = async (status) => {
  const url = `/user/order/get?active=${status}&skip=0&limit=10`;

  try {
    const response = await axiosClient.get(url);

    return response.data;
  } catch (error) {
    console.error("Error while fetching recent orders:");
    throw error;
  }
};

export const addReview = async (reviewBody) => {
  const url = `user/order/review/add`;

  try {
    const response = await axiosClient.post(url, reviewBody);

    console.log("add review API RESPONSE:");
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error("Error while fetching recent orders:");
    throw error;
  }
};
