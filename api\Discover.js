import axiosClient from "../AxiosClient";

//  login API
export const LoginApi = async (number) => {
  const response = await axiosClient.post(
    `user/me/login/sms/send?phone_number=${number}`
  );
  return response.data;
};

// Get Vendors
export const GetVendorsApi = async (lat, long, data, section) => {
  const response = await axiosClient.post(
    `user/discover/get/${section}?latitude=${lat}&longitude=${long}`,
    data
  );
  return response.data;
};

// Get Banner
export const GetBannerApi = async () => {
  const response = await axiosClient.get(`user/discover/banner/get`);
  return response.data;
};

// Get Item Details
export const GetItemApi = async (data) => {
  const response = await axiosClient.post(`user/discover/item/get`, data);
  return response.data;
};

export const Search = async (data) => {
  const response =
    await axiosClient.get(`user/discover/get/:section?latitude={{latitude}}&longitude={{longitude}}
`);
  console.log("serach API response");
  console.log(response.data);
  return response.data;
};

export const GetVendorReviewsApi = async (vendor_id) => {
  const response = await axiosClient.get(
    `user/discover/review/get?vendor_id=${vendor_id}`
  );

  return response.data;
};

export const GetVendorAllReviewsApi = async (vendor_id, sort_by) => {
  const response = await axiosClient.get(
    `user/discover/review/get/all?vendor_id=${vendor_id}&sort_by=${sort_by}&skip=0&limit=10`
  );
  return response.data;
};
