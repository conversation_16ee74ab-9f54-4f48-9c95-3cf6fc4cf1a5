import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView,
  ActivityIndicator,
  Alert,
  Modal,
  Dimensions,
} from "react-native";
import { SvgXml, Svg, Path } from "react-native-svg";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { AppImages } from "../utils/AppImages";
import { TextInput } from "react-native-gesture-handler";
import PersonalDetailsPopup from "../components/sections/checkout/PersonalDetailsPopup";

import EmptyCart from "../components/Empty/EmptyCart";
import {
  GetCheckoutApi,
  getSuggestions,
  updateCheckoutItemQuantity,
  removeCheckoutItem
} from "../api/Checkout";
import SuggestedItemSlider from "../components/sections/checkout/SuggestedItemSlider";
import { useDispatch, useSelector } from "react-redux";
import { clearSelectedCoupon } from "../redux/slices/couponSlice";
import {
  setCheckoutData,
  updateItemQuantity,
  removeCartItem,
  setCartLoading,
  setUpdatingQuantity
} from "../redux/slices/cartSlice";
import CartDebugger from "../components/debug/CartDebugger";

const { width } = Dimensions.get("window");

// Apple
const appleSvg = `<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M31.1926 8.31803L30.8725 8.42475C27.3381 9.60288 24.9541 12.9105 24.9541 16.6361V11.3529C24.9541 8.37911 22.8551 5.81872 19.939 5.23551L14.5565 4.15901V5.28317C14.5565 8.25696 16.6555 10.8173 19.5716 11.4006L24.9541 12.4771M32.9426 14.034C35.1199 13.8849 37.25 14.7164 38.7505 16.3012C40.1572 17.787 40.9509 19.7422 40.9851 21.7767L40.9795 22.2475L40.9259 23.5608C40.8113 26.3668 40.3599 29.1475 39.5822 31.8434L39.2753 32.8503L38.5118 35.2264C37.5716 38.1523 35.2368 40.4182 32.284 41.2704C31.0304 41.6321 29.3221 41.5542 27.1592 41.0366L26.558 40.8845C25.5067 40.6047 24.4004 40.6048 23.3491 40.8846C20.9 41.5364 18.9917 41.665 17.6241 41.2704C14.6714 40.4182 12.3365 38.1523 11.3964 35.2264L10.6328 32.8503C9.66633 29.8423 9.11111 26.7175 8.98227 23.5608L8.92867 22.2475C8.83882 20.0458 9.64269 17.9013 11.1576 16.3012C12.6581 14.7164 14.7882 13.8849 16.9655 14.034L18.2509 14.122C20.099 14.2485 22.3338 15.6048 24.9554 18.1908C27.382 15.7263 29.4539 14.3858 31.1711 14.1692L31.6573 14.122L32.9426 14.034Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
</svg>
`;

// beef
const beefSvg = `<svg width="43" height="37" viewBox="0 0 43 37" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.4517 2.50158L13.9005 2.08764C18.6687 0.7253 23.7114 0.641278 28.5223 1.84401L30.4713 2.33127C35.0153 3.46726 38.6076 6.94184 39.8944 11.4454L40.4356 13.3399C41.4328 16.83 41.2424 20.5524 39.8944 23.9225L39.7456 24.2943C38.6325 27.0771 36.0092 28.9454 33.0422 29.1095L32.6161 29.1213C29.2507 29.1213 25.9856 30.2666 23.3577 32.3689L22.2185 33.2803C19.0179 35.8408 14.6677 36.4313 10.9004 34.8167L9.74146 34.3201C5.23372 32.3882 2.0893 28.2098 1.481 23.3434L1.13565 20.5807C0.64843 16.6829 1.48193 12.7343 3.50293 9.36593C5.50716 6.02555 8.70605 3.57176 12.4517 2.50158ZM28.5223 1.84401L30.4713 2.33127C35.0153 3.46726 38.6076 6.94184 39.8944 11.4454L40.4356 13.3399C40.5655 13.7946 40.6753 14.2532 40.765 14.7144C40.5715 15.725 40.2804 16.7189 39.8944 17.684L39.7456 18.0558C38.6325 20.8386 36.0092 22.7069 33.0422 22.871L32.6161 22.8827C29.2507 22.8827 25.9856 24.0281 23.3577 26.1304L22.2185 27.0418C19.0179 29.6022 14.6677 30.1928 10.9004 28.5782L9.74146 28.0815C5.23372 26.1496 2.0893 21.9713 1.481 17.1049L1.26167 15.3819C1.63032 13.2702 2.38492 11.2293 3.50293 9.36593C5.50716 6.02555 8.70605 3.57176 12.4517 2.50158L13.9005 2.08764C18.6687 0.7253 23.7114 0.641278 28.5223 1.84401ZM40.8301 15.2001C38.8587 11.1371 34.7394 8.55723 30.2233 8.55723H29.5488C24.2925 8.55723 19.1517 7.0147 14.7638 4.12084L13.4326 3.24292C13.0613 2.99808 12.6833 2.767 12.2991 2.54975L12.4517 2.50158L13.9005 2.08764C16.2846 1.40647 18.7373 1.04488 21.1954 1.00392L22.1173 1.0036C24.2684 1.03791 26.4175 1.31782 28.5223 1.84401L30.4713 2.33127C35.0153 3.46726 38.6076 6.94184 39.8944 11.4454L40.4356 13.3399C40.6226 13.9943 40.7678 14.6569 40.8715 15.3237L40.863 15.2681C40.8521 15.2454 40.8411 15.2228 40.8301 15.2001ZM8.7756 15.8874C8.7756 17.6101 10.6377 19.0066 12.9346 19.0066C15.2316 19.0066 17.0936 17.6101 17.0936 15.8874C17.0936 14.1646 15.2316 12.7681 12.9346 12.7681C10.6377 12.7681 8.7756 14.1646 8.7756 15.8874Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="square"/>
</svg>
`;

// Banana
const bananaSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.989 18.2104C14.527 14.6724 19.7066 14.1159 22.558 16.9673C23.3212 17.7305 23.8402 18.6603 24.1233 19.6809C22.7738 21.3753 21.2452 22.9173 19.5642 24.2798C19.5678 22.7694 19.0637 21.3573 18.0201 20.3138C15.8645 18.1581 12.1363 18.3043 9.23126 20.4949C9.69473 19.689 10.2824 18.917 10.989 18.2104ZM33.5442 21.7363L33.4252 21.7316C37.4577 21.7316 40.7268 25.7877 40.7268 30.7911C40.7268 31.3911 40.6798 31.9774 40.5905 32.5437C39.5817 29.4735 37.1309 27.3067 34.2677 27.3067C33.7105 27.3067 33.169 27.3887 32.6507 27.5435C32.2894 26.8671 31.8263 26.2356 31.2614 25.6707L31.3921 25.8093C32.102 24.7555 32.7214 23.637 33.2399 22.4654L33.5442 21.7363ZM32.8916 5.65131C33.9991 5.89626 34.8041 6.85428 34.8546 7.98737L35.0656 12.7206C35.2809 17.5511 33.9014 22.0833 31.3931 25.8079L31.2614 25.6707C27.9782 22.3875 22.4479 22.5444 18.6397 25.9282L18.9662 25.9315C20.7022 25.9804 22.3531 26.6178 23.6006 27.8654C24.6612 28.9259 25.2808 30.2779 25.4728 31.7259C22.2754 33.8769 18.4646 35.2039 14.3325 35.3881C13.688 35.4168 13.0426 35.4168 12.3981 35.3881L7.66483 35.1771C6.28172 35.1155 5.21046 33.9443 5.2721 32.5612C5.32261 31.4281 6.12763 30.47 7.23508 30.2251C18.5565 27.721 27.3985 18.879 29.9026 7.55762C30.2016 6.2058 31.5398 5.35232 32.8916 5.65131ZM18.6397 25.9282C22.4479 22.5444 27.9782 22.3875 31.2614 25.6707C34.6831 29.0924 34.3683 34.955 30.5582 38.7651C27.4832 41.8401 23.0713 42.6384 19.6846 41.0333C20.8212 40.5846 21.9065 39.8855 22.8504 38.9416C26.1162 35.6758 26.4521 30.7168 23.6006 27.8654C22.3531 26.6178 20.7022 25.9804 18.9662 25.9315L18.6397 25.9282Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
</svg>`;

// crab
const crabSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
  <path d="M5.39535 11.6361L9.66755 12.3481C10.0504 12.4119 10.4378 12.444 10.826 12.444C14.7176 12.444 17.8724 9.28919 17.8724 5.39755L13.7134 7.47706V1.23853L13.2982 1.24871C8.89718 1.46504 5.39535 5.10186 5.39535 9.55657V11.6361ZM5.39535 11.6361V12.966C5.39535 14.8005 6.0018 16.5835 7.12031 18.0376L9.55437 21.2018M8.51461 22.0336H5.88625C4.31093 22.0336 2.87082 22.9237 2.16631 24.3327L1.23633 26.1927M7.05896 28.2722H5.88625C4.31093 28.2722 2.87082 29.1622 2.16631 30.5712L1.23633 32.4312M12.6736 34.5107H10.0453C8.46995 34.5107 7.02984 35.4007 6.32533 36.8098L5.39535 38.6697M31.3892 22.0336H34.0176C35.5929 22.0336 37.033 22.9237 37.7375 24.3327L38.6675 26.1927M32.8449 28.2722H34.0176C35.5929 28.2722 37.033 29.1622 37.7375 30.5712L38.6675 32.4312M27.2302 34.5107H29.8586C31.4339 34.5107 32.874 35.4007 33.5785 36.8098L34.5085 38.6697M34.5085 11.6361V12.966C34.5085 14.8005 33.902 16.5835 32.7835 18.0376L30.3495 21.2018M34.5085 11.6361L30.2363 12.3481C29.8535 12.4119 29.466 12.444 29.0779 12.444C25.1862 12.444 22.0314 9.28919 22.0314 5.39755L26.1905 7.47706V1.23853L26.6056 1.24871C31.0067 1.46504 34.5085 5.10186 34.5085 9.55657V11.6361ZM12.2378 34.1419L13.9733 35.1605C17.6646 37.3272 22.2392 37.3272 25.9305 35.1605L27.666 34.1419C30.3703 32.5546 32.2862 29.9073 32.9489 26.8425C33.4865 24.356 31.9066 21.9045 29.4202 21.3669L29.2714 21.3373L26.9703 20.9939C22.3168 20.3045 17.5871 20.3045 12.9336 20.9939L10.7821 21.3126C8.26564 21.6854 6.52787 24.0276 6.90068 26.5441C6.9155 26.6441 6.93361 26.7437 6.95498 26.8425C7.61765 29.9073 9.53357 32.5546 12.2378 34.1419Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M16.834 17.0428V16.8349V17.0428Z" stroke="currentColor" stroke-width="2.92431" stroke-linecap="round" stroke-linejoin="round"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M23.0703 17.0428V16.8349V17.0428Z" stroke="currentColor" stroke-width="2.92431" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

// Eggs
const eggsSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="44" height="38" viewBox="0 0 44 38" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.5168 1C19.067 1 23.0921 6.33863 25.5417 12.1983C27.3428 9.30505 29.6622 7.15855 32.1927 7.15855C37.9351 7.15855 42.5902 18.2122 42.5902 23.9546C42.5902 29.697 37.9351 34.3522 32.1927 34.3522C29.3794 34.3522 26.8271 33.2349 24.9553 31.4199C22.4797 34.4309 18.7226 36.3517 14.5168 36.3517C7.05169 36.3517 1 30.3 1 22.8349C1 20.9346 1.39211 18.5871 2.09983 16.143L2.42224 15.0906C4.6406 8.22524 9.22336 1 14.5168 1ZM14.5168 36.3517C21.982 36.3517 28.0336 30.3 28.0336 22.8349C28.0336 20.9346 27.6415 18.5871 26.9338 16.143L26.6114 15.0906C24.393 8.22524 19.8103 1 14.5168 1C9.22336 1 4.6406 8.22524 2.42224 15.0906L2.09983 16.143C1.39211 18.5871 1 20.9346 1 22.8349C1 30.3 7.05169 36.3517 14.5168 36.3517Z"
  stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
</svg>
`;

// Fish
const fishSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="42" height="32" viewBox="0 0 42 32" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.3833 10.5012C22.0307 4.48379 34.7296 7.42645 40.747 17.0739L40.2717 17.8013C39.1277 19.4738 37.7422 20.9696 36.1589 22.2393C28.0659 28.7295 16.5706 28.1126 9.21561 21.2177C8.03637 24.2582 4.97636 26.3097 1.55219 26.0613L1.23633 26.0318V25.4958C1.23633 22.2836 2.64265 19.3494 4.92135 17.3416C2.80299 15.698 1.40789 13.1824 1.25109 10.354L1.23633 9.82014V8.11597L1.66736 8.13331C4.90549 8.34322 7.67797 10.3283 8.98674 13.1518C10.0189 12.155 11.1581 11.2654 12.3833 10.5012ZM24.1884 26.7472L24.462 26.7339C22.946 28.2436 21.0877 29.3903 18.9726 30.0112C17.3294 30.4937 15.6755 30.6164 14.0806 30.4235C14.8594 28.8417 15.4634 27.1506 15.873 25.3887C18.524 26.4107 21.3603 26.87 24.1884 26.7472ZM26.218 7.3005L26.4333 7.62454C22.3582 6.99085 18.0771 7.5722 14.1725 9.50358C12.9792 6.98234 11.3787 4.78453 9.4936 2.98852C10.307 2.53633 11.178 2.16701 12.0975 1.89704C17.5246 0.303679 23.0676 2.63377 26.218 7.3005Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M31.3906 15.1223V14.9144V15.1223Z" stroke="currentColor" stroke-width="2.92431" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

// Milk
const milkSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="22" height="41" viewBox="0 0 22 41" fill="none">
  <path d="M5.53011 1.63306H15.9277M2.01066 17.2534L3.94197 14.1862C5.63562 11.4964 6.72425 8.47049 7.13244 5.3182L7.60963 1.63306H13.8482L14.2796 5.08446C14.6803 8.29035 15.775 11.3704 17.4873 14.1101L19.4545 17.2577C19.8676 17.9187 20.0867 18.6825 20.0867 19.4619V34.9052C20.0867 37.2022 18.2246 39.0643 15.9277 39.0643H5.53011C3.23315 39.0643 1.37109 37.2022 1.37109 34.9052V19.4695C1.37109 18.6853 1.59281 17.917 2.01066 17.2534ZM1.37109 24.5077L2.67763 24.9432C5.76648 25.9728 9.16391 25.4215 11.7686 23.4679L12.2077 23.1386C14.3609 21.5237 17.2012 21.1887 19.6574 22.2301L20.0867 22.4282V34.9052C20.0867 37.2022 18.2246 39.0643 15.9277 39.0643H5.53011C3.23315 39.0643 1.37109 37.2022 1.37109 34.9052V24.5077Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
</svg>`;

// Pork
const porkSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M15.0425 42.5527C13.8243 43.7709 11.8493 43.7709 10.6312 42.5527C9.6712 41.5928 9.46774 40.1628 10.0208 39.0036L9.76904 38.7518C8.60983 39.3049 7.17983 39.1014 6.21986 38.1414C5.00171 36.9233 5.00171 34.9483 6.21986 33.7301C7.43801 32.512 9.41302 32.512 10.6312 33.7301C10.8893 33.9883 11.0928 34.2805 11.2415 34.5922L15.0386 30.7937C15.4365 31.352 15.8859 31.8849 16.3868 32.3858C16.8882 32.8872 17.4216 33.3371 17.9806 33.7353L14.1823 37.532C14.4934 37.6807 14.7848 37.8838 15.0425 38.1414C16.2606 39.3596 16.2606 41.3346 15.0425 42.5527ZM25.7905 5.33979L25.8185 5.30891C28.2548 2.87261 34.1798 4.84762 39.0524 9.72022C43.925 14.5928 45.9 20.5178 43.4637 22.9541L34.032 32.3858C29.1594 37.2584 21.2594 37.2584 16.3868 32.3858C11.5142 27.5132 11.5142 19.6132 16.3868 14.7406L25.7905 5.33979ZM25.8185 5.30891C23.3822 7.74521 25.3572 13.6702 30.2298 18.5428C35.1024 23.4154 41.0274 25.3904 43.4637 22.9541C45.9 20.5178 43.925 14.5928 39.0524 9.72022C34.1798 4.84762 28.2548 2.87261 25.8185 5.30891ZM31.7002 11.1907C30.8881 12.0028 31.5464 13.9778 33.1706 15.602C34.7948 17.2262 36.7698 17.8845 37.5819 17.0724C38.394 16.2603 37.7357 14.2853 36.1115 12.6611C34.4873 11.0369 32.5123 10.3786 31.7002 11.1907Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
</svg>`;

// Walnut
const walnutSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="38" viewBox="0 0 32 38" fill="none">
  <path d="M15.955 8.59633V29.3914M8.85512 9.81447C7.23092 11.4387 7.23092 14.072 8.85512 15.6962C9.82447 16.6656 11.1533 17.0564 12.4125 16.8687M20.9754 30.2528C22.5996 28.6286 22.5996 25.9952 20.9754 24.371M25.0378 15.9564C23.8197 14.7383 21.8447 14.7383 20.6265 15.9564M6.90175 26.0642C8.1199 27.2824 10.0949 27.2824 11.3131 26.0642M15.955 36.6697C26.3526 36.6697 30.5116 28.3517 30.5116 20.2783C30.5116 11.7971 25.6594 5.47706 15.955 1.31804C6.25063 5.47706 1.39844 11.7971 1.39844 20.2783C1.39844 28.3517 5.55746 36.6697 15.955 36.6697ZM15.955 6.8736L18.1633 9.08125C18.7451 8.77172 19.4091 8.59633 20.114 8.59633C22.411 8.59633 24.2731 10.4584 24.2731 12.7553C24.2731 13.6124 24.0138 14.4089 23.5695 15.0707C25.1898 15.6352 26.3526 17.1787 26.3526 18.9939C26.3526 19.7517 26.1499 20.4622 25.7958 21.0741C26.1499 21.6846 26.3526 22.3951 26.3526 23.1529C26.3526 25.4499 24.4905 27.3119 22.1935 27.3119C22.1935 29.6089 20.3315 31.4709 18.0345 31.4709C17.2769 31.4709 16.5666 31.2684 15.9549 30.9144C15.345 31.2679 14.634 31.4709 13.8755 31.4709C11.5785 31.4709 9.71648 29.6089 9.71648 27.3119L9.40609 27.3005C7.25409 27.1417 5.55746 25.3455 5.55746 23.1529C5.55746 22.3951 5.76014 21.6846 6.11426 21.0727C5.76014 20.4622 5.55746 19.7517 5.55746 18.9939C5.55746 17.1787 6.72026 15.6352 8.3416 15.0675C7.89621 14.4089 7.63697 13.6124 7.63697 12.7553C7.63697 10.4584 9.49903 8.59633 11.796 8.59633C12.501 8.59633 13.1649 8.77172 13.7467 9.08125L15.955 6.8736Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="square"/>
</svg>`;

// Mushroom
const mushroomSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="40" height="33" viewBox="0 0 40 33" fill="none">
  <path d="M22.4087 15.7233C26.9537 15.1457 31.4419 17.737 33.2142 21.9619M23.663 3.86856L28.3838 7.64522C29.3833 8.44484 29.99 9.55352 30.1766 10.7253C28.0583 9.92204 25.7028 9.68779 23.34 10.155L21.4804 10.5228C20.6857 10.6799 19.9293 10.9903 19.2533 11.4367C17.22 12.7791 16.2285 15.1075 16.495 17.375C12.8026 17.4181 9.1082 17.0761 5.47105 16.3487C2.79516 15.8135 1.05978 13.2104 1.59495 10.5345C1.82295 9.39455 2.4456 8.37147 3.35341 7.64522L8.07424 3.86856C12.6311 0.223074 19.1061 0.223074 23.663 3.86856ZM10.6694 17.0099C12.6047 17.2392 14.5495 17.3614 16.4949 17.3766C16.6049 18.3126 16.931 19.2419 17.4886 20.0863L18.1144 19.9358C19.459 19.649 20.841 19.587 22.2042 19.7514L22.3043 20.0626L19.0109 25.7676C18.5445 26.5754 18.3799 27.4746 18.4821 28.3346C17.668 28.6532 16.7834 28.8257 15.8686 28.8257C12.9974 28.8257 10.6698 26.4982 10.6698 23.627L10.6694 17.0099ZM37.5708 18.3712L38.1822 20.1655C39.2933 23.4269 37.5502 26.9715 34.2889 28.0827C33.6754 28.2917 33.0342 28.4035 32.3882 28.415L31.8958 28.4043C31.4608 26.7809 30.7026 25.2649 29.6693 23.946L26.2145 29.9266C25.066 31.9159 22.5224 32.5974 20.5332 31.4489C18.5439 30.3005 17.8624 27.7568 19.0109 25.7676L22.4647 19.7856C21.0173 19.58 19.5447 19.6308 18.1144 19.9358L17.4886 20.0863C15.5861 17.2049 16.378 13.3351 19.2533 11.4367C19.9293 10.9903 20.6857 10.6799 21.4804 10.5228L23.34 10.155C29.472 8.94243 35.555 12.4545 37.5708 18.3712Z" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
</svg>`;

// shrimp
const shrimpSvg = `<svg width="51" height="51" viewBox="0 0 51 51" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M32.6423 39.2051L32.4534 39.6181C30.5397 43.5473 26.1208 45.6328 21.8409 44.5628L21.205 44.4038L21.2161 44.062C21.3921 41.3502 23.6474 39.2051 26.4038 39.2051C23.5326 39.2051 21.205 36.8775 21.205 34.0063L21.8409 33.8473C26.2684 32.7405 30.8446 35.0104 32.6423 39.2051ZM32.6423 39.2051L33.8644 32.4835C34.3432 29.85 32.6559 27.3087 30.0429 26.728C26.9288 26.036 23.8758 25.0929 20.9138 23.9082L20.1653 23.6087C17.0254 22.3528 14.9665 19.3117 14.9665 15.93V15.2907M32.6423 39.2051L35.6467 37.9175C38.4063 36.7348 40.6574 34.6122 42.0001 31.9268L42.1624 31.6022C43.3996 29.1278 43.7108 26.2925 43.0399 23.6087C42.3837 20.9839 40.5551 18.8075 38.0826 17.7086L37.3212 17.3702C34.2363 15.9991 30.898 15.2907 27.5221 15.2907H21.205M14.9665 15.2907C12.6695 15.2907 10.8075 13.4286 10.8075 11.1317V9.05217M14.9665 15.2907H21.205M21.205 15.2907H10.8075C8.51049 15.2907 6.64844 13.4286 6.64844 11.1317V6.97266M43.3525 27.2987L43.123 27.667C41.4704 30.1925 38.5583 31.7117 35.4369 31.5166L33.9428 31.4223M42.0639 21.242L38.3042 24.5834L37.8988 24.9238C35.8312 26.5628 33.1664 27.2545 30.5627 26.8282M35.3559 16.5978L29.5071 21.9218L29.137 22.2404C26.7473 24.1864 23.5194 24.7655 20.6015 23.7703L20.9634 23.8831" stroke="currentColor" stroke-width="1.94954" stroke-linecap="round"/>
<path d="M23.2852 19.4497H23.4931" stroke="currentColor" stroke-width="2.92431" stroke-linecap="round"/>
</svg>`;



const allergens = [
  { id: 1, name: "Apple", icon: appleSvg },
  { id: 2, name: "Beef", icon: beefSvg },
  { id: 3, name: "Banana", icon: bananaSvg },
  { id: 4, name: "Crab", icon: crabSvg },
  { id: 5, name: "Eggs", icon: eggsSvg },
  { id: 6, name: "Fish", icon: fishSvg },
  { id: 7, name: "Milk", icon: milkSvg },
  { id: 8, name: "Mushroom", icon: mushroomSvg },
  { id: 9, name: "Shrimp", icon: shrimpSvg },
  { id: 10, name: "Pork", icon: porkSvg },
  { id: 11, name: "Mushroom", icon: mushroomSvg },
  { id: 12, name: "Walnut", icon: walnutSvg },
];

const Card = ({ item, vendor, checkoutId, onQuantityUpdate, onRemoveItem }) => {
  const [quantity, setQuantity] = useState(item.quantity);
  const [isUpdating, setIsUpdating] = useState(false);

  // Sync local quantity state with prop changes
  useEffect(() => {
    setQuantity(item.quantity);
  }, [item.quantity]);

  const handleIncrease = async () => {
    if (isUpdating || !checkoutId) return;

    const newQuantity = quantity + 1;
    const originalQuantity = quantity;
    setIsUpdating(true);

    // Optimistically update the UI
    setQuantity(newQuantity);

    try {
      console.log("📈 Increasing quantity for item:", item.id, "from", originalQuantity, "to", newQuantity);
      // Call the parent handler first to update the state
      await onQuantityUpdate(item.id, newQuantity);
      console.log("✅ Quantity increase successful");
    } catch (error) {
      console.error("❌ Error increasing quantity:", error);
      // Revert the optimistic update
      setQuantity(originalQuantity);
      Alert.alert("Error", "Failed to update quantity. Please try again.");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDecrease = async () => {
    if (quantity <= 1 || isUpdating || !checkoutId) return;

    const newQuantity = quantity - 1;
    const originalQuantity = quantity;
    setIsUpdating(true);

    // Optimistically update the UI
    setQuantity(newQuantity);

    try {
      console.log("📉 Decreasing quantity for item:", item.id, "from", originalQuantity, "to", newQuantity);
      // Call the parent handler first to update the state
      await onQuantityUpdate(item.id, newQuantity);
      console.log("✅ Quantity decrease successful");
    } catch (error) {
      console.error("❌ Error decreasing quantity:", error);
      // Revert the optimistic update
      setQuantity(originalQuantity);
      Alert.alert("Error", "Failed to update quantity. Please try again.");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemove = async () => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item from cart?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: async () => {
            setIsUpdating(true);
            try {
              // Call the parent handler to remove the item
              await onRemoveItem(item.id);
            } catch (error) {
              console.error("Error removing item:", error);
              Alert.alert("Error", "Failed to remove item");
            } finally {
              setIsUpdating(false);
            }
          },
        },
      ]
    );
  };

  // Get vendor name from item description or use default
  const getVendorName = () => {    
      return vendor?.vendor_name || "Restaurant";
  };

  return (
    <View style={styles.card}>
      <Image source={{ uri: item.image_url }} style={styles.image} />
      <View style={styles.infoContainer}>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{getVendorName()}</Text>
          <Text style={styles.subtitle}>{item.item_type || "Surprise Bag"}</Text>
          <Text style={styles.price}>₹{item.actual_price}</Text>
        </View>
        <View style={styles.quantityContainer}>
          <TouchableOpacity
            onPress={handleDecrease}
            style={[styles.quantityButton, isUpdating && styles.disabledButton]}
            disabled={isUpdating || quantity <= 1}
          >
            {isUpdating ? (
              <ActivityIndicator size="small" color="#5F22D9" />
            ) : (
              <Text style={styles.quantityButtonText}>-</Text>
            )}
          </TouchableOpacity>
          <Text style={styles.quantity}>
            {quantity.toString().padStart(2, "0")}
          </Text>
          <TouchableOpacity
            onPress={handleIncrease}
            style={[styles.IncreaseQuantityButton, isUpdating && styles.disabledButton]}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.IncreaseQuantityButtonText}>+</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={handleRemove}
        disabled={isUpdating}
      >
        <Text style={styles.removeButtonText}>×</Text>
      </TouchableOpacity>
    </View>
  );
};

const Cart = ({ navigation, route }) => {
  const vendor = useSelector((state) => state.cart.selectedVendor);
  const selectedCoupon = useSelector((state) => state.coupon.selectedCoupon);
  const cartItems = useSelector((state) => state.cart.cartItems);
  const paymentBreakdown = useSelector((state) => state.cart.paymentBreakdown);
  const checkoutData = useSelector((state) => state.cart.checkoutData);
  const isLoading = useSelector((state) => state.cart.isLoading);
  const dispatch = useDispatch();

  const [showEmptyCart, setShowEmptyCart] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [isUpdatingQuantity, setIsUpdatingQuantity] = useState(false);
  const [hasHadItems, setHasHadItems] = useState(false);
  const [allergensModalVisible, setAllergensModalVisible] = useState(false);
  const [selectedAllergens, setSelectedAllergens] = useState([]);

  const fetchSuggestions = async (vendor_id, item_ids) => {
    try {
      console.log("🔍 Fetching suggestions for vendor:", vendor_id, "items:", item_ids);
      const response = await getSuggestions(vendor_id, item_ids);
      console.log("✅ Suggestions response:", response);
      setSuggestions(response || []);
    } catch (error) {
      console.error("❌ Error fetching suggestions:", error);
      setSuggestions([]);
    }
  };

  const GetCheckoutData = async (showLoader = true) => {
    try {
      if (showLoader) {
        dispatch(setCartLoading(true));
      }

      console.log("🔄 Fetching checkout data...");
      const response = await GetCheckoutApi();
      console.log("📋 Get Checkout API response:", response);

      // Check if response is valid
      if (!response || !Array.isArray(response)) {
        console.warn("⚠️ Invalid response format:", response);
        // Only set empty cart if we don't have existing items and not updating
        if (cartItems.length === 0 && !isUpdatingQuantity) {
          setShowEmptyCart(true);
        }
        return;
      }

      // Check if we have any checkouts
      if (response.length === 0) {
        console.log("ℹ️ No active checkouts found");
        // Set empty cart state
        setShowEmptyCart(true);
        setHasHadItems(false);
        dispatch(setCheckoutData(null));
        return;
      }

      const checkout = response[0];
      console.log("📦 Processing checkout:", checkout);

      // Check if checkout has items
      if (!checkout?.checkout_items || checkout.checkout_items.length === 0) {
        console.log("ℹ️ Checkout has no items");
        // Set empty cart state
        setShowEmptyCart(true);
        setHasHadItems(false);
        dispatch(setCheckoutData(null));
        return;
      }

      // Valid checkout with items
      console.log("✅ Valid checkout with", checkout.checkout_items.length, "items");
      dispatch(setCheckoutData(checkout));
      setShowEmptyCart(false);

      // Fetch suggestions for all items
      if (checkout.checkout_items.length > 0) {
        const itemIds = checkout.checkout_items.map(item => item.id);
        fetchSuggestions(checkout.vendor_id, itemIds);
      }
    } catch (error) {
      console.error("❌ Error fetching checkout data:", error);
      if (error.response) {
        console.error("📊 Error response status:", error.response.status);
        console.error("📋 Error response data:", error.response.data);
      }
      // Don't immediately show empty cart on error - keep current state
      console.log("🔄 Keeping current cart state due to error");
    } finally {
      if (showLoader) {
        dispatch(setCartLoading(false));
      }
    }
  };

  const handleQuantityUpdate = async (itemId, newQuantity) => {
    console.log("🔄 Handling quantity update in Cart:", { itemId, newQuantity });

    if (!checkoutData || !checkoutData.checkout_id) {
      console.error("❌ No checkout data available");
      Alert.alert("Error", "No active checkout found. Please refresh the page.");
      return;
    }

    // Prevent showing empty cart during updates
    setIsUpdatingQuantity(true);
    dispatch(setUpdatingQuantity(true));

    // Store the original state for potential rollback
    const originalCheckoutData = { ...checkoutData };

    // Optimistically update the Redux state
    dispatch(updateItemQuantity({ itemId, quantity: newQuantity }));

    try {
      // Call the API with current checkout data to avoid fetching
      console.log("📤 Calling updateCheckoutItemQuantity with current data");
      console.log("📋 Current checkout data:", JSON.stringify(checkoutData, null, 2));
      console.log("📋 Updating item ID:", itemId, "to quantity:", newQuantity);

      const response = await updateCheckoutItemQuantity(
        checkoutData.checkout_id,
        itemId,
        newQuantity,
        checkoutData // Pass current data to avoid fetching
      );

      console.log("✅ API update successful:", response);

      // Update Redux with the response data
      if (response && response.checkout_items) {
        dispatch(setCheckoutData(response));
      }

      console.log("✅ Cart updated successfully");
    } catch (error) {
      console.error("❌ Error updating quantity:", error);

      // Rollback to original state
      console.log("🔄 Rolling back to original state");
      dispatch(setCheckoutData(originalCheckoutData));

      // Show user-friendly error
      Alert.alert(
        "Update Failed",
        "Failed to update quantity. Please check your connection and try again.",
        [{ text: "OK" }]
      );
    } finally {
      // Always reset the updating flag
      setIsUpdatingQuantity(false);
      dispatch(setUpdatingQuantity(false));
    }
  };

  const handleRemoveItem = async (itemId) => {
    console.log("🗑️ Handling item removal in Cart:", itemId);

    if (!checkoutData || !checkoutData.checkout_id) {
      console.error("❌ No checkout data available");
      Alert.alert("Error", "No active checkout found. Please refresh the page.");
      return;
    }

    // Store the original state for potential rollback
    const originalCheckoutData = { ...checkoutData };

    // Optimistically remove from Redux state
    dispatch(removeCartItem(itemId));

    try {
      // Call the API with current checkout data
      console.log("📤 Calling removeCheckoutItem with current data");
      const response = await removeCheckoutItem(
        checkoutData.checkout_id,
        itemId,
        checkoutData // Pass current data to avoid fetching
      );

      console.log("✅ API removal successful:", response);

      // Update Redux with the response data
      if (response && response.checkout_items) {
        dispatch(setCheckoutData(response));
      } else if (response && response.message && response.message.includes("empty checkout items")) {
        // If checkout was cancelled due to empty items, clear the cart
        console.log("📭 Checkout cancelled - no items left");
        dispatch(setCheckoutData(null));
        setShowEmptyCart(true);
        setHasHadItems(false);
      }

      console.log("✅ Item removed successfully");
    } catch (error) {
      console.error("❌ Error removing item:", error);

      // Rollback to original state
      console.log("🔄 Rolling back to original state");
      dispatch(setCheckoutData(originalCheckoutData));

      // Show user-friendly error
      Alert.alert(
        "Remove Failed",
        "Failed to remove item. Please check your connection and try again.",
        [{ text: "OK" }]
      );
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await GetCheckoutData(false);
    setRefreshing(false);
  };

  const toggleAllergen = (allergenId) => {
    setSelectedAllergens((prev) =>
      prev.includes(allergenId)
        ? prev.filter((id) => id !== allergenId)
        : [...prev, allergenId]
    );
  };

  const handleAllergensApply = () => {
    // Handle apply logic here
    setAllergensModalVisible(false);
  };

  const handleAllergensClose = () => {
    setAllergensModalVisible(false);
  };

  const renderAllergenItem = (allergen) => {
    const isSelected = selectedAllergens.includes(allergen.id);

    return (
      <TouchableOpacity
        key={allergen.id}
        style={[
          styles.allergenItem,
          isSelected && styles.selectedAllergenItem,
        ]}
        onPress={() => toggleAllergen(allergen.id)}
      >
        <View style={styles.allergenIconContainer}>
          <SvgXml
            xml={allergen.icon}
            width={32}
            height={32}
            color={isSelected ? "#FFFFFF" : "#000000"}
          />
        </View>
        <Text style={[
          styles.allergenName,
          isSelected && styles.selectedAllergenName
        ]}>
          {allergen.name}
        </Text>
      </TouchableOpacity>
    );
  };

  useEffect(() => {
    GetCheckoutData();

    return () => {
      dispatch(clearSelectedCoupon());
    };
  }, []);

  // Listen for navigation focus to refresh data
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      GetCheckoutData(false);
    });

    return unsubscribe;
  }, [navigation]);

  // Track if we've ever had items to prevent showing empty cart during updates
  useEffect(() => {
    if (cartItems.length > 0) {
      setHasHadItems(true);
      setShowEmptyCart(false);
    }
  }, [cartItems]);

  const [firstModalVisible, setFirstModalVisible] = useState(false);

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingtext}>Cart</Text>
        </View>
      </View>
      {showEmptyCart ? (
        <EmptyCart />
      ) : cartItems.length === 0 && isUpdatingQuantity ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#5F22D9" />
          <Text style={styles.loadingText}>Loading cart...</Text>
        </View>
      ) : (
        <ScrollView>
          {/* Debug info - remove in production */}
          {/* <CartDebugger visible={__DEV__} /> */}

          {/* Show loading indicator when updating quantities */}
          {isUpdatingQuantity && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#5F22D9" />
              <Text style={styles.loadingText}>Updating cart...</Text>
            </View>
          )}

          <View style={styles.flatListConatiner}>
            <FlatList
              data={cartItems}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <Card
                  item={item}
                  vendor={vendor}
                  checkoutId={checkoutData?.checkout_id}
                  onQuantityUpdate={handleQuantityUpdate}
                  onRemoveItem={handleRemoveItem}
                />
              )}
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          </View>

          <View style={styles.secondSection}>
            <View style={styles.PromoCodeConatiner}>
              <View style={styles.container1}>
                <TextInput
                  value={selectedCoupon?.code || ""}
                  placeholder={selectedCoupon?.code ? "" : "Promo code"}
                  editable={true}
                  style={styles.textInput}
                  placeholderTextColor="#B0B0B0"
                />
                <View style={styles.promoInnerContainer}>
                  <TouchableOpacity
                    onPress={() => navigation.navigate("Coupon")}
                  >
                    <Image source={AppImages.PROMO} style={styles.promoImage} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.copyButton}>
                    <Text style={styles.copyText}>Apply</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            {suggestions && suggestions.length > 0 && (
              <View>
                <Text
                  style={{
                    fontFamily: "Poppins_500Medium",
                    fontSize: 16,
                    marginTop: 14,
                  }}
                >
                  Complete your purchase with
                </Text>
                <SuggestedItemSlider
                  data={suggestions}
                  onRefreshCart={() => GetCheckoutData(false)}
                />
              </View>
            )}

            {/* Allergens & Dislikes Section */}
            <View style={styles.allergensInputContainer}>
              <View style={styles.allergensInputContent}>
                <View style={styles.allergensIcon}>
                  <Image
                    source={AppImages.DISLIKE}
                    style={styles.dislikeIcon}
                  />
                </View>
                <Text style={styles.allergensInputText}>
                  Allergens & Dislikes
                </Text>
              </View>
              <TouchableOpacity
                style={styles.selectInputButton}
                onPress={() => setAllergensModalVisible(true)}
              >
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Text style={styles.selectInputButtonText}>Select</Text>
                  <Svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="8"
                    height="5"
                    viewBox="0 0 8 5"
                    style={{ marginLeft: 6 }}
                  >
                    <Path
                      d="M1.00544 0.843923L3.95656 4.00509L7.00178 0.970367"
                      stroke="white"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </Svg>
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.billCard}>
              <Text style={styles.header}>Total Bill</Text>

              <View style={styles.row}>
                <Text style={styles.label}>
                  Quantity (
                  {cartItems.reduce((acc, item) => acc + item.quantity, 0)}{" "}
                  items)
                </Text>
                <Text style={styles.boldText}>
                  ₹{paymentBreakdown?.subtotal?.toFixed(0) || ""}
                </Text>
              </View>

              <View style={styles.row}>
                <Text style={styles.label}>Platform Fee</Text>
                <Text style={styles.boldText}>
                  ₹{paymentBreakdown?.platform_fee?.toFixed(0) || ""}
                </Text>
              </View>

              {paymentBreakdown?.coupon_discount != 0 && (
              <View style={styles.row}>
                <Text style={styles.label}>Voucher</Text>
                <Text style={styles.purpleText}>
                  -₹{paymentBreakdown?.coupon_discount?.toFixed(0) || ""}
                </Text>
              </View> )}
            </View>
            <PersonalDetailsPopup
              firstModalVisible={firstModalVisible}
              setFirstModalVisible={setFirstModalVisible}
            />
          </View>
        </ScrollView>
      )}

      {/* Bottom Total and Pay Section - Fixed at bottom */}
      {!showEmptyCart && cartItems.length > 0 && (
        <View style={styles.bottomTotalSection}>
          <View style={styles.totalLeft}>
            <Text style={styles.totalLabel}>
              Total: ₹{paymentBreakdown?.final_amount?.toFixed(0) || "37,50"}
            </Text>
            <Text style={styles.itemsCount}>
              {cartItems.reduce((acc, item) => acc + item.quantity, 0)} items
            </Text>
          </View>
          <TouchableOpacity style={styles.payButton}>
            <Text style={styles.payText}>Pay</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Allergens Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={allergensModalVisible}
        onRequestClose={handleAllergensClose}
      >
        <View style={styles.allergensOverlay}>
          <View style={styles.allergensModalContainer}>
            <TouchableOpacity
              style={styles.allergensCloseButton}
              onPress={handleAllergensClose}
            >
              <Text style={styles.allergensCloseButtonText}>×</Text>
            </TouchableOpacity>

            <Text style={styles.allergensTitle}>
              Let us know what are your{"\n"}allergens & dislikes
            </Text>

            <View style={styles.allergensGrid}>
              {allergens.map((allergen) => renderAllergenItem(allergen))}
            </View>

            <TouchableOpacity
              style={styles.allergensApplyButton}
              onPress={handleAllergensApply}
            >
              <Text style={styles.allergensApplyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    position: "relative",
  },
  conatinerRoww: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  flatListConatiner: {
    backgroundColor: "#FFF",
    padding: 10,
  },
  secondSection: {
    paddingLeft: 20,
    paddingRight: 20,
    backgroundColor: "#FFFFFF",
  },
  PromoCodeConatiner: {
    backgroundColor: "#FBFBFBBA",
    padding: 16,
    paddingLeft: 20,
    borderTopLeftRadius: 11,
    borderTopRightRadius: 11,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingRight: 20,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  promoInnerConatiner: {
    flexDirection: "row",
    gap: 6,
    alignItems: "center",
  },
  totalPriceInnerConatinner: {
    flexDirection: "row",
    gap: 1,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  headingtext: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    color: "#5F22D9",
    marginRight: 35,
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FBFBFBF0",
    marginVertical: 4,
    padding: 16,
    borderTopLeftRadius: 11,
    borderTopRightRadius: 11,
    borderBottomLeftRadius: 11,
    borderBottomRightRadius: 11,
    opacity: 0.87,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 10, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
    width: "95%",
    paddingHorizontal: 10,
    marginHorizontal: 10,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 40,
    resizeMode: "cover",
    overflow: "hidden",
  },
  infoContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 5,
    paddingRight: 32,
    marginLeft: 10,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: "#000000",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#8C8A9D",
    marginTop: -8,
    marginBottom: 5,
    textTransform: "lowercase",
  },
  price: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9",
    marginTop: -8,
  },
  quantityContainer: {
    position: "absolute",
    right: 0,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 34,
  },
  quantityButton: {
    width: 21,
    height: 21,
    borderRadius: 16,
    borderColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFFFFF",
  },
  IncreaseQuantityButton: {
    width: 21,
    height: 21,
    borderRadius: 16,
    borderColor: "#5F22D9",
    backgroundColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  quantityButtonText: {
    fontSize: 16,
    color: "#5F22D9",
    lineHeight: 15,
    textAlign: "center",
  },

  IncreaseQuantityButtonText: {
    fontSize: 16,
    color: "#FFF",
    lineHeight: 15,
    textAlign: "center",
  },

  quantity: {
    fontFamily: "Poppins_600SemiBold",
    marginHorizontal: 12,
    fontSize: 16,
    color: "#000",
    minWidth: 24,
    textAlign: "center",
  },
  removeButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  removeButtonText: {
    fontSize: 16,
    color: "#5F22D9",
    fontWeight: "bold",
    lineHeight: 16,
  },
  container1: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    borderRadius: 28,
    paddingHorizontal: 10,
    paddingVertical: 7,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    width: "100%",
    alignSelf: "center",
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: "#000",
    marginRight: 10,
    height: "46px",
  },
  promoInnerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    maxWidth: "40%",
    flexShrink: 1,
  },
  promoImage: {
    width: 25,
    height: 25,
    resizeMode: "contain",
  },
  copyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: 20,
  },
  copyText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  billCard: {
    backgroundColor: "#FBFBFBF0",
    marginVertical: 8,
    padding: 16,
    borderTopLeftRadius: 11,
    borderTopRightRadius: 11,
    borderBottomLeftRadius: 11,
    borderBottomRightRadius: 11,
    opacity: 0.87,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  header: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#171725",
    marginBottom: 10,
  },
  rowContainer: {
    marginBottom: 16,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },
  totalSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },
  totalLeft: {
    flex: 1,
  },
  label: {
    fontSize: 16,
  },
  boldText: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
  },
  purpleText: {
    fontSize: 16,
    color: "#5F22D9",
  },
  total: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  strikeThrough: {
    fontSize: 14,
    color: "#A0A0A0",
    textDecorationLine: "line-through",
    marginRight: 8,
  },
  finalAmount: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#5F22D9",
  },
  paymentContainer: {
    marginTop: 20,
  },
  paymentMethod: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  cardDetails: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  cardNumber: {
    fontSize: 16,
    color: "#666",
    flex: 1,
    marginLeft: 10,
  },
  changeButton: {
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 17,
    borderColor: "#EDEFFF",
    borderWidth: 2,
  },
  changeText: {
    color: "#5F22D9",
    fontWeight: "bold",
    fontSize: 16,
  },
  totalContainer: {
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  totalTextPrice: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  itemsText: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#898EBC",
  },
  payButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 16,
    paddingHorizontal: 40,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 170,
  },
  payText: {
    color: "#FFF",
    fontSize: 18,
    fontWeight: "bold",
  },
  allergensContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  allergensContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  allergensIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FFFFFF",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  allergensIconText: {
    fontSize: 20,
  },
  dislikeIcon: {
    width: 24,
    height: 24,
    resizeMode: "contain",
  },
  allergensText: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#8E8E8E",
  },
  selectButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  selectButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  totalLabel: {
    fontSize: 20,
    fontFamily: "Poppins_600SemiBold",
    color: "#171725",
    marginBottom: 4,
  },
  itemsCount: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#898EBC",
  },
  disabledButton: {
    opacity: 0.5,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 10,
    backgroundColor: "#F5F5F5",
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#5F22D9",
    fontFamily: "Poppins_400Regular",
  },
  allergensInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    borderRadius: 100,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    height: "46px",
  },
  allergensInputContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  allergensInputText: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#B0B0B0",
  },
  selectInputButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: 20,
  },
  selectInputButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  bottomTotalSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: "#FBFBFBC2",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  // Allergens Modal Styles
  allergensOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  allergensModalContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 20,
    padding: 24,
    width: width * 0.9,
    maxWidth: 400,
    position: "relative",
  },
  allergensCloseButton: {
    position: "absolute",
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  allergensCloseButtonText: {
    fontSize: 26,
    color: "#666",
    lineHeight: 20,
  },
  allergensTitle: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#8E8E8E",
    textAlign: "center",
    marginBottom: 32,
    marginTop: 16,
    lineHeight: 24,
  },
  allergensGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  allergenItem: {
    width: "30%",
    aspectRatio: 1,
    backgroundColor: "#F8F9FA",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedAllergenItem: {
    backgroundColor: "#5F22D9",
  },
  allergenIconContainer: {
    marginBottom: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  allergenIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  allergenName: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#333",
    textAlign: "center",
  },
  selectedAllergenName: {
    color: "#FFFFFF",
  },
  allergensApplyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 16,
    borderRadius: 32,
    alignItems: "center",
    marginTop: -60,
  },
  allergensApplyButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
  },
});

export default Cart;
