{"info": {"_postman_id": "6aa88ebb-5f6a-4a15-955f-efda506ccf68", "name": "Razorpay APIs", "description": "Razorpay is an Indian payments solution provider that allows businesses to accept, process and disburse payments with its product suite. Razorpay APIs are completely RESTful and all our responses are returned in JSON.\n\n\n# API Authentication\n\nAll Razorpay APIs are authenticated using **Basic Auth**. Basic auth requires the following:\n\n- [YOUR_KEY_ID]\n- [YOUR_KEY_SECRET]\n\nBasic auth expects an Authorization header for each request in the Basic base64token format. Here, base64token is a base64 encoded string of YOUR_KEY_ID:YOUR_KEY_SECRET.\n\n<table>\n<b>Watch Out!</b> <br>\nThe Authorization header value should strictly adhere to the format mentioned above. Invalid formats will result in authentication failures. Few examples of invalid headers are:\n\n- BASIC base64token\n- basic base64token\n- Basic \"base64token\"\n- Basic $base64token\n</table>\n\n# Generate API Key\n\nYou can use Razorpay APIs in two modes, Test and Live. The API key is different for each mode.\n\nTo generate the API keys:\n1. Log into the <a href=\"https://dashboard.razorpay.com/app/dashboard\" target=\"_blank\">Razorpay Dashboard</a>.\n2. Select the mode (Test or Live) for which you want to generate the API key.\n<br>- Test Mode: The test mode is a simulation mode that you can use to test your integration flow. Your customers will not be able to make payments in this mode.\n<br>- Live Mode: When your integration is complete, in the Dashboard, switch to the live mode and generate live mode API keys. Replace test mode keys with live mode keys in the integration to accept payments from customers.\n3. Navigate to Settings → API Keys → Generate Key to generate key for the selected mode.\n\n# Errors\nAll successful responses are returned with HTTP Status code 204. In case of failure, API returns a JSON error response with the parameters that contain the failure reason.\n\n# Understanding Error Response\n\nThe error response contains `code`, `description`, `field`, `source`, `step`, and `reason` parameters to understand and troubleshoot the error.\n\nLet us take an example where a merchant tries to add new allowed payer accounts when the overall limit is exceeded.\n\n```json: Sample Error Response\n{\n  \"error\": {\n    \"code\": \"BAD_REQUEST_ERROR\",\n    \"description\": \"Authentication failed due to incorrect otp\",\n    \"field\": null,\n    \"source\": \"customer\",\n    \"step\": \"payment_authentication\",\n    \"reason\": \"invalid_otp\",\n    \"metadata\": {\n      \"payment_id\": \"pay_EDNBKIP31Y4jl8\",\n      \"order_id\": \"order_DBJKIP31Y4jl8\"\n    }\n  }\n}\n```\n\n### Response Parameters\n\n`error`\n: `object` The error object.\n\n`code`\n: `string` Type of the error.\n\n`description`\n: `string` Description of the error.\n\n`field`\n: `string` Name of the parameter in the API request that caused the error.\n\n`source`\n: `string` The point of failure in the specific operation (payment in this case). For example, customer, business\n\n`step`\n: `string` The stage where the transaction failure occurred. The stages can vary depending on the payment method used to complete the transaction.\n\n`reason`\n: `string` The exact error reason. It can be handled programmatically.\n\n`metadata`\n: `object` Contains additional information about the request.\n\n    `payment_id`\n    : `string` Unique identifier of the payment.\n\n    `order_id`\n    : `string` Unique identifier of the order associated with the payment.\n\nKnow more about <a href=\"/docs/errors/error-codes\" target=\"_blank\">Error Codes</a>.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "46874862", "_collection_link": "https://plenti-3436.postman.co/workspace/Plenti~a41a2b4c-632a-4017-a550-382c4851d8a9/collection/6617652-6aa88ebb-5f6a-4a15-955f-efda506ccf68?action=share&source=collection_link&creator=46874862"}, "item": [{"name": "Customers APIs", "item": [{"name": "Create a Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\n  \"email\":\"<EMAIL>\",\n  \"contact\":\"*********0\",\n  \"fail_existing\":\"1\",\n  \"gstin\":\"12ABCDE2356F7GH\",\n  \"notes\":{\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/customers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "customers"]}, "description": "Create a customer using the Create a Customer API. You can add your customer's name, email and contact number. In the response, you will receive a customer_id.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/customers/#create-a-customer\" target=\"_blank\">Create a Customer API</a>."}, "response": []}, {"name": "Fetch all Customers", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/customers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "customers"]}, "description": "Retrieve details of all customers using the Fetch all Customers API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/customers/#fetch-all-customers\" target=\"_blank\">Fetch all Customers API</a>."}, "response": []}, {"name": "Fetch Customer by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/customers/{cust_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "customers", "{cust_id}"]}, "description": "Retrieve details of a specific customer using the Fetch a Customer by ID API. You can send the customer_id as a path parameter to retrieve the customer details.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/customers/#fetch-customer-by-id\" target=\"_blank\">Fetch a Customer by ID API</a>."}, "response": []}, {"name": "Edit a Customer", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"contact\": \"**********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/customers/{cust_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "customers", "{cust_id}"]}, "description": "Update details of a specific customer using the Edit a Customer API. You can send the customer_id as a path parameter to access and edit the customer details.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/customers/#edit-customer-details\" target=\"_blank\">Edit a Customer API</a>."}, "response": []}], "description": "You can create customers with basic details such as name and contact details and use them for various Razorpay solution offerings.\n\n## List of APIs:\n- <a href=\"https://razorpay.com/docs/api/customers/#create-a-customer\" target=\"_blank\">Create a Customer</a>\n- <a href=\"https://razorpay.com/docs/api/customers/#edit-customer-details\" target=\"_blank\">Edit Customer Details</a>\n- <a href=\"https://razorpay.com/docs/api/customers/#fetch-all-customers\" target=\"_blank\">Fetch all Customers</a>\n- <a href=\"https://razorpay.com/docs/api/customers/#fetch-customer-by-id\" target=\"_blank\">Fetch Customer by ID</a>"}, {"name": "Orders APIs", "item": [{"name": "Create an Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000000,\n  \"currency\": \"INR\",\n  \"receipt\": \"Receipt no. 1\",\n  \"notes\": {\n    \"notes_key_1\": \"<PERSON>, <PERSON>, Hot\",\n    \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/orders", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders"]}, "description": "Create an order using this API. Amount and currency are mandatory parameters.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/orders/#create-an-order\" target=\"_blank\">Create an Order API</a>."}, "response": []}, {"name": "Fetch Orders", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/orders", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders"]}, "description": "Fetch all orders created on your account using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/orders/#fetch-orders\" target=\"_blank\">Fetch all Orders API</a>."}, "response": []}, {"name": "Fetch Orders (With Expanded Payments)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/orders?expand[]=payments", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders"], "query": [{"key": "expand[]", "value": "payments"}]}, "description": "Fetch all orders created on your account using this API.\n\nKnow more about the [Fetch all Orders (With Expanded Payments) API](https://razorpay.com/docs/api/orders/fetch-all-expanded-payments/)."}, "response": []}, {"name": "Fetch Orders (With Expanded Card Payments)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/orders?expand[]=payments.card", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders"], "query": [{"key": "expand[]", "value": "payments.card"}]}, "description": "Fetch all orders created on your account using this API.\n\nKnow more about the [Fetch all Orders (With Expanded Card Payments) API](https://razorpay.com/docs/api/orders/fetch-all-expanded-card-payments)."}, "response": []}, {"name": "Fetch Orders by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/orders/{order_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders", "{order_id}"]}, "description": "Fetch a particular order with the order_id using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/orders/#fetch-an-order-with-id\" target=\"_blank\">Fetch Orders by ID API</a>."}, "response": []}, {"name": "Fetch Payments by Order", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/orders/{order_id}/payments", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders", "{order_id}", "payments"]}, "description": "Fetch payments made for a particular order using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/orders/#fetch-an-order-with-id\" target=\"_blank\">Fetch Payments For an Orders API</a>."}, "response": []}, {"name": "Update Order", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"notes\": {\n    \"notes_key_1\": \"Beam me up <PERSON><PERSON>\",\n    \"notes_key_2\": \"Engage\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/orders/{order_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders", "{order_id}"]}, "description": "Update a particular order using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/orders/#update-order\" target=\"_blank\">Update an Order API</a>."}, "response": []}], "description": "Order is an important step in the payment process. Orders and payments go hand-in-hand. For every payment, an order needs to be created. You can create orders using the Orders API.\n\n## List of APIs:\n- <a href=\"https://razorpay.com/docs/api/orders/#create-an-order\" target=\"_blank\">Create an Order</a>\n- <a href=\"https://razorpay.com/docs/api/orders/#fetch-orders\" target=\"_blank\">Fetch Orders</a>\n- <a href=\"https://razorpay.com/docs/api/orders/#fetch-an-order-with-id\" target=\"_blank\">Fetch an Order With Id</a>\n- <a href=\"https://razorpay.com/docs/api/orders/#fetch-payments-for-an-order\" target=\"_blank\">Fetch Payments for an Order</a>\n- <a href=\"https://razorpay.com/docs/api/orders/#update-order\" target=\"_blank\">Update Order</a>"}, {"name": "Payments APIs", "item": [{"name": "Payment Downtime API", "item": [{"name": "Fetch Payment Downtime Details", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/downtimes", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "downtimes"]}, "description": "Fetch details of all payment downtimes using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/downtime/#fetch-payment-downtime-details\" target=\"_blank\">Fetch Payment Downtime Details API</a>"}, "response": []}, {"name": "Fetch Payment Downtime Details by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/downtimes/down_F1cxDoHWD4fkQt", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "downtimes", "down_F1cxDoHWD4fkQt"]}, "description": "Fetch details of particular payment downtime using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/downtime/#fetch-payment-downtime-details-by-id\" target=\"_blank\">Fetch Payment Downtime Details by ID API</a>"}, "response": []}], "description": "Usually, downtime webhook payloads are delivered within few seconds of the event. However, in some cases, this can be delayed by few minutes due to various reasons.\n\nIf you have not received any webhook notifications due to some technical issues, you can use the Fetch Payment Downtime API to fetch the downtime status.\n\nList of APIs:\n- <a href=\"https://razorpay.com/docs/api/payments/downtime/#fetch-payment-downtime-details\" target=\"_blank\">Fetch Payment Downtime Details</a>\n- <a href=\"https://razorpay.com/docs/api/payments/downtime/#fetch-payment-downtime-details-by-id\" target=\"_blank\">Fetch Payment Downtime Details by ID</a>"}, {"name": "Fetch all Payments", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", ""]}, "description": "Fetch all payments made to your account using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/#fetch-multiple-payments\" target=\"_blank\">Fetch all Payments API</a>."}, "response": []}, {"name": "Fetch Payment by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}"]}, "description": "Fetch the details of a particular payment using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/#fetch-a-payment\" target=\"_blank\">Fetch a Payment by ID API</a>."}, "response": []}, {"name": "Fetch Payment based on Order", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/orders/{order_id}/payments", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders", "{order_id}", "payments"]}, "description": "Fetch the details of payments made to a particular order using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/#fetch-payments-based-on-orders\" target=\"_blank\">Fetch a Payment by ID API</a>."}, "response": []}, {"name": "Fetch Card Details for a Payment", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/card", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "card"]}, "description": "Fetch the details of the card used to make a payment using the API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/#fetch-card-details-of-a-payment\" target=\"_blank\">Fetch Card Details of a Payment API</a>."}, "response": []}, {"name": "Update Payment", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"note\": {\n\t\t\"notes_key_1\": \"Beam me up <PERSON><PERSON>\",\n\t\t\"notes_key_2\": \"Engage\"\n\t}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}"]}, "description": "Update the details of a specific payment using the API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/#update-the-payment\" target=\"_blank\">Update a Payment API</a>."}, "response": []}, {"name": "Capture a Payment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/capture", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "capture"]}, "description": "Change the status of payment from `authorized` to `captured` using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/#capture-a-payment\" target=\"_blank\">Capture a Payment API</a>."}, "response": []}, {"name": "Fetch Expanded Card or EMI Details for Payments", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/?expand[]=card", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", ""], "query": [{"key": "expand[]", "value": "card"}]}, "description": "Fetch the expanded details of the card used to make a payment using the API. \n\nYou can also fetch expanded details for an EMI. In this case, the URL is:\n`https://api.razorpay.com/v1/payments/?expand[]=emi`\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/payments/#fetch-expanded-card-or-emi-details-for-payments\" target=\"_blank\">Fetch Expanded Card or EMI Details for Payments API</a>."}, "response": []}, {"name": "Fetch a Payment (With Expanded Card Details)", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/:id/?expand[]=card", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", ":id", ""], "query": [{"key": "expand[]", "value": "card"}], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "Fetch a Payment (With Expanded EMI Details)", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/:id/?expand[]=emi", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", ":id", ""], "query": [{"key": "expand[]", "value": "emi"}], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "Fetch a Payment (With Expanded Offers Details)", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/:id/?expand[]=offers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", ":id", ""], "query": [{"key": "expand[]", "value": "offers"}], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "Fetch a Payment (With Expanded UPI Details)", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/:id/?expand[]=upi", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", ":id", ""], "query": [{"key": "expand[]", "value": "upi"}], "variable": [{"key": "id", "value": ""}]}}, "response": []}], "description": "You can create payments using the Razorpay Standard Checkout. https://razorpay.com/docs/payment-gateway/.\n\nThe Payments APIs help you to capture payments and fetch them only.\n\n## List of APIs\n- <a href=\"https://razorpay.com/docs/api/payments/#capture-a-payment\" target=\"_blank\">Capture a Payment</a>\n- <a href=\"https://razorpay.com/docs/api/payments/#fetch-a-payment\" target=\"_blank\">Fetch a Payment</a>\n- <a href=\"https://razorpay.com/docs/api/payments/#fetch-multiple-payments\" target=\"_blank\">Fetch Multiple Payments</a>\n- <a href=\"https://razorpay.com/docs/api/payments/#fetch-expanded-card-or-emi-details-for-payments\" target=\"_blank\">Fetch Expanded Card or EMI Details for Payments</a>\n- <a href=\"https://razorpay.com/docs/api/payments/#fetch-payments-based-on-orders\" target=\"_blank\">Fetch Payments Based on Orders</a>\n- <a href=\"https://razorpay.com/docs/api/payments/#fetch-card-details-of-a-payment\" target=\"_blank\">Fetch Card Details of a Payment</a>\n- <a href=\"https://razorpay.com/docs/api/payments/#update-the-payment\" target=\"_blank\">Update the Payment</a>"}, {"name": "Refunds APIs", "item": [{"name": "Create a Normal Refund", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 10000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/refund", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "refund"]}, "description": "Create a normal refund using this API. You can refund the entire amount or a partial amount. For example, for payment of INR100, you can refund the entire INR100 or INR50. \n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#create-a-normal-refund\" target=\"_blank\">Create a Normal Refund API.</a>"}, "response": []}, {"name": "Create an Instant Refund", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 20000,\n  \"receipt\": \"Receipt No. 2\",\n  \"notes\": {\n    \"notes_key_1\":\"<PERSON>, <PERSON>, Hot\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/refund", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "refund"]}, "description": "Create an instant refund using this API. You can refund the entire amount or a partial amount. For example, for payment of INR100, you can refund the entire INR100 or INR50. \n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#create-an-instant-refund\" target=\"_blank\">Create an Instant Refund API.</a>"}, "response": []}, {"name": "Fetch Refunds", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/refunds", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "refunds"]}, "description": "Fetch all refunds created by you using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#fetch-all-refunds\" target=\"_blank\">Fetch all Refunds API.</a>"}, "response": []}, {"name": "Fetch Refund by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/refunds/{rfnd_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "refunds", "{rfnd_id}"]}, "description": "Fetch a specific refund created by you using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#fetch-refund-by-id\" target=\"_blank\">Fetch a Refund API.</a>"}, "response": []}, {"name": "Fetch a Specific Refund for a Payment", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/refunds/{rfnd_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "refunds", "{rfnd_id}"]}, "description": "Fetch a specific refund for a specific payment using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#fetch-a-specific-refund-for-a-payment\" target=\"_blank\">Fetch a Specific Refund for a Specific Payment API.</a>"}, "response": []}, {"name": "Fetch All Refunds for a Payment", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/refunds", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "refunds"]}, "description": "Fetch multiple refunds for a specific payment using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#fetch-multiple-refunds-for-a-payment\" target=\"_blank\">Fetch all Refunds for a Payment API</a>."}, "response": []}, {"name": "Update Refund", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"notes\": {\n    \"notes_key_1\":\"Beam me up <PERSON><PERSON>.\",\n    \"notes_key_2\":\"Engage\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/refunds/{rfnd_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "refunds", "{rfnd_id}"]}, "description": "Update a specific refund using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#update-refund\" target=\"_blank\">Update a Refund API</a>."}, "response": []}], "description": "You can make full or partial refunds to customers. While issuing refunds, you can choose to process the refunds instantly or at normal speed (within 5-7 working days). Razorpay provides you real-time tracking of the processing speed and the status of the initiated refund.\n\n<table>\n<b>Refunds Can be Made Only on Captured Payments</b> <br>\nYou can initiate refunds only on those payments that are in captured state. A payment in authorized state is auto-refunded if not captured within 5 days of creation.\n</table>\n\n## List of APIs\n- <a href=\"https://razorpay.com/docs/api/refunds/#create-a-normal-refund\" target=\"_blank\">Create a Normal Refund</a>\n- <a href=\"https://razorpay.com/docs/api/refunds/#create-an-instant-refund\" target=\"_blank\">Create an Instant Refund</a>\n- <a href=\"https://razorpay.com/docs/api/refunds/#fetch-multiple-refunds-for-a-payment\" target=\"_blank\">Fetch Multiple Refunds for a Payment</a>\n- <a href=\"https://razorpay.com/docs/api/refunds/#fetch-a-specific-refund-for-a-payment\" target=\"_blank\">Fetch a Specific Refund for a Payment</a>\n- <a href=\"https://razorpay.com/docs/api/refunds/#fetch-all-refunds\" target=\"_blank\">Fetch All Refunds</a>\n- <a href=\"https://razorpay.com/docs/api/refunds/#fetch-refund-by-id\" target=\"_blank\">Fetch Refund by ID</a>\n- <a href=\"https://razorpay.com/docs/api/refunds/#update-refund\" target=\"_blank\">Update Refund</a>"}, {"name": "Settlements APIs", "item": [{"name": "Instant Settlements", "item": [{"name": "Create Instant Settlement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 50000,\n  \"max_balance\": 0,\n  \"description\": \"Need this to make vendor payouts\",\n  \"notes\": {\n    \"notes_key_1\": \"<PERSON>, <PERSON>, Hot\",\n    \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/settlements/ondemand", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements", "ondemand"]}, "description": "Create an on-demand settlement using this API. Here, `amount` is a mandatory parameter.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/settlements/#create-an-on-demand-settlement\" target=\"_blank\">Create an On-demand Settlement API</a>."}, "response": []}, {"name": "Fetch all Instant Settlements", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/settlements/ondemand", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements", "ondemand"]}, "description": "Fetch all on-demand settlements created by you using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/settlements/#fetch-all-on-demand-settlements\" target=\"_blank\">Fetch all On-demand Settlements API</a>."}, "response": []}, {"name": "Fetch Instant Settlement by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/settlements/ondemand/{sod_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements", "ondemand", "{sod_id}"]}, "description": "Fetch a specific on-demand settlement created by you using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/settlements/#fetch-on-demand-settlements-by-id\" target=\"_blank\">Fetch On-demand Settlements by ID API</a>."}, "response": []}, {"name": "Fetch All Instant Settlements With Payout Details", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/settlements/ondemand?expand[]=ondemand_payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements", "ondemand"], "query": [{"key": "expand[]", "value": "ondemand_payouts"}]}}, "response": []}, {"name": "Fetch Instant Settlement by ID With Payout Details", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/settlements/ondemand/setlod_FNj7g2YS5J67Rz?expand[]=ondemand_payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements", "ondemand", "setlod_FNj7g2YS5J67Rz"], "query": [{"key": "expand[]", "value": "ondemand_payouts"}]}}, "response": []}], "description": "Razorpay On-demand Settlements helps you reduce your settlement period from T+2 days (default settlement cycle) to a few minutes (from the time of the transaction), thus enabling your business to avoid cash-flow challenges and prepare better for working capital requirements.\n\nList of APIs:\n- <a href=\"https://razorpay.com/docs/api/settlements/#create-an-on-demand-settlement\" target=\"_blank\">Create an On-demand Settlement</a>\n- <a href=\"https://razorpay.com/docs/api/settlements/#fetch-all-on-demand-settlements\" target=\"_blank\">Fetch All On-demand Settlements</a>\n- <a href=\"https://razorpay.com/docs/api/settlements/#fetch-on-demand-settlements-by-id\" target=\"_blank\">Fetch On-demand Settlements by ID</a>"}, {"name": "Settlements Recon", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/settlements/recon/combined?year=2019&month=09", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements", "recon", "combined"], "query": [{"key": "year", "value": "2019"}, {"key": "month", "value": "09"}]}, "description": "Fetch a list of all transactions such as payments, refunds, transfers and adjustments that have been settled to you for a particular day or month, using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/settlements/#settlement-recon\" target=\"_blank\">Settlement Recon API</a>."}, "response": []}, {"name": "Fetch Settlement by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/settlements/{setl_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements", "{setl_id}"]}, "description": "Fetch details of a particular settlement using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/settlements/#fetch-settlement-using-id\" target=\"_blank\">Fetch Settlement by ID API</a>."}, "response": []}, {"name": "Fetch All Settlements", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/settlements", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "settlements"]}, "description": "Fetch details of all settlements using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/settlements/#fetch-all-settlements\" target=\"_blank\">Fetch All Settlements API</a>."}, "response": []}], "description": "Settlement is the process in which the money received from your customers is settled to your bank account. Settlements are made to the bank account, the details of which you submitted to us as a part of KYC verification.\n\nSettlements for all payments are done in INR (Indian Rupees), irrespective of the currency in which the payment was made. After <PERSON><PERSON><PERSON><PERSON> receives the amount, it is settled to your bank account, after fees deduction. Know more about settlements.\n\n## Settlement Cycle\nBy default, the complete process takes a time of T+2 business days for domestic transactions and T+7 days for international transactions, T being the date of capture of payment. This is called the settlement cycle. The settlement cycle is subject to bank approval and can vary based on your business vertical, risk factors and so on.\n\n## List of APIs\n- <a href=\"https://razorpay.com/docs/api/settlements/#fetch-all-settlements\" target=\"_blank\">Fetch All Settlements</a>\n- <a href=\"https://razorpay.com/docs/api/settlements/#fetch-settlement-using-id\" target=\"_blank\">Fetch Settlement Using ID</a>\n- <a href=\"https://razorpay.com/docs/api/settlements/#settlement-recon\" target=\"_blank\">Settlement Recon</a>"}, {"name": "Disputes APIs", "item": [{"name": "Documents", "item": [{"name": "Create a Document", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/documents", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "documents"]}}, "response": []}, {"name": "Fetch Document Information", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/documents/:id", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "documents", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "Fetch Document Content", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/documents/:id/content", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "documents", ":id", "content"], "variable": [{"key": "id", "value": ""}]}}, "response": []}], "description": "Use the Documents APIs to securely upload and share documents with Razorpay.\n\n## List of APIs\n\n- [Create a Document](https://razorpay.com/docs/api/documents/create/)\n- [Fetch Document Information](https://razorpay.com/docs/api/documents/fetch-info)\n- [Fetch Document Content](https://razorpay.com/docs/api/documents/fetch-content)"}, {"name": "Fetch All Disputes", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/disputes", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "disputes"]}, "description": "Fetch the list of all disputes using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/disputes/#fetch-all-disputes\" target=\"_blank\">Fetch All Disputes API</a>."}, "response": []}, {"name": "Fetch a Dispute by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/disputes", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "disputes"]}, "description": "Fetch the details of a particular dispute using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/disputes/#fetch-all-disputes\" target=\"_blank\">Fetch All Disputes API</a>."}, "response": []}, {"name": "Accept a Dispute", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/disputes/:id/accept", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "disputes", ":id", "accept"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "Contest a Dispute", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"amount\": 5000,\n    \"summary\": \"goods delivered\",\n    \"shipping_proof\": [\n        \"doc_EFtmUsbwpXwBH9\",\n        \"doc_EFtmUsbwpXwBH8\"\n    ],\n    \"others\": [\n        {\n            \"type\": \"receipt_signed_by_customer\",\n            \"document_ids\": [\n                \"doc_EFtmUsbwpXwBH1\",\n                \"doc_EFtmUsbwpXwBH7\"\n            ]\n        }\n    ],\n    \"action\": \"draft\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/disputes/:id/contest/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "disputes", ":id", "contest", ""], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "Fetch a Dispute (With Expanded Payments Details)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://dashboard.dev.razorpay.in/app/disputes/disp_K8bVLppJ8zp5Wp?expand[]=payment", "protocol": "https", "host": ["dashboard", "dev", "razorpay", "in"], "path": ["app", "disputes", "disp_K8bVLppJ8zp5Wp"], "query": [{"key": "expand[]", "value": "payment"}]}}, "response": []}, {"name": "Fetch a Dispute (With Expanded transaction.settlement Details)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://dashboard.dev.razorpay.in/app/disputes/disp_K8bVLppJ8zp5Wp?expand[]=transaction.settlement", "protocol": "https", "host": ["dashboard", "dev", "razorpay", "in"], "path": ["app", "disputes", "disp_K8bVLppJ8zp5Wp"], "query": [{"key": "expand[]", "value": "transaction.settlement"}]}}, "response": []}], "description": "A dispute arises when your customer or the issuing bank questions the validity of a payment. It can be raised due to reasons such as unauthorised charges, failure to deliver promised merchandise or excessive charges levied by you. The chargeback raised by your customer can be filed as a dispute with Razorpay.\n\nManage customer disputes using the Razorpay Disputes APIs.\n\n## List of APIs\n\n*   <a href=\"https://razorpay.com/docs/api/disputes/#fetch-all-disputes\">Fetch all Disputes</a>\n*   <a href=\"https://razorpay.com/docs/api/disputes/#fetch-a-dispute\">Fetch a Dispute by ID</a>"}, {"name": "Invoices APIs", "item": [{"name": "Create an Invoice with Customer Id", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"invoice\",\n  \"date\": **********,\n  \"customer_id\": \"cust_HOQzpsovChhcpl\",\n  \"line_items\": [\n    {\n      \"item_id\": \"item_K6g5L6X43dXjEA\"\n    }\n  ]\n}"}, "url": {"raw": "https://api.razorpay.com/v1/invoices", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices"], "query": [{"key": "count", "value": "10", "disabled": true}, {"key": "skip", "value": "10", "disabled": true}]}, "description": "Create an Invoice using the Create an Invoice API.\n\nSome points to note. You can:\n- Create a blank invoice (with no details at all) in draft state.\n- Update it with necessary information.\n- Issue it at a later time with the Issue Invoice API. Only after the invoice is issued, you will get a short URL. Also, only after the invoice is issued, it can be sent to customers and the corresponding payment can be made against it.\n- Use <a href=\"https://razorpay.com/docs/api/items/\" target=\"_blank\">Items APIs</a> to create items which you can later use as a template to create line items in an invoice.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#create-an-invoice\" target=\"_blank\">Create an Invoice API</a>."}, "response": []}, {"name": "Create an Invoice with Customer Details", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"type\": \"invoice\",\n  \"description\": \"Invoice for the month of January 2020\",\n  \"partial_payment\": true,\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": **********,\n    \"email\": \"<EMAIL>\",\n    \"billing_address\": {\n      \"line1\": \"Ground & 1st Floor, SJR Cyber Laskar\",\n      \"line2\": \"Hosur Road\",\n      \"zipcode\": \"560068\",\n      \"city\": \"Bengaluru\",\n      \"state\": \"Karnataka\",\n      \"country\": \"in\"\n    },\n    \"shipping_address\": {\n      \"line1\": \"Ground & 1st Floor, SJR Cyber Laskar\",\n      \"line2\": \"Hosur Road\",\n      \"zipcode\": \"560068\",\n      \"city\": \"Bengaluru\",\n      \"state\": \"Karnataka\",\n      \"country\": \"in\"\n    }\n  },\n  \"line_items\": [\n    {\n      \"name\": \"Master Cloud Computing in 30 Days\",\n      \"description\": \"Book by Ravena Ravenclaw\",\n      \"amount\": 399,\n      \"currency\": \"USD\",\n      \"quantity\": 1\n    }\n  ],\n  \"sms_notify\": 1,\n  \"email_notify\": 1,\n  \"currency\": \"USD\",\n  \"expire_by\": 1589765167\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/invoices", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices"]}}, "response": []}, {"name": "Fetch Invoices", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/invoices?type=invoice", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices"], "query": [{"key": "type", "value": "invoice"}]}, "description": "Fetch all invoices created by you using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#fetch-multiple-invoices\" target=\"_blank\">Fetch all Invoices API</a>."}, "response": []}, {"name": "Fetch Invoice by Id", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/invoices/{inv_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices", "{inv_id}"]}, "description": "Fetch an invoice created by you using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#fetch-an-invoice\" target=\"_blank\">Fetch an Invoice API</a>."}, "response": []}, {"name": "Update an Invoice", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"partial_payment\": true,\n    \"customer\": {\n        \"name\": \"<PERSON><PERSON><PERSON>\",\n        \"contact\": \"**********\",\n        \"email\": \"<EMAIL>\"\n    },\n    \"line_items\": [\n        {\n            \"name\": \"Crate of Red Herbs\",\n            \"description\": \"Red Herbs from Resident Evil\",\n            \"amount\": 50000,\n            \"currency\": \"INR\",\n            \"quantity\": 5\n        },\n        {\n            \"item_id\": \"{item_id}\"\n        }\n    ],\n    \"sms_notify\": 1,\n    \"email_notify\": 1,\n    \"draft\": \"1\",\n    \"date\": 1588076279,\n    \"expire_by\": 1924991999,\n    \"receipt\": \"Receipt No. 1\",\n    \"comment\": \"Fresh herbs mowed this morning\",\n    \"terms\": \"No Returns; No Refunds\",\n    \"notes\": {\n        \"notes_key_1\": \"Beam me up <PERSON><PERSON>.\",\n        \"notes_key_2\": \"Engage\"\n    }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/invoices/{inv_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices", "{inv_id}"], "query": [{"key": "count", "value": "10", "disabled": true}, {"key": "skip", "value": "10", "disabled": true}]}, "description": "Update an invoice using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#update-an-invoice\" target=\"_blank\">Update an Invoice API</a>."}, "response": []}, {"name": "Issue an Invoice", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/invoices/{inv_id}/issue", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices", "{inv_id}", "issue"]}, "description": "Issue an invoice using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#issue-an-invoice\" target=\"_blank\">Issue an Invoice API</a>."}, "response": []}, {"name": "Send or Resend Notification", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/invoices/{inv_id}/notify_by/{medium}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices", "{inv_id}", "notify_by", "{medium}"]}, "description": "Send or resend an invoice notification using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#send-notifications\" target=\"_blank\">Send/Resend a Notification API</a>."}, "response": []}, {"name": "Cancel an Invoice", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/invoices/{inv_id}/cancel", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices", "{inv_id}", "cancel"]}, "description": "Cancel an invoice using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#cancel-an-invoice\" target=\"_blank\">Cancel an Invoice API</a>."}, "response": []}, {"name": "Delete an Invoice", "request": {"method": "DELETE", "header": [], "url": {"raw": "https://api.razorpay.com/v1/invoices/{inv_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "invoices", "{inv_id}"]}, "description": "Delete an invoice using this API.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/invoices/#delete-an-invoice\" target=\"_blank\">Delete an Invoice API</a>."}, "response": []}], "description": "Razorpay Invoices enables you to send invoices to your customers and accept payments instantly.\n\nThe invoice contains information regarding the sale such as the name of the ordered products or services, quantity, billing cycle, price breakup, receipt number and customer information.\n\nYou can create, update, cancel and delete invoices using our Invoices APIs.\n\n## List of APIs\n\n- <a href=\"https://razorpay.com/docs/api/invoices/#create-an-invoice\">Create an Invoice</a>\n    \n- <a href=\"https://razorpay.com/docs/api/invoices/#fetch-multiple-invoices\">Fetch Invoices</a>\n    \n- <a href=\"https://razorpay.com/docs/api/invoices/#fetch-an-invoice\">Fetch Invoice by ID</a>\n    \n- <a href=\"https://razorpay.com/docs/api/invoices/#update-an-invoice\">Update an Invoice</a>\n    \n- <a href=\"https://razorpay.com/docs/api/invoices/#issue-an-invoice\">Issue an Invoice</a>\n    \n- <a href=\"https://razorpay.com/docs/api/invoices/#send-notifications\">Send or Resend Notification</a>\n    \n- <a href=\"https://razorpay.com/docs/api/invoices/#cancel-an-invoice\">Cancel an Invoice</a>\n    \n- <a href=\"https://razorpay.com/docs/api/invoices/#delete-an-invoice\">Delete an Invoice</a>"}, {"name": "Items APIs", "item": [{"name": "Create an Item", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Yellow Herb\",\n    \"description\": \"Yellow herb from Resident Evil\",\n    \"amount\": 50000,\n    \"currency\": \"INR\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/items", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "items"]}}, "response": []}, {"name": "Fetch Items", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/items", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "items"]}, "description": "Use this [API](https://razorpay.com/docs/api/items/#fetch-multiple-items) to fetch multiple items."}, "response": []}, {"name": "Fetch Item by ID", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/items/{item_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "items", "{item_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/items/#fetch-an-item) to fetch a specific item."}, "response": []}, {"name": "Update an Item", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Green Herb\",\n    \"description\": \"Green herb from Resident Evil\",\n    \"amount\": 70000,\n    \"currency\": \"INR\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/items/{item_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "items", "{item_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/items/#update-an-item) to update a specific item."}, "response": []}, {"name": "Delete an Item", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/items/{item_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "items", "{item_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/items/#delete-an-item) to delete a specific item."}, "response": []}], "description": "Items are products or services that you bill customers for by adding them to an invoice. You can create an item using APIs. When an item is created, it will appear on the list of items in the Dashboard.\n\nKnow more about [Items APIs](https://razorpay.com/docs/api/items/).\n\n## **List of APIs**\n\n*   [Create an Item](https://razorpay.com/docs/api/items/#create-an-item)\n*   [Fetch an Item](https://razorpay.com/docs/api/items/#fetch-an-item)\n*   [Fetch Multiple Items](https://razorpay.com/docs/api/items/#fetch-multiple-items)\n*   [Update an Item](https://razorpay.com/docs/api/items/#update-an-item)\n*   [Delete an Item](https://razorpay.com/docs/api/items/#delete-an-item)"}, {"name": "Subscriptions APIs", "item": [{"name": "Step 1 - Plans", "item": [{"name": "Create Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"period\": \"weekly\",\n  \"interval\": 1,\n  \"item\": {\n    \"name\": \"Test plan - Weekly\",\n    \"amount\": 69900,\n    \"currency\": \"INR\",\n    \"description\": \"Description for the test plan - Weekly\"\n  },\n  \"notes\": {\n    \"notes_key_1\": \"<PERSON>, <PERSON>, Hot\",\n    \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/plans", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "plans"]}, "description": "You must create a plan before creating a subscription via the Checkout or using the Subscription Link feature.\n\nKnow more about the <a href=\"https://razorpay.com/docs/api/subscriptions/#create-a-plan\" target=\"_blank\">Create a Plan</a> API."}, "response": []}, {"name": "Fetch All Plans", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/plans", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "plans"]}, "description": "You can fetch all the plans created by you.\nKnow more about the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-all-plans\" target=\"_blank\">Fetch all Plans</a> API."}, "response": []}, {"name": "Fetch Plans by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/plans/{plan_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "plans", "{plan_id}"]}, "description": "You can fetch a particular plan using its ID.\nKnow more about the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-a-plan-by-id\" target=\"_blank\">Fetch a Plan</a> API."}, "response": []}], "description": "A plan is a foundation on which a subscription is built. It acts as a reusable template and contains details of the goods or services offered, the amount to be charged and the frequency at which the customer should be charged (billing cycle). Depending upon your business, you can create multiple plans with different billing cycles and pricing.\n\n## List of APIs\n\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-a-plan-by-id)[Create a Plan](https://razorpay.com/docs/api/subscriptions/#create-a-plan)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-a-plan-by-id)[Fetch all Plans](https://razorpay.com/docs/api/subscriptions/#fetch-all-plans)\n*   [Fetch a Plan by ID](https://razorpay.com/docs/api/subscriptions/#fetch-a-plan-by-id)"}, {"name": "Step 2 - Subscriptions", "item": [{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\":\"{plan_id}\",\n  \"total_count\":6,\n  \"quantity\":1,\n  \"start_at\":1735689600,\n  \"expire_by\":1893456000,\n  \"customer_notify\":1,\n  \"addons\":[\n    {\n      \"item\":{\n        \"name\":\"Delivery charges\",\n        \"amount\":30000,\n        \"currency\":\"INR\"\n      }\n    }\n  ],\n  \"offer_id\":\"{offer_id}\",\n  \"notes\":{\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions"]}, "description": "You can create a subscription using the a <a href=\"https://razorpay.com/docs/api/subscriptions/#create-a-subscription\" target=\"_blank\">Create a Subscription API</a>."}, "response": []}, {"name": "Create Subscription Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\":\"{plan_id}\",\n  \"total_count\":12,\n  \"quantity\":1,\n  \"start_at\":1735689600,\n  \"expire_by\":1893456000,\n  \"customer_notify\":1,\n  \"addons\":[\n    {\n      \"item\":{\n        \"name\":\"Delivery charges\",\n        \"amount\":30000,\n        \"currency\":\"INR\"\n      }\n    }\n  ],\n  \"notes\":{\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  },\n  \"offer_id\":\"{offer_id}\",\n  \"notify_info\":{\n    \"notify_phone\":\"**********\",\n    \"notify_email\":\"<EMAIL>\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions"]}, "description": "You can create a subscription link using the a <a href=\"https://razorpay.com/docs/api/subscriptions/#create-a-subscription-link\" target=\"_blank\">Create a Subscription Link API</a>."}, "response": []}, {"name": "Fetch All Subscriptions", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/subscriptions", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions"]}, "description": "You can fetch all the subscriptions created by you using the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-all-subscriptions\" target=\"_blank\">Fetch All Subscriptions API</a>."}, "response": []}, {"name": "Fetch Subscriptions by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}"]}, "description": "You can fetch a specific subscription created by you using the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-subscription-by-id\" target=\"_blank\">Fetch a Subscription API</a>."}, "response": []}, {"name": "Cancel Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cancel_at_cycle_end\": 0\n}"}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}/cancel", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}", "cancel"]}, "description": "You can cancel a specific subscription created by you using the <a href=\"https://razorpay.com/docs/api/subscriptions/#cancel-a-subscription\" target=\"_blank\">Cancel a Subscription API</a>."}, "response": []}, {"name": "Update Subscription", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": \"{plan_id}\",\n  \"quantity\": 3,\n  \"remaining_count\": 6,\n  \"schedule_change_at\": \"now\",\n  \"customer_notify\": 1,\n  \"offer_id\":\"{offer_id}\"\n}"}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}"]}, "description": "You can update a specific subscription created by you using the <a href=\"https://razorpay.com/docs/api/subscriptions/#update-a-subscription\" target=\"_blank\">Update a Subscription API</a>."}, "response": []}, {"name": "Fetch Details of a Pending Update", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}/retrieve_scheduled_changes", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}", "retrieve_scheduled_changes"]}, "description": "You can fetch the details of a pending update using the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-details-of-a-pending-update\" target=\"_blank\">Fetch details of a pending update API</a>."}, "response": []}, {"name": "Cancel Pending Update", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}/cancel_scheduled_changes", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}", "cancel_scheduled_changes"]}, "description": "You can cancel a pending update using the <a href=\"https://razorpay.com/docs/api/subscriptions/#cancel-an-update\" target=\"_blank\">Cancel a pending update API</a>."}, "response": []}, {"name": "Pause a Subscription", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"pause_at\":\"now\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}/pause", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}", "pause"]}, "description": "You can pause a subscription using the <a href=\"https://razorpay.com/docs/api/subscriptions/#pause-a-subscription\" target=\"_blank\">Pause a Subscription API</a>."}, "response": []}, {"name": "Resume a Subscription", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"resume_at\":\"now\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}/resume", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}", "resume"]}, "description": "You can resume a paused subscription using the <a href=\"https://razorpay.com/docs/api/subscriptions/#resume-a-subscription\" target=\"_blank\">Resume a Subscription API</a>."}, "response": []}, {"name": "Fetch All Invoices for a Subscription", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1//invoices?subscription_id={sub_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "", "invoices"], "query": [{"key": "subscription_id", "value": "{sub_id}"}]}, "description": "You can fetch all invoices for a subscription using the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-all-invoices-for-a-subscription\" target=\"_blank\">Fetch All Invoices for a Subscription API</a>. Here, the count indicates the number of invoices generated for the subscription."}, "response": []}, {"name": "Link an Offer to a Subscription", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": \"plan_00000000000001\",\n  \"total_count\": 12,\n  \"quantity\": 1,\n  \"start_at\": 1561852800,\n  \"expire_by\": 1561939199,\n  \"customer_notify\": 1,\n  \"addons\": [\n    {\n    \"item\": {\n      \"name\": \"Delivery charges\",\n      \"amount\": 30000,\n      \"currency\": \"INR\"\n      }\n    }\n  ],\n  \"offer_id\":\"offer_JHD834hjbxzhd38d\",\n  \"notes\": {\n    \"notes_key_1\":\"<PERSON>, <PERSON>, Hot\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  },\n  \"notify_info\":{\n    \"notify_phone\": \"+**********\",\n    \"notify_email\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions"]}}, "response": []}, {"name": "Delete an Offer Linked to a Subscription", "request": {"method": "DELETE", "header": [], "url": {"raw": "/subscriptions/{sub_id}/{offer_id}", "path": ["subscriptions", "{sub_id}", "{offer_id}"]}, "description": "You can delete an offer linked to a subscription using the <a href=\"https://razorpay.com/docs/subscriptions/offers/link-offer-subscription/#delete-an-offer-linked-to-a-subscription\" target=\"_blank\">Delete an offer linked to a Subscription API</a>."}, "response": []}], "description": "Subscriptions allow you to charge a customer's card periodically. A subscription ties a customer to a particular plan you have created. It contains details like the plan, the start date, total number of billing cycles, free trial period (if any) and upfront amount to be collected.\n\nKnow more about <a href=\"https://razorpay.com/docs/api/subscriptions/#subscriptions\">Subscriptions APIs</a>.\n\n## List of APIs\n\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Create a Subscription](https://razorpay.com/docs/api/subscriptions/#create-a-subscription)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Create a Subscription Link](https://razorpay.com/docs/api/subscriptions/#create-a-subscription-link)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Fetch All Subscriptions](https://razorpay.com/docs/api/subscriptions/#fetch-all-subscriptions)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Fetch Subscription by ID](https://razorpay.com/docs/api/subscriptions/#fetch-subscription-by-id)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Cancel a Subscription](https://razorpay.com/docs/api/subscriptions/#cancel-a-subscription)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Update a Subscription](https://razorpay.com/docs/api/subscriptions/#update-a-subscription)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Fetch Details of a Pending Update](https://razorpay.com/docs/api/subscriptions/#fetch-details-of-a-pending-update)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Cancel an Update](https://razorpay.com/docs/api/subscriptions/#cancel-an-update)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Pause a Subscription](https://razorpay.com/docs/api/subscriptions/#pause-a-subscription)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Resume a Subscription](https://razorpay.com/docs/api/subscriptions/#resume-a-subscription)\n*   [](https://betasite.razorpay.com/docs/postman-api-change/razorpay/api/subscriptions/#fetch-all-invoices-for-a-subscription)[Fetch All Invoices for a Subscription](https://razorpay.com/docs/api/subscriptions/#fetch-all-invoices-for-a-subscription)"}, {"name": "Add-ons", "item": [{"name": "Create Add-on", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"item\": {\n        \"name\": \"Extra appala (papadum)\",\n        \"amount\": 30000,\n        \"currency\": \"INR\",\n        \"description\": \"1 extra oil fried appala with meals\"\n    },\n    \"quantity\": 2\n}"}, "url": {"raw": "https://api.razorpay.com/v1/subscriptions/{sub_id}/addons", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "subscriptions", "{sub_id}", "addons"]}, "description": "You can create an add-on using the <a href=\"https://razorpay.com/docs/api/subscriptions/#create-an-add-on\" target=\"_blank\">Create an Add-on API</a>."}, "response": []}, {"name": "Fetch All Add-ons", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/addons/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "addons", ""]}, "description": "You can fetch all add-ons created by you using the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-all-add-ons\" target=\"_blank\">Fetch all Add-ons API</a>."}, "response": []}, {"name": "Fetch Add-on by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/addons/{ao_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "addons", "{ao_id}"]}, "description": "You can fetch a particular add-on created by you using the <a href=\"https://razorpay.com/docs/api/subscriptions/#fetch-an-add-on-by-id\" target=\"_blank\">Fetch an Add-on API</a>."}, "response": []}, {"name": "Delete Add-on", "request": {"method": "DELETE", "header": [], "url": {"raw": "https://api.razorpay.com/v1/addons/{ao_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "addons", "{ao_id}"]}, "description": "You can delete a particular add-on created by you using the <a href=\"https://razorpay.com/docs/api/subscriptions/#delete-an-add-on\" target=\"_blank\">Delete an Add-on API</a>."}, "response": []}], "description": "You can create add-ons to charge the customer an extra amount for a particular billing cycle. Once you create an add-on for a subscription, it is added to the next invoice that is generated. On the next scheduled charge, the customer is charged the add-on amount in addition to their regular subscription amount.\n\nKnow more about <a href=\"https://razorpay.com/docs/api/subscriptions/#add-on\">Add-on APIs</a>.\n\n## List of APIs\n\n*   [Create an Add-on](https://razorpay.com/docs/api/subscriptions/#create-an-add-on)\n*   [Fetch all Add-ons](https://razorpay.com/docs/api/subscriptions/#fetch-all-add-ons)\n*   [Fetch an Add-on by ID](https://razorpay.com/docs/api/subscriptions/#fetch-an-add-on-by-id)\n*   [Delete an Add-on](https://razorpay.com/docs/api/subscriptions/#delete-an-add-on)"}], "description": "Enable recurring payment schedule, control the billing cycle and get instant alerts on subscription activity with Razorpay Subscriptions.\n\nYou can create, fetch, query or cancel plans, subscriptions and add-ons using the <a href=\"https://razorpay.com/docs/api/subscriptions\" target=\"_blank\">Subscriptions API</a>.\n\n## List of APIs\n\n- <a href=\"https://razorpay.com/docs/api/subscriptions/#plans\" target=\"_blank\">Plan APIs</a>\n- <a href=\"https://razorpay.com/docs/api/subscriptions/#subscriptions\" target=\"_blank\">Subscription APIs</a>\n- <a href=\"https://razorpay.com/docs/api/subscriptions/#add-on\" target=\"_blank\">Add-on APIs</a>"}, {"name": "Payment Links APIs", "item": [{"name": "Standard Payment Links", "item": [{"name": "Create a Payment Link", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"expire_by\": 1691097057,\n  \"reference_id\": \"TSsd1989\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"notes\": {\n    \"policy_name\": \"<PERSON><PERSON><PERSON>\"\n  },\n  \"callback_url\": \"https://example-callback-url.com/\",\n  \"callback_method\": \"get\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links"]}, "description": "You can create a payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#create-payment-link\" target=\"_blank\">Create a Payment Link API</a>."}, "response": []}, {"name": "Update a Payment Link", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"notes\": {\n        \"policy_name\": \"<PERSON><PERSON>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/{plink_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", "{plink_id}"]}, "description": "You can update a specific payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#update-payment-link\" target=\"_blank\">Update a Payment Link API</a>."}, "response": []}, {"name": "Cancel a Payment Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/{plink_id}/cancel", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", "{plink_id}", "cancel"]}, "description": "You can cancel a specific payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#cancel-payment-link\" target=\"_blank\">Cancel a Payment Link</a> API."}, "response": []}, {"name": "Fetch all Payment Links", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "You can fetch all payment links using the <a href=\"https://razorpay.com/docs/api/payment-links/#all-payment-links\" target=\"_blank\">Fetch all Payment Links API</a>."}, "response": []}, {"name": "Resend a Payment Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/{plink_id}/notify_by/sms", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", "{plink_id}", "notify_by", "sms"]}, "description": "You can resend a specific payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#send-or-resend-notifications\" target=\"_blank\">Resend a Payment Link</a> API."}, "response": []}, {"name": "Fetch a Payment Link", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/{plink_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", "{plink_id}"]}, "description": "You can fetch a specific payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#specific-payment-links-by-id\" target=\"_blank\">Fetch a Payment Link API</a>."}, "response": []}], "description": "Standard Payment Links are normal, non-customized Payment Links, which are not customized as per your business preferences. Know more about these [APIs](https://razorpay.com/docs/api/payment-links/).\n\n## List of APIs\n\n*   [Create a Payment Link](https://razorpay.com/docs/api/payment-links/#create-payment-link)\n*   [Update a Payment Link](https://razorpay.com/docs/api/payment-links/#update-payment-link)\n*   [Cancel a Payment Link](https://razorpay.com/docs/api/payment-links/#cancel-payment-link)\n*   [Fetch all Payment Links](https://razorpay.com/docs/api/payment-links/#all-payment-links)\n*   [Fetch a Payment Link](https://razorpay.com/docs/api/payment-links/#specific-payment-links-by-id)\n*   [Resend a Payment Link](https://razorpay.com/docs/api/payment-links/#send-or-resend-notifications)"}, {"name": "UPI Payment Links", "item": [{"name": "Create a Payment Link", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"upi_link\": \"true\",\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": false,\n  \"first_min_partial_amount\": 100,\n  \"expire_by\": 1691097057,\n  \"reference_id\": \"TS1989\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"notes\": {\n    \"policy_name\": \"<PERSON><PERSON><PERSON>\"\n  },\n  \"callback_url\": \"https://example-callback-url.com/\",\n  \"callback_method\": \"get\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links"]}, "description": "You can create a payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#create-payment-link\" target=\"_blank\">Create a Payment Link API</a>."}, "response": []}, {"name": "Update a Payment Link", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"reference_id\": \"TS35\",\n  \"expire_by\": 1653347540,\n  \"reminder_enable\": false,\n  \"notes\": {\n    \"policy_name\": \"<PERSON><PERSON><PERSON>\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/{plink_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", "{plink_id}"]}, "description": "You can update a specific payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#update-payment-link\" target=\"_blank\">Update a Payment Link API</a>."}, "response": []}, {"name": "Cancel a Payment Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/{plink_id}/cancel", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", "{plink_id}", "cancel"]}, "description": "You can cancel a specific payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#cancel-payment-link\" target=\"_blank\">Cancel a Payment Link</a> API."}, "response": []}, {"name": "Fetch all Payment Links", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "You can fetch all payment links using the <a href=\"https://razorpay.com/docs/api/payment-links/#all-payment-links\" target=\"_blank\">Fetch all Payment Links API</a>."}, "response": []}, {"name": "Fetch a Payment Link", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/{plink_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", "{plink_id}"]}, "description": "You can fetch a specific payment link using the <a href=\"https://razorpay.com/docs/api/payment-links/#specific-payment-links-by-id\" target=\"_blank\">Fetch a Payment Link API</a>."}, "response": []}], "description": "UPI Payment Links are normal, non-customized Payment Links, which are not customized as per your business preferences. Know more about these [APIs](https://razorpay.com/docs/api/payment-links/).\n\n## List of APIs\n\n- [Create a Payment Link](https://razorpay.com/docs/api/payment-links/#create-payment-link)\n    \n- [Update a Payment Link](https://razorpay.com/docs/api/payment-links/#update-payment-link)\n    \n- [Cancel a Payment Link](https://razorpay.com/docs/api/payment-links/#cancel-payment-link)\n    \n- [Fetch all Payment Links](https://razorpay.com/docs/api/payment-links/#all-payment-links)\n    \n- [Fetch a Payment Link](https://razorpay.com/docs/api/payment-links/#specific-payment-links-by-id)\n    \n- [Resend a Payment Link](https://razorpay.com/docs/api/payment-links/#send-or-resend-notifications)"}, {"name": "Custom Payment Links", "item": [{"name": "Implement Thematic Changes in Checkout", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#423212\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"checkout\": {\n      \"theme\": {\n        \"hide_topbar\": true\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links"]}, "description": "You can modify the top bar theme element of the Checkout UI on the payment request page to restrict customers from navigating to the initial screen of Checkout and selecting a different payment method. Know more about <a href=\"https://razorpay.com/docs/api/payment-links/customise/checkout-theme/\" target=\"_blank\">this API</a>."}, "response": []}, {"name": "Change Merchant Name and Description", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#2234542\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"checkout\": {\n      \"name\": \"Lacme Corp\"\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links"]}, "description": "Learn how to customize the merchant name and description appearing on the Checkout section of the Payment Link payment request page."}, "response": []}, {"name": "Customize Payment Methods on Razorpay Checkout", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#523442\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"checkout\": {\n      \"method\": {\n        \"netbanking\": true,\n        \"card\": true,\n        \"upi\": false,\n        \"wallet\": false\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Learn how to enable and display specific payment methods on Razorpay Checkout."}, "response": []}, {"name": "Prefill Checkout Fields", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#419\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"checkout\": {\n      \"prefill\": {\n        \"select_partial\": true\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Learn how to prefill payment methods, bank names and partial payment-related fields."}, "response": []}, {"name": "Set Checkout Fields as Read-Only", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#20\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"checkout\": {\n      \"readonly\": {\n        \"email\": true,\n        \"contact\": true\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Learn how to customize and set the email and contact fields in the Checkout Section of the Payment Links payment request page using Razorpay API."}, "response": []}, {"name": "Rename Labels in Checkout Section", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#421\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"checkout\": {\n      \"partial_payment\": {\n        \"min_amount_label\": \"Minimum Money to be paid\",\n        \"partial_amount_label\": \"Pay in parts\",\n        \"partial_amount_description\": \"Pay at least ₹100\",\n        \"full_amount_label\": \"Pay the entire amount\"\n      }\n    }\n  }\n}'", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Learn how to customize and rename the labels in the Checkout Section of the Payment Links payment request page using Razorpay API."}, "response": []}, {"name": "Rename Labels in Payment Details Section", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#412232\",\n  \"description\": \"Payment for policy no #23456\",\n  \"expire_by\": 1599193801,\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"hosted_page\": {\n      \"label\": {\n        \"receipt\": \"Ref No.\",\n        \"description\": \"Course Name\",\n        \"amount_payable\": \"Course Fee Payable\",\n        \"amount_paid\": \"Course Fee Paid\",\n        \"partial_amount_due\": \"Fee Installment Due\",\n        \"partial_amount_paid\": \"Fee Installment Paid\",\n        \"expire_by\": \"Pay Before\",\n        \"expired_on\": \"Link Expired. Please contact Admin\",\n        \"amount_due\": \"Course Fee Due\"\n      },\n      \"show_preferences\": {\n        \"issued_to\": false\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Learn how to customize and rename the labels in the Payment Details section of the Payment Links payment request page using Razorpay API. You can also display labels in a different language, for example, Hindi or Tamil."}, "response": []}, {"name": "Rename Labels in Payment Details Section - Hindi", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#423\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON> <PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  #customization parameters start below#\n  \"options\":{\n    \"hosted_page\":{\n      \"label\":{\n        \"receipt\":\"रसीद संख्या\",\n        \"description\":\"कोर्स का नाम\",\n        \"amount_payable\":\"शुल्क भुगतान\",\n        \"amount_paid\":\"शुल्क जमा \",\n        \"partial_amount_due\":\"बाकी शुल्क किस्त\",\n        \"partial_amount_paid\":\"शुल्क किस्त जमा\",\n        \"expire_by\":\"शुल्क पेमेंट लास्ट डेट\",\n        \"expired_on\":\"लिंक की समय सीमा समाप्त हो गई है। कृपया व्यवस्थापक से संपर्क करें\",\n        \"amount_due\": \"बाकी शुल्क \"\n      },\n      \"show_preferences\":{\n        \"issued_to\":false\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Learn how to customize and rename the labels in the Payment Details section of the Payment Links payment request page using Razorpay API. You can also display labels in a different language, for example, Hindi or Tamil."}, "response": []}], "description": "You can send standard payment links to customers via email and SMS. When customers click on the payment link, they are redirected to a page hosted by us where they can complete the payment.\n\nThe payment request page consists of two sections:\n\n*   Payment Details: Displays details about the payment description, expiry date, payable amount and in case of partial payments, partial amount paid and due.\n*   Checkout: Displays the Phone and Email fields and list the various payment methods available.\n    \n\nYou can customize this hosted page as per your brand and business requirements. For example, you may display only specific payment methods, change the colour of Checkout, and so on.\n\nKnow more about - <a href=\"https://razorpay.com/docs/api/payment-links/customise\">Custom Payment Links</a>.\n\n## **List of APIs**\n\n*   <a href=\"https://razorpay.com/docs/api/payment-links/customise/checkout-theme/\">Implement Thematic Changes in Payment Links Checkout Section</a>\n*   <a href=\"https://razorpay.com/docs/api/payment-links/customise/merchant-details/\">Change Business Name</a>\n*   <a href=\"https://razorpay.com/docs/api/payment-links/customise/payment-methods/\">Customize Payment Methods</a>\n*   <a href=\"https://razorpay.com/docs/api/payment-links/customise/prefill/\">Prefill Checkout Fields</a>\n*   <a href=\"https://razorpay.com/docs/api/payment-links/customise/read-only/\">Set Checkout Fields as Read-Only</a>\n*   <a href=\"https://razorpay.com/docs/api/payment-links/customise/rename-checkout-labels/\">Rename Labels in Checkout Section</a>\n*   <a href=\"https://razorpay.com/docs/api/payment-links/customise/rename-payment-details-labels/\">Rename Labels in Payment Details Section</a>"}, {"name": "Advanced Options", "item": [{"name": "Third Party Validation using Netbanking and UPI", "item": [{"name": "Netbanking", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#4sds25\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"order\": {\n      \"method\": \"netbanking\",\n      \"bank_account\": {\n        \"account_number\": \"**************\",\n        \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n        \"ifsc\": \"KKBK0000430\"\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Know how to perform third-party validation for bank accounts using this <a href=\"https://razorpay.com/docs/api/payment-links/advanced-options/third-party-validation/#request-parameters\" target=\"_blank\">API</a>."}, "response": []}, {"name": "UPI", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#42226\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"order\": {\n      \"method\": \"upi\",\n      \"bank_account\": {\n        \"account_number\": \"**************\",\n        \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n        \"ifsc\": \"KKBK0000430\"\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Know how to perform third-party validation for UPI accounts using this <a href=\"https://razorpay.com/docs/api/payment-links/advanced-options/third-party-validation/#request-parameters\" target=\"_blank\">API</a>."}, "response": []}, {"name": "Either", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#qw427\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"order\": {\n      \"bank_account\": {\n        \"account_number\": \"**************\",\n        \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n        \"ifsc\": \"KKBK0000430\"\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Know how to perform third-party validation for bank accounts/UPI using this <a href=\"https://razorpay.com/docs/api/payment-links/advanced-options/third-party-validation/#request-parameters\" target=\"_blank\">API</a>."}, "response": []}], "description": "Third-party validation is a process using which you can ensure that customers make payments using only those bank accounts that they had provided at the time of registration.\n\nKnow more about third-party validation using  <a href=\"https://razorpay.com/docs/api/payment-links/advanced-options/third-party-validation/\" target=\"_blank\">Payment Links API</a>."}, {"name": "Transfer Payment Link Amount to Linked Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1500,\n  \"currency\": \"INR\",\n  \"accept_partial\": false,\n  \"reference_id\": \"#aasasw8\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": true,\n  \"options\": {\n    \"order\": {\n      \"transfers\": [\n        {\n          \"account\": \"acc_CPRsN1LkFccllA\",\n          \"amount\": 500,\n          \"currency\": \"INR\",\n          \"notes\": {\n            \"branch\": \"Acme Corp Bangalore North\",\n            \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n          },\n          \"linked_account_notes\": [\n            \"branch\"\n          ]\n        },\n        {\n          \"account\": \"acc_CNo3jSI8OkFJJJ\",\n          \"amount\": 500,\n          \"currency\": \"INR\",\n          \"notes\": {\n            \"branch\": \"Acme Corp Bangalore South\",\n            \"name\": \"<PERSON><PERSON><PERSON>\"\n          },\n          \"linked_account_notes\": [\n            \"branch\"\n          ]\n        }\n      ]\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}, "description": "Using Razorpay Payment Links, you can transfer the payments received from your customers automatically to your linked accounts. You no longer need to create a separate transfer using Razorpay Route Transfers API. Instead, you can use the order parameter in <a href=\"https://razorpay.com/docs/api/payment-links/advanced-options/transfers/\" target=\"_blank\">Create Payment Links API</a> to automate the transfer once the payment is received from the customer."}, "response": []}, {"name": "Offers on Payment Links", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 3400,\n  \"currency\": \"INR\",\n  \"accept_partial\": false,\n  \"reference_id\": \"#425\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": false,\n  \"options\": {\n    \"order\": {\n      \"offers\": [\n        \"offer_F4WMTC3pwFKnzq\",\n        \"offer_F4WJHqvGzw8dWF\"\n      ]\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links"]}, "description": "Using Razorpay Offers, you can provide discounts or cashback on Payment Links issued to customers. You can restrict the payment methods on which the Offers are applied and limit their usage to a defined time period.\n\nKnow more about this <a href=\"https://razorpay.com/docs/api/payment-links/advanced-options/offers/\" target=\"_blank\">API</a>."}, "response": []}, {"name": "Manage Reminders for Payment Links", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"accept_partial\": true,\n  \"first_min_partial_amount\": 100,\n  \"reference_id\": \"#425\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links", ""]}}, "response": []}, {"name": "Offers on Payment Links", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 3400,\n  \"currency\": \"INR\",\n  \"accept_partial\": false,\n  \"reference_id\": \"#425\",\n  \"description\": \"Payment for policy no #23456\",\n  \"customer\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact\": \"+************\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"notify\": {\n    \"sms\": true,\n    \"email\": true\n  },\n  \"reminder_enable\": false,\n  \"options\": {\n    \"order\": {\n      \"offers\": [\n        \"offer_F4WMTC3pwFKnzq\",\n        \"offer_F4WJHqvGzw8dWF\"\n      ]\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payment_links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payment_links"]}}, "response": []}], "description": "You can perform advanced configuration on Payment Links to help achieve your business requirements:\n\n1.  You can set up Payment Links so that payments made against the link are automatically transferred to a linked account. This saves you the additional step of making a Transfer API call later.\n2.  To attract and retain customers, you can provide promotional offers on Payment Links such as discounts and cashback. Customers can avail these offers while making payments using Payment Links.\n3.  You can use Razorpay Payment Links to perform third-party validation of bank accounts provided by your customers.\n    \n\n## List of APIs\n\n*   [Transfer Payments Received Using Payment Links](https://razorpay.com/docs/api/payment-links/advanced-options/transfers/)\n*   [Offers on Payment Links](https://razorpay.com/docs/api/payment-links/advanced-options/offers/)\n*   [Third-party Validation on Payment Links](https://razorpay.com/docs/api/payment-links/advanced-options/third-party-validation/)"}], "description": "Payment Links are URLs that you can send to your customers through SMS and email to collect payments from them. Customers can click on the URL, which opens the payment request page, and complete the payment using any of the available payment methods.\n\nYou can easily set up and use Payment Links with these [APIs](https://razorpay.com/docs/api/payment-links/).\n\n## List of APIs\n\n*   [Standard Payment Links](https://razorpay.com/docs/api/payment-links/)\n*   [Custom Payment Links](https://razorpay.com/docs/api/payment-links/customise/)\n*   [Advanced Options](https://razorpay.com/docs/api/payment-links/advanced-options/)"}, {"name": "Smart Collect", "item": [{"name": "Smart Collect -TPV", "item": [{"name": "Fetch Operations", "item": [{"name": "Fetch a Customer Identifier By ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/{va_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "{va_id}"]}, "description": "You can use <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-a-virtual-account-by-id\" target=\"_blank\">this API</a> to fetch a specific virtual account using its ID."}, "response": []}, {"name": "Fetch All Customer Identifiers", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts"]}, "description": "You can use <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-all-virtual-accounts\" target=\"_blank\">this API</a> to fetch all the virtual accounts."}, "response": []}, {"name": "Fetch Payments Made to a Customer Identifier", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/{va_id}/payments", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "{va_id}", "payments"]}, "description": "You can use this <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-payments-for-a-virtual-account\" target=\"_blank\">API</a> to retrieve payments made to a specific virtual account."}, "response": []}, {"name": "Method is Bank Transfer", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/bank_transfer", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "bank_transfer"]}, "description": "You can use this <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-payment-details-using-id-and-transfer-method\" target=\"_blank\">API</a> to retrieve details of payments using the Payment ID and transfer method."}, "response": []}]}, {"name": "Refund Payments Made to a Customer Identifier", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"amount\": 100,\n    \"notes\": {\n        \"key_1\": \"value1\",\n        \"key_2\": \"value2\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/refund", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "refund"]}, "description": "You can process refunds for a payment made towards a virtual account using this <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#refund-payments-made-to-a-virtual-account\" target=\"_blank\">API</a>."}, "response": []}, {"name": "Close a Customer Identifier", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/{va_id}/close", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "{va_id}", "close"]}, "description": "Use this <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#close-a-virtual-account\" target=\"_blank\">API</a> to close a virtual account."}, "response": []}, {"name": "Update a Virtual Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"receivers\": {\n        \"types\": [\n            \"vpa\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/{va_id}/receiver", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "{va_id}", "receiver"]}, "description": "You can add a receiver to a virtual account using this API."}, "response": []}, {"name": "Create a Customer Identifier", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"receivers\": {\n        \"types\": [\n            \"bank_account\"\n        ]\n    },\n    \"allowed_payers\": [\n      {\n        \"type\": \"bank_account\",\n        \"bank_account\": {\n          \"ifsc\": \"UTIB0000013\",\n          \"account_number\": \"***************\"\n        }\n      },\n      {\n        \"type\": \"bank_account\",\n        \"bank_account\": {\n          \"ifsc\": \"UTIB0000014\",\n          \"account_number\": \"***************\"\n        }\n      }\n    ],\n    \"description\": \"Virtual Account created for Raftar Soft\",\n    \"customer_id\": \"cust_CaVDm8eDRSXYME\",//Replace customer_id\n    \"close_by\": **********,\n    \"notes\": {\n        \"project_name\": \"Banking Software\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts"]}, "description": "You can create a virtual account using this <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#create-virtual-account\" target=\"_blank\">API</a>."}, "response": []}, {"name": "Add an Allowed Payer Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n   \"type\":\"bank_account\",\n   \"bank_account\":{\n      \"ifsc\":\"UTIB0000013\",\n      \"account_number\":\"***************\"\n   }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/:va_id/allowed_payers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", ":va_id", "allowed_payers"], "variable": [{"key": "va_id", "value": ""}]}, "description": "You can add an allowed payer to a virtual account using this <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#add-an-allowed-payer-account\" target=\"_blank\">API</a>."}, "response": []}, {"name": "Delete an Allowed Payer Account", "request": {"method": "DELETE", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/va_DlGmm7jInLudH9/allowed_payers/ba_DlGmm9mSj8fjRM", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "va_DlGmm7jInLudH9", "allowed_payers", "ba_DlGmm9mSj8fjRM"]}, "description": "You can delete an allowed payer from a virtual account using this <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#delete-an-allowed-payer-account\" target=\"_blank\">API</a>."}, "response": []}, {"name": "Add Receiver to an Existing Customer Identifier with VPA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"types\": [\n    \"vpa\"\n  ],\n  \"vpa\": {\n    \"descriptor\": \"gau<PERSON><PERSON><PERSON>\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/va_DzcFjMezDcN8vv/receivers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "va_DzcFjMezDcN8vv", "receivers"]}}, "response": []}, {"name": "Add Receiver to an Existing Customer Identifier with Bank Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"types\": [\n    \"bank_account\"\n  ]\n}"}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/va_DzaBLzIz494C64/receivers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "va_DzaBLzIz494C64", "receivers"]}}, "response": []}], "description": "Using Razorpay Smart Collect API, you can comply with the regulatory guidelines to ensure that the customers make payments only from their registered bank accounts (TPV). If payments are made from the unregistered accounts (non-TPV), they are automatically refunded to the customers.\n\nWhen you create a virtual account, send the allowed_payers array with the customer's bank account_number and ifsc. This helps to identify TPV transactions and automatically refund non-TPV transactions.\n\nKnow more about <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/\" target=\"_blank\">third-party validation</a>.\n\n<b>List of APIs</b>:\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#create-virtual-account\" target=\"_blank\">Create Virtual Account</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#add-an-allowed-payer-account\" target=\"_blank\">Add an Allowed Payer Account</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-a-virtual-account-by-id\" target=\"_blank\">Fetch a Virtual Account by ID</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-all-virtual-accounts\" target=\"_blank\">Fetch all Virtual Accounts</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-payments-for-a-virtual-account\" target=\"_blank\">Fetch Payments for a Virtual Account</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#fetch-payment-details-using-id-and-transfer-method\" target=\"_blank\">Fetch Payment Details using ID and Transfer Method</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#delete-an-allowed-payer-account\" target=\"_blank\">Delete an Allowed Payer Account</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#refund-payments-made-to-a-virtual-account\" target=\"_blank\">Refund Payments made to a Virtual Account</a>\n- <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/#close-a-virtual-account\" target=\"_blank\">Close a Virtual Account</a>"}, {"name": "Smart Collect", "item": [{"name": "Create Virtual Accounts and VPAs", "item": [{"name": "Create a Virtual Account with Bank Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"receivers\": {\n        \"types\": [\n            \"bank_account\"\n        ]\n    },\n    \"description\": \"Virtual Account created for Raftar Soft\",\n    \"customer_id\": \"cust_id\",//Enter customer_id\n    \"close_by\": **********,\n    \"notes\": {\n        \"project_name\": \"Banking Software\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts"]}, "description": "Use this <a href=\"https://razorpay.com/docs/api/smart-collect/#create-virtual-account\" target=\"_blank\">API</a> to create a Virtual Account."}, "response": []}, {"name": "Create Virtual Account and VPA", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"receivers\": {\n        \"types\": [\n            \"vpa\",\n            \"bank_account\"\n        ]\n    },\n    \"description\": \"Receive payment instalment from Gaurav Kumar- Flat No 105\",\n    \"customer_id\": \"cust_id\",//Replace customer_id\n    \"close_by\": **********\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts"]}, "description": "Use this [API](https://razorpay.com/docs/api/smart-collect/#sample-code-for-both--vpa-and-bank-account) to create a Virtual Account and a VPA using Razorpay Smart Collect APIs."}, "response": []}, {"name": "Create a Virtual Account with VPA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"receivers\": {\n    \"types\": [\n      \"vpa\"\n    ],\n    \"vpa\": { // Pass this only for custom descriptor.\n      \"descriptor\": \"gau<PERSON><PERSON><PERSON>\"\n    }\n  },\n  \"description\": \"Receive payment instalment from Gaurav Kumar- Flat No 105\",\n  \"customer_id\": \"cust_BM8NbnFAk1BVDA\",\n  \"close_by\": **********\n}"}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts"]}}, "response": []}]}, {"name": "Fetch Operations", "item": [{"name": "Fetch Payment Details using ID and Transfer Method", "item": [{"name": "Method is Bank Transfer", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/bank_transfer", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "bank_transfer"]}, "description": "Use this [API](https://razorpay.com/docs/api/smart-collect/#bank-transfer) to retrieve details of a payment using the Payment ID and transfer method."}, "response": []}, {"name": "Method is UPI Transfer", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/upi_transfer", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "upi_transfer"]}, "description": "Use this [API](https://razorpay.com/docs/api/smart-collect/#upi) to retrieve details of a payment using the Payment ID and transfer method."}, "response": []}, {"name": "Method is Bank Transfer via UTR", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://api.razorpay.com/v1/payments?skip=0&count=25&va_transaction_id=************&virtual_account=1", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments"], "query": [{"key": "skip", "value": "0"}, {"key": "count", "value": "25"}, {"key": "va_transaction_id", "value": "************"}, {"key": "virtual_account", "value": "1"}]}}, "response": []}]}, {"name": "Fetch a Virtual Account By ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/{va_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "{va_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/smart-collect/#fetch-a-virtual-account-by-id) to fetch a specific virtual account using its ID."}, "response": []}, {"name": "Fetch All Virtual Accounts", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts"]}, "description": "Use this [API](https://razorpay.com/docs/api/smart-collect/#fetch-all-virtual-accounts) to fetch all the virtual accounts created by you."}, "response": []}, {"name": "Fetch Payments Made to a Virtual Account", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/{va_id}/payments", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "{va_id}", "payments"]}, "description": "Use this [API](https://razorpay.com/docs/api/smart-collect/#fetch-payments-for-a-virtual-account) to retrieve payments made to a specific virtual account."}, "response": []}]}, {"name": "Refund Payments Made to a Virtual Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"amount\": 100,\n    \"notes\": {\n        \"key_1\": \"value1\",\n        \"key_2\": \"value2\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/refund", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "refund"]}, "description": "You can use this [API](https://razorpay.com/docs/api/smart-collect/#refund-payments-made-to-a-virtual-account) to process refunds for a payment made towards a virtual account."}, "response": []}, {"name": "Close a Virtual Account", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/{va_id}/close", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "{va_id}", "close"]}, "description": "You can use this [API](https://razorpay.com/docs/api/smart-collect/#close-a-virtual-account) to close a virtual account."}, "response": []}, {"name": "Update a Virtual Account with VPA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"close_by\": **********,\n    \"description\": \"VA creation for Raftar Soft\",\n    \"notes\": {\n        \"project_name\": \"Banking Software Work\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/va_KFIrkRmd70ylIg", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "va_KFIrkRmd70ylIg"]}, "description": "You can use this [API](https://razorpay.com/docs/api/smart-collect/#add-receiver-to-an-existing-virtual-account) to add a receiver to a virtual account."}, "response": []}, {"name": "Update a Virtual Account with Bank Transfer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"types\": [\n    \"bank_account\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/va_DzaBLzIz494C64/receivers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "va_DzaBLzIz494C64", "receivers"]}, "description": "You can use this [API](https://razorpay.com/docs/api/smart-collect/#add-receiver-to-an-existing-virtual-account) to add a receiver to a virtual account."}, "response": []}, {"name": "Add Receiver to an Existing Customer Identifier with VPA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"types\": [\n    \"vpa\"\n  ],\n  \"vpa\": {\n    \"descriptor\": \"gau<PERSON><PERSON><PERSON>\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/va_DzcFjMezDcN8vv/receivers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "va_DzcFjMezDcN8vv", "receivers"]}}, "response": []}, {"name": "Add Receiver to an Existing Customer Identifier with Bank Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"types\": [\n    \"bank_account\"\n  ]\n}"}, "url": {"raw": "https://api.razorpay.com/v1/virtual_accounts/va_DzaBLzIz494C64/receivers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "virtual_accounts", "va_DzaBLzIz494C64", "receivers"]}}, "response": []}], "description": "This is the standard version of <a href=\"https://razorpay.com/docs/api/smart-collect\">Smart Collect APIs</a>.\n\n## List of APIs\n\n*   [Create Virtual Account](https://razorpay.com/docs/api/smart-collect/#create-virtual-account)\n*   [Fetch a Virtual Account by ID](https://razorpay.com/docs/api/smart-collect/#fetch-a-virtual-account-by-id)\n*   [Fetch all Virtual Accounts](https://razorpay.com/docs/api/smart-collect/#fetch-all-virtual-accounts)\n*   [Fetch Payments for a Virtual Account](https://razorpay.com/docs/api/smart-collect/#fetch-payments-for-a-virtual-account)\n*   [Fetch Payment Details using ID and Transfer Method](https://razorpay.com/docs/api/smart-collect/#fetch-payment-details-using-id-and-transfer-method)\n    *   [Bank Transfer](https://razorpay.com/docs/api/smart-collect/#bank-transfer)\n    *   [UPI](https://razorpay.com/docs/api/smart-collect/#upi)\n*   [Refund Payments made to a Virtual Account](https://razorpay.com/docs/api/smart-collect/#refund-payments-made-to-a-virtual-account)\n*   [Add Receiver to an Existing Virtual Account](https://razorpay.com/docs/api/smart-collect/#add-receiver-to-an-existing-virtual-account)\n*   [Close a Virtual Account](https://razorpay.com/docs/api/smart-collect/#close-a-virtual-account)"}], "description": "Razorpay Smart Collect enables you to create virtual accounts to accept large payments from your customers in the form of bank transfers via NEFT, RTGS and IMPS.\n\nVirtual accounts are similar to bank accounts wherein customers can transfer payments. You can create, retrieve and close virtual accounts using the Smart Collect APIs.\n\n## List of APIs\n\n*   <a href=\"https://razorpay.com/docs/api/smart-collect/\">Smart Collect</a>\n*   <a href=\"https://razorpay.com/docs/api/smart-collect-tpv/\">Smart Collect-TPV</a>"}, {"name": "Route APIs", "item": [{"name": "Transfers API", "item": [{"name": "Transfer via Orders", "request": {"method": "POST", "header": [{"key": "content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 200,\n    \"payment_capture\": 1,\n    \"currency\":\"INR\",\n    \"transfers\": [\n        {\n            \"account\": \"acc_id\",//Please replace with appropriate ID.\n            \"amount\": 100,\n            \"currency\": \"INR\",\n            \"notes\": {\n                \"branch\": \"Acme Corp Bangalore North\",\n                \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n            },\n            \"linked_account_notes\": [\n                \"branch\"\n            ],\n            \"on_hold\": true,\n            \"on_hold_until\": **********\n        },\n        {\n            \"account\": \"acc_id\",//Please replace with appropriate ID.\n            \"amount\": 100,\n            \"currency\": \"INR\",\n            \"notes\": {\n                \"branch\": \"Acme Corp Bangalore South\",\n                \"name\": \"<PERSON><PERSON><PERSON>\"\n            },\n            \"linked_account_notes\": [\n                \"branch\"\n            ],\n            \"on_hold\": false,\n            \"on_hold_until\": null\n        }\n    ]\n}"}, "url": {"raw": "https://api.razorpay.com/v1/orders", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders"]}, "description": "You can set up a transfer of funds when creating an order using the Orders API. This can be done by passing the `transfers` parameters as part of the Order API request body.\n\nKnow more about this [API](https://razorpay.com/docs/api/route/#create-transfers-from-orders)."}, "response": []}, {"name": "Transfer via Payments", "request": {"method": "POST", "header": [{"key": "content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"transfers\": [\n        {\n            \"account\": \"acc_id\",//Please replace with appropriate ID.\n            \"amount\": 100,\n            \"currency\": \"INR\",\n            \"notes\": {\n                \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n                \"roll_no\": \"IEC2011025\"\n            },\n            \"linked_account_notes\": [\n                \"roll_no\"\n            ],\n            \"on_hold\": true,\n            \"on_hold_until\": **********\n        },\n        {\n            \"account\": \"acc_id\",//Please replace with appropriate ID.\n            \"amount\": 100,\n            \"currency\": \"INR\",\n            \"notes\": {\n                \"name\": \"<PERSON><PERSON><PERSON>\",\n                \"roll_no\": \"IEC2011026\"\n            },\n            \"linked_account_notes\": [\n                \"roll_no\"\n            ],\n            \"on_hold\": false\n        }\n    ]\n}"}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/transfers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "transfers"]}, "description": "You can use this [API](https://razorpay.com/docs/api/route/#create-transfers-from-payments) to transfer a `captured` payment to one or more linked accounts using `account_id`. A response will be generated on a successful transfer with a collection of transfer entities created for the payment."}, "response": []}, {"name": "Direct Transfer", "request": {"method": "POST", "header": [{"key": "content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"account\": \"acc_id\",//Please replace with appropriate ID.\n    \"amount\": 100,\n    \"currency\": \"INR\"\n}"}, "url": {"raw": "https://api.razorpay.com/v1/transfers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers"]}, "description": "You can transfer funds to your linked accounts directly from your account balance using the Direct Transfers [API](https://razorpay.com/docs/api/route/#direct-transfers)."}, "response": []}], "description": "You can transfer funds to your linked accounts using the Razorpay Route Transfer APIs. Know more about [transfers and related fees](https://razorpay.com/docs/route/transfer-example).\n\n## [Transfer Requirements🔗](https://razorpay.com/docs/api/route/#transfer-requirements)\n\nBelow are the requirements to initiate a transfer:\n\n*   Your account must have sufficient funds to process the transfer to the linked account. The transfer will fail in case of insufficient funds.\n*   You can only transfer the `captured` payments.\n*   You can create more than one transfer on a `payment_id`. However, the total transfer amount should not exceed the captured payment amount.\n*   You cannot request a transfer on payment once a refund has been initiated.\n    \n\n## [Types of Transfers🔗](https://razorpay.com/docs/api/route/#types-of-transfers)\n\nYou can transfer funds to linked accounts using one of the following methods:\n\n*   [Transfer via Orders](https://razorpay.com/docs/api/route/#create-transfers-from-orders) - You can set up a transfer at the time of order creation.\n*   [Transfer via Payments](https://razorpay.com/docs/api/route/#create-transfers-from-payments) - You can initiate a transfer once the payment has been received from the customer.\n*   [Direct Transfer](https://razorpay.com/docs/api/route/#direct-transfers) - You can initiate a transfer directly from existing funds in your Razorpay account."}, {"name": "Fetch Details", "item": [{"name": "Fetch Transfers for a Payment", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/transfers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "transfers"]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#fetch-transfers-for-a-payment) to fetch the collection of all transfers created on a specific Payment ID."}, "response": []}, {"name": "Fetch Transfers for an Order", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/orders/order_DSkl2lBNvueOly/?expand[]=transfers&status=processing", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "orders", "order_DSkl2lBNvueOly", ""], "query": [{"key": "expand[]", "value": "transfers"}, {"key": "status", "value": "processing"}]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#fetch-transfer-for-an-order) to fetch the collection of all transfers created on a specific Order ID."}, "response": []}, {"name": "Fetch a Transfer", "request": {"method": "GET", "header": [{"key": "", "value": "", "disabled": true}], "url": {"raw": "https://api.razorpay.com/v1/transfers/{trf_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers", "{trf_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#fetch-a-transfer) to fetch details of a specific transfer."}, "response": []}, {"name": "Fetch Transfers for a Settlement", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/transfers?recipient_settlement_id={setl_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers"], "query": [{"key": "recipient_settlement_id", "value": "{setl_id}"}]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#fetch-transfers-for-a-settlement) to retrieve the collection of all transfers made for a particular `recipient_settlement_id`."}, "response": []}, {"name": "Fetch Settlement Details", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/transfers?expand[]=recipient_settlement", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers"], "query": [{"key": "expand[]", "value": "recipient_settlement"}]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#fetch-settlement-details) to fetch the details of settlements made to linked accounts.\n\nYou must append `?expand[]=recipient_settlement` as the query parameter to the fetch transfer request. This would return a `settlement` entity along with the `transfer` entity."}, "response": []}, {"name": "Fetch Payments of a Linked Account", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Account", "value": "{acc_id}"}], "url": {"raw": "https://api.razorpay.com/v1/payments?count=2", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments"], "query": [{"key": "count", "value": "2"}]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#fetch-payments-of-a-linked-account) to fetch a list of all the payments received by a linked account."}, "response": []}, {"name": "Fetch Reversals for a Transfer", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/transfers/{trf_id}/reversals", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers", "{trf_id}", "reversals"]}}, "response": []}], "description": "Know how to fetch details of transfers and settlements using APIs."}, {"name": "Linked Account Settlements", "item": [{"name": "Modify Transfer Settlement Hold", "request": {"method": "PATCH", "header": [{"key": "content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"on_hold\": 1,\n    \"on_hold_until\": **********\n}"}, "url": {"raw": "https://api.razorpay.com/v1/transfers/{trf_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers", "{trf_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#modify-settlement-hold-for-transfers) to modify the settlement configuration for a particular `transfer_id`. On a successful request, the API responds with the modified transfer entity."}, "response": []}, {"name": "Hold Settlements For Transfers", "request": {"method": "POST", "header": [{"key": "content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"transfers\": [\n        {\n            \"amount\": 100,\n            \"account\": \"acc_id\",//Please replace with appropriate ID.\n            \"currency\": \"INR\",\n            \"on_hold\": 1\n        }\n    ]\n}"}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/transfers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "transfers"]}, "description": "When transferring payment to an account, you can choose to put the transfer settlement on hold indefinitely or until a defined time. You can change these settings anytime via this [API](https://razorpay.com/docs/api/route/#hold-settlements-for-transfers) until the settlement is made. When you put a transfer settlement on hold, the settlement will not happen until you release it."}, "response": []}], "description": "Know how to hold transfer settlements and modify them using APIs."}, {"name": "Refunds and Reversals", "item": [{"name": "Refunds", "request": {"method": "POST", "header": [{"key": "content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 100,\n    \"reverse_all\": 1\n}"}, "url": {"raw": "https://api.razorpay.com/v1/payments/{pay_id}/refund", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "{pay_id}", "refund"]}, "description": "Use this [API](https://razorpay.com/docs/api/route/#refund-payments-and-reverse-transfer-from-a-linked) to refund payments to customers and reverse transfers from linked accounts."}, "response": []}, {"name": "Transfer Reversals", "request": {"method": "POST", "header": [{"key": "content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"amount\": 100\n}"}, "url": {"raw": "https://api.razorpay.com/v1/transfers/{trf_id}/reversals", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers", "{trf_id}", "reversals"]}}, "response": []}], "description": "Know how to refund payments to customers and reverse transfers from linked accounts."}, {"name": "Linked Account Onboarding", "item": [{"name": "Create a Linked Account", "request": {"method": "POST", "header": [{"key": "Content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n   \"email\":\"<EMAIL>\",\n   \"phone\":\"**********\",\n   \"type\":\"route\",\n   \"reference_id\":\"124124\",\n   \"legal_business_name\":\"Acme Corp\",\n   \"business_type\":\"partnership\",\n   \"contact_name\":\"<PERSON><PERSON><PERSON><PERSON>\",\n   \"profile\":{\n      \"category\":\"healthcare\",\n      \"subcategory\":\"clinic\",\n      \"addresses\":{\n         \"registered\":{\n            \"street1\":\"507, Koramangala 1st block\",\n            \"street2\":\"MG Road\",\n            \"city\":\"Bengaluru\",\n            \"state\":\"KARNATAKA\",\n            \"postal_code\":\"560034\",\n            \"country\":\"IN\"\n         }\n      }\n   },\n   \"legal_info\":{\n      \"pan\":\"**********\",\n      \"gst\":\"18AABCU9603R1ZM\"\n   }\n}"}, "url": {"raw": "https://api.razorpay.com/v2/accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts"]}}, "response": []}, {"name": "Update Linked Accounts", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"email\":\"<EMAIL>\",\n   \"legal_business_name\":\"Acme Corp V2\",\n   \"profile\":{\n      \"addresses\":{\n         \"operation\":{\n            \"street1\":\"5071, Koramangala 6th block\",\n            \"street2\":\"Kormanagala\",\n            \"city\":\"Bengaluru\",\n            \"state\":\"Karnataka\",\n            \"postal_code\":560047,\n            \"country\":\"IN\"\n         }\n      }\n   },\n   \"legal_info\":{\n      \"pan\":\"**********\",\n      \"gst\":\"10AABCU9603R1ZM\"\n   }\n}"}, "url": {"raw": "https://api.razorpay.com/v2/accounts/acc_GP4lfNA0iIMn5B", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "acc_GP4lfNA0iIMn5B"]}}, "response": []}, {"name": "<PERSON>tch a Linked Account", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v2/accounts/acc_GLGeLkU2JUeyDZ", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "acc_GLGeLkU2JUeyDZ"]}}, "response": []}, {"name": "Create a Stakeholder", "request": {"method": "POST", "header": [{"key": "Content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n   \"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\n   \"addresses\":{\n      \"residential\":{\n         \"street\":\"506, Koramangala 1st block\",\n         \"city\":\"Bengaluru\",\n         \"state\":\"Karnataka\",\n         \"postal_code\":\"560034\",\n         \"country\":\"IN\"\n      }\n   },\n   \"kyc\":{\n      \"pan\":\"**********\"\n   },\n   \"notes\":{\n      \"random_key_by_partner\":\"random_value\"\n   }\n}"}, "url": {"raw": "https://api.razorpay.com/v2/accounts/acc_GLGeLkU2JUeyDZ/stakeholders", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "acc_GLGeLkU2JUeyDZ", "stakeholders"]}}, "response": []}, {"name": "Update a Stakeholder", "request": {"method": "PATCH", "header": [{"key": "Content-type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n   \"addresses\":{\n      \"residential\":{\n         \"street\":\"507, Koramangala 1st block\",\n         \"city\":\"Bangalore\",\n         \"state\":\"Karnataka\",\n         \"postal_code\":\"560035\",\n         \"country\":\"IN\"\n      }\n   },\n   \"kyc\":{\n      \"pan\":\"**********\"\n   }\n}"}, "url": {"raw": "https://api.razorpay.com/v2/accounts/acc_GLGeLkU2JUeyDZ/stakeholders/sth_GOQ4Eftlz62TSL", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "acc_GLGeLkU2JUeyDZ", "stakeholders", "sth_GOQ4Eftlz62TSL"]}}, "response": []}, {"name": "Request a Product Configuration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"product_name\":\"route\",\n   \"tnc_accepted\":true\n}"}, "url": {"raw": "https://api.razorpay.com/v2/accounts/acc_HQVlm3bnPmccC0/products", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "acc_HQVlm3bnPmccC0", "products"]}}, "response": []}, {"name": "Update a Product Configuration", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"settlements\":{\n      \"account_number\":\"**********\",\n      \"ifsc_code\":\"HDFC0000317\",\n      \"beneficiary_name\":\"<PERSON><PERSON><PERSON><PERSON>\"\n   },\n   \"tnc_accepted\":true\n}"}, "url": {"raw": "https://api.razorpay.com/v2/accounts/acc_HQVlm3bnPmccC0/products/acc_prd_HEgNpywUFctQ9e/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "acc_HQVlm3bnPmccC0", "products", "acc_prd_HEgNpywUFctQ9e", ""]}}, "response": []}, {"name": "Fetch a Product Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v2/accounts/acc_HQVlm3bnPmccC0/products/acc_prd_HEgNpywUFctQ9e/", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "acc_HQVlm3bnPmccC0", "products", "acc_prd_HEgNpywUFctQ9e", ""]}}, "response": []}], "description": "You can transfer funds to a vendor by creating a Linked Account using APIs.\n\n## How to Create Linked Accounts\n\nBelow are the steps to integrate Route:\n\n1. Create a Linked Account:\n    \n\nCreate a Linked Account using the API. A unique `account_id` will be assigned to the created account.\n\n2\\. Create a Stakeholder:\n\nYou should now create a stakeholder using the `account_id`. A unique `stakeholder_id` will be assigned to the created stakeholder account.\n\n3\\. Request a Product Configuration:\n\nNow that both Linked Account and stakeholder are created, you should request a Route product configuration.\n\n4\\. Update a Product Configuration:\n\nYou should now trigger the update product configuration API with the bank account details of the Linked Account. The configuration will be activated if the information review is successful.\n\nKnow more about [LInked Account Onboarding](https://razorpay.com/docs/api/payments/route/linked-account-onboarding/)."}], "description": "Razorpay Route enables you to split payments received using the Razorpay Payment Gateway or other products (such as Payment Links, Payment Pages, Invoices, Subscriptions and Smart Collect) and transfer the funds to your vendors.\n\nKnow more about [Razorpay Route](https://razorpay.com/docs/route/).\n\n## List of APIs\n\n*   [Create Transfers from Orders](https://razorpay.com/docs/api/route/#create-transfers-from-orders)\n*   [Create Transfers from Payments](https://razorpay.com/docs/api/route/#create-transfers-from-payments)\n*   [Direct Transfers](https://razorpay.com/docs/api/route/#direct-transfers)\n*   [Fetch Transfers for a Payment](https://razorpay.com/docs/api/route/#fetch-transfers-for-a-payment)\n*   [Fetch Transfer for an Order](https://razorpay.com/docs/api/route/#fetch-transfer-for-an-order)\n*   [Fetch a Transfer](https://razorpay.com/docs/api/route/#fetch-a-transfer)\n*   [Fetch Transfers for a Settlement](https://razorpay.com/docs/api/route/#fetch-transfers-for-a-settlement)\n*   [Fetch Settlement Details](https://razorpay.com/docs/api/route/#fetch-settlement-details)\n*   [Fetch Payments of a Linked Account](https://razorpay.com/docs/api/route/#fetch-payments-of-a-linked-account)\n*   [Refund Payments and Reverse Transfer from a Linked Account](https://razorpay.com/docs/api/route/#refund-payments-and-reverse-transfer-from-a-linked)\n*   [Reverse Transfers from all Linked Accounts](https://razorpay.com/docs/api/route/#reverse-transfers-from-all-linked-accounts)\n*   [Hold Settlements For Transfers](https://razorpay.com/docs/api/route/#hold-settlements-for-transfers)\n*   [Modify Settlement Hold for Transfers](https://razorpay.com/docs/api/route/#modify-settlement-hold-for-transfers)"}, {"name": "RazorpayX APIs", "item": [{"name": "Step 1 - Contacts", "item": [{"name": "Create Contact", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON><PERSON> <PERSON>\",\n  \"email\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>@example.com\",\n  \"contact\": **********,\n  \"type\": \"employee\",\n  \"reference_id\": \"Acme Contact ID 12345\",\n  \"notes\":{\n    \"random_key_1\": \"Make it so.\",\n    \"random_key_2\": \"Tea. Earl Grey. Hot.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/contacts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts"]}, "description": "A contact is an entity to whom payouts can be made through supported modes such as UPI, IMPS, NEFT and RTGS.\n\nUse this [API](https://razorpay.com/docs/api/razorpayx/contacts/#create-a-contact) to create a contact."}, "response": []}, {"name": "Fetch all Contacts", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.razorpay.com/v1/contacts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/contacts/#fetch-all-contacts) to fetch all contacts."}, "response": []}, {"name": "Fetch Contact by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/contacts/{cont_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts", "{cont_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/contacts/#fetch-a-contact-by-id) to fetch the details of a specific contact."}, "response": []}, {"name": "Update Contact", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"contact\": **********,\n  \"type\": \"self\",\n  \"reference_id\": \"Acme Contact ID 12345 Updated\",\n  \"notes\": {\n    \"random_key_1\": \"Tea. Earl Grey. Hot.\",\n    \"random_key_2\": \"Tea. Earl Grey. Decaf...\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/contacts/{cont_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts", "{cont_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/contacts/#update-a-contact) to update a particular contact."}, "response": []}, {"name": "Activate or Deactivate a Contact", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"active\": false\n}"}, "url": {"raw": "https://api.razorpay.com/v1/contacts/{cont_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts", "{cont_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/contacts/#activate-or-deactivate-a-contact) to update a particular contact."}, "response": []}], "description": "A contact is an entity to whom payouts can be made through supported modes such as UPI, IMPS, NEFT and RTGS.\n\nSome important points:\n\n*   A new contact is created if any combination of the following details is unique: name, email, contact, type and reference_id.\n*   If all the above details match the details of an existing contact, the API returns details of the existing contact.\n*   Use the Update Contact API if you want to make changes to an existing contact.\n    \n\n## **List of APIs**\n\n*   [Create a Contact](https://razorpay.com/docs/api/razorpayx/contacts/#create-a-contact)\n*   [Update a Contact](https://razorpay.com/docs/api/razorpayx/contacts/#update-a-contact)\n*   [Activate or Deactivate a Contact](https://razorpay.com/docs/api/razorpayx/contacts/#activate-or-deactivate-a-contact)\n*   [Fetch all Contacts](https://razorpay.com/docs/api/razorpayx/contacts/#fetch-all-contacts)\n*   [Fetch a Contact by ID](https://razorpay.com/docs/api/razorpayx/contacts/#fetch-a-contact-by-id)"}, {"name": "Step 2 - Fund Accounts", "item": [{"name": "Create Fund Account - Bank Account", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"contact_id\": \"{cont_id}\",\n  \"account_type\": \"bank_account\",\n  \"bank_account\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"ifsc\": \"HDFC0009107\",\n    \"account_number\": \"**************\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/fund_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/fund-accounts/#bank-account) to create a Fund Account with Bank Account details"}, "response": []}, {"name": "Create Fund Account - Card", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"contact_id\": \"{cont_id}\",\n  \"account_type\": \"card\",\n  \"card\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"number\": \"****************\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/fund_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"]}, "description": "Use this API to create a Fund Account with card details."}, "response": []}, {"name": "Create - Fund Account - VPA", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"contact_id\": \"{cont_id}\",\n  \"account_type\": \"vpa\",\n  \"vpa\": {\n    \"address\": \"gaurav.kumar@exampleupi\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/fund_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/fund-accounts/#vpa-upi-id) to create a Fund Account with UPI VPA."}, "response": []}, {"name": "Fetch all Fund Accounts", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/fund_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"]}}, "response": []}, {"name": "Fetch Fund Account by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/fund_accounts/{fa_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts", "{fa_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/fund-accounts/#fetch-fund-account-details-by-id) to fetch a specific fund account."}, "response": []}, {"name": "Activate or Deactivate", "request": {"auth": {"type": "basic"}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"active\": false\n}"}, "url": {"raw": "https://api.razorpay.com/v1//fund_accounts/:id", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "", "fund_accounts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}], "description": "Fund accounts are accounts associated with a contact. Payouts are made to fund accounts.\n\nCurrently, RazorpayX supports 4 types of fund accounts:\n\n*   `bank_account`: Make payouts to a beneficiary's bank account via bank transfer using one of the available such as NEFT, IMPS or RTGS.\n*   `vpa` (Virtual Payment Address): Make payouts to a beneficiary's UPI ID via a UPI transfer.\n*   `cards`: Make payouts directly to a beneficiary's card via a bank transfer using one of the available such as NEFT or IMPS.\n*   `wallet`: Make payouts directly to a beneficiary's wallet. Currently, you can make payouts to a beneficiary via an Amazon Pay Gift Card.\n    \n\nKnow more about [Fund Accounts](https://razorpay.com/docs/api/razorpayx/fund-accounts/).\n\n## **List of APIs**\n\n*   [Create a Fund Account](https://razorpay.com/docs/api/razorpayx/fund-accounts/#create-a-fund-account)\n    *   [Bank Account](https://razorpay.com/docs/api/razorpayx/fund-accounts/#bank-account)\n    *   [VPA (UPI ID)](https://razorpay.com/docs/api/razorpayx/fund-accounts/#vpa-upi-id)\n*   [Activate or Deactivate a Fund Account](https://razorpay.com/docs/api/razorpayx/fund-accounts/#activate-or-deactivate-a-fund-account)[](https://razorpay.com/docs/api/razorpayx/fund-accounts/#path-parameters)\n*   [Fetch All Fund Accounts](https://razorpay.com/docs/api/razorpayx/fund-accounts/#fetch-all-fund-accounts)\n*   [Fetch Fund Account Details by ID](https://razorpay.com/docs/api/razorpayx/fund-accounts/#fetch-fund-account-details-by-id)"}, {"name": "Step 3 - Payouts", "item": [{"name": "Create Payout-Bank Account", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{apiPrivateAuth}}"}, {"key": "X-Dashboard-User-Id", "value": "**************", "disabled": true}, {"key": "X-Dashboard-User-Role", "value": "owner", "disabled": true}], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"****************\",\n  \"fund_account_id\": \"fa_00000000000001\",\n  \"amount\": 1000000,\n  \"currency\": \"INR\",\n  \"mode\": \"IMPS\",\n  \"purpose\": \"refund\",\n  \"queue_if_low_balance\": true,\n  \"reference_id\": \"Acme Transaction ID 12345\",\n  \"narration\": \"Acme Corp Fund Transfer\",\n  \"notes\": {\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payouts/#bank-accounts) to create a payout to a bank account."}, "response": []}, {"name": "Create Payout-VPA", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{apiPrivateAuth}}"}, {"key": "X-Dashboard-User-Id", "value": "**************", "disabled": true}, {"key": "X-Dashboard-User-Role", "value": "owner", "disabled": true}], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"****************\",\n  \"fund_account_id\": \"fa_00000000000001\",\n  \"amount\": 1000000,\n  \"currency\": \"INR\",\n  \"mode\": \"UPI\",\n  \"purpose\": \"refund\",\n  \"queue_if_low_balance\": true,\n  \"reference_id\": \"Acme Transaction ID 12345\",\n  \"narration\": \"Acme Corp Fund Transfer\",\n  \"notes\": {\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payouts/#vpa-upi-id) to create a payout to a VPA."}, "response": []}, {"name": "Fetch all Payouts", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.razorpay.com/v1/payouts?account_number={account_number}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"], "query": [{"key": "account_number", "value": "{account_number}"}]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payouts/#fetch-all-payouts) to fetch all the payouts created by you."}, "response": []}, {"name": "Fetch Payout by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payouts/{pout_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts", "{pout_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payouts/#fetch-a-payout-by-id) to fetch a specific payout created by you."}, "response": []}, {"name": "Cancel Queued Payouts", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payouts/{pout_id}/cancel", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts", "{pout_id}", "cancel"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payouts/#cancel-a-queued-payout) to cancel a specific payout created by you.\n\n**Handy Tips**:  \nYou can only cancel payouts that are in the `queued` state. It is not possible to cancel payouts that have any other status."}, "response": []}], "description": "A payout is the transfer of funds from your business account to a contact's fund account.\n\nKnow more about [Payouts](https://razorpay.com/docs/razorpayx/payouts/payout-overview/).\n\n## **List of APIs**\n\n*   [Create a Payout](https://razorpay.com/docs/api/razorpayx/payouts/#create-a-payout)\n    *   [Bank Accounts](https://razorpay.com/docs/api/razorpayx/payouts/#bank-accounts)\n    *   [VPA (UPI ID)](https://razorpay.com/docs/api/razorpayx/payouts/#vpa-upi-id)\n*   [Fetch All Payouts](https://razorpay.com/docs/api/razorpayx/payouts/#fetch-all-payouts)\n*   [Fetch a Payout by ID](https://razorpay.com/docs/api/razorpayx/payouts/#fetch-a-payout-by-id)\n*   [Cancel a Queued Payout](https://razorpay.com/docs/api/razorpayx/payouts/#cancel-a-queued-payout)"}, {"name": "Composite API", "item": [{"name": "Composite API - Bank Account", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_number\":\"account_number\",\n    \"amount\":100,\n    \"currency\":\"INR\",\n    \"mode\":\"NEFT\",\n    \"purpose\":\"refund\",\n    \"fund_account\":{\n        \"account_type\":\"bank_account\",\n        \"bank_account\":{\n            \"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\n            \"ifsc\":\"HDFC0001234\",\n            \"account_number\":\"****************\"\n        },\n        \"contact\":{\n            \"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\n            \"email\":\"<EMAIL>\",\n            \"contact\":\"**********\",\n            \"type\":\"employee\",\n            \"reference_id\":\"Acme Contact ID 12345\",\n            \"notes\":{\n                \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n                \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n            }\n        }\n    },\n    \"queue_if_low_balance\":true,\n    \"reference_id\":\"Acme Transaction ID 12345\",\n    \"narration\":\"Acme Corp Fund Transfer\",\n    \"notes\":{\n        \"notes_key_1\":\"Beam me up <PERSON><PERSON>\",\n        \"notes_key_2\":\"Engage\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/composite-api/#bank-account) to create a payout using contact and bank account details."}, "response": []}, {"name": "Composite API - VPA", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_number\":\"account_number\",\n    \"amount\":10000,\n    \"currency\":\"INR\",\n    \"mode\":\"UPI\",\n    \"purpose\":\"refund\",\n    \"fund_account\":{\n        \"account_type\":\"vpa\",\n        \"vpa\":{\n            \"address\":\"<PERSON><PERSON><PERSON><PERSON><PERSON>@exampleupi\"\n        },\n        \"contact\":{\n            \"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\n            \"email\":\"<EMAIL>\",\n            \"contact\":\"**********\",\n            \"type\":\"employee\",\n            \"reference_id\":\"Acme Contact ID 12345\",\n            \"notes\":{\n                \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n                \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n            }\n        }\n    },\n    \"queue_if_low_balance\":true,\n    \"reference_id\":\"Acme Transaction ID 12345\",\n    \"narration\":\"Acme Corp Fund Transfer\",\n    \"notes\":{\n        \"notes_key_1\":\"Beam me up <PERSON><PERSON>\",\n        \"notes_key_2\":\"Engage\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/composite-api/#vpa-upi-id) to create a payout using contact and VPA."}, "response": []}, {"name": "Composite API - Card", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_number\": \"account_number\",\n    \"amount\": 10000,\n    \"currency\": \"INR\",\n    \"mode\": \"IMPS\",\n    \"purpose\": \"refund\",\n    \"fund_account\": {\n        \"account_type\": \"card\",\n        \"card\": {\n            \"number\": \"****************\",\n            \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n        },\n        \"contact\": {\n            \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"contact\": \"**********\",\n            \"type\": \"employee\",\n            \"reference_id\": \"Acme Contact ID 12345\",\n            \"notes\": {\n                \"notes_key_1\": \"<PERSON>, <PERSON>, Hot\",\n                \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n            }\n        }\n    },\n    \"queue_if_low_balance\": true,\n    \"reference_id\": \"Acme Transaction ID 12345\",\n    \"narration\": \"Acme Corp Fund Transfer\",\n    \"notes\": {\n        \"notes_key_1\": \"Beam me up <PERSON><PERSON>\",\n        \"notes_key_2\": \"Engage\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts?X-Payout-Idempotency=", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"], "query": [{"key": "X-Payout-Idempotency", "value": "", "description": "123456"}]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/composite-api/#card) to create a payout using contact and card details."}, "response": []}, {"name": "Composite API - Phone", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "{{API_KEY_SECRET}}", "type": "string"}, {"key": "username", "value": "{{API_Key_ID}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"****************\",\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"mode\": \"UPI\",\n  \"purpose\": \"cashback\",\n  \"fund_account\": {\n    \"account_type\": \"mobile\",\n    \"mobile\": {\n      \"number\": \"**********\",\n      \"account_holder_name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n    },\n    \"contact\": {\n      \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"contact\": \"**********\",\n      \"type\": \"self\",\n      \"reference_id\": \"Acme Contact ID 12345\",\n      \"notes\": {\n        \"notes_key_1\": \"<PERSON>, <PERSON>, Hot\",\n        \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n      }\n    }\n  },\n  \"queue_if_low_balance\": true,\n  \"reference_id\": \"Acme Transaction ID 12345\",\n  \"narration\": \"Acme Corp Fund Transfer\",\n  \"notes\": {\n    \"notes_key_1\": \"Beam me up <PERSON><PERSON>\",\n    \"notes_key_2\": \"Engage\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}], "description": "The composite API allows you to make a payout using a single API call. This reduces the number of calls you would otherwise need to make to create a payout. The composite API also gives you the flexibility to either create a new contact and fund account or use existing contact and fund account details (contact_id and fund_account_id) to make a payout.\n\n  \nKnow more about the [Composite API](https://razorpay.com/docs/api/razorpayx/composite-api/)."}, {"name": "Payout Links", "item": [{"name": "Create Payout Link (using customer details)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"account_number\",\n  \"contact\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"type\": \"customer\",\n    \"contact\": \"*********\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"purpose\": \"refund\",\n  \"description\": \"Payout link for <PERSON><PERSON><PERSON><PERSON>\",\n  \"receipt\": \"Receipt No. 1\",\n  \"notes\": {\n    \"random_key_1\": \"Make it so.\",\n    \"random_key_2\": \"Tea. Earl Grey. Hot.\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payout-links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payout-links"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payout-links/#create-a-payout-link) to create a Payout Link."}, "response": []}, {"name": "Create Payout Link (using customer_id)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"account_number\",\n  \"contact\": {\n    \"id\": \"{cont_id}\"\n  },\n  \"amount\": 1000,\n  \"currency\": \"INR\",\n  \"purpose\": \"refund\",\n  \"description\": \"Payout link for <PERSON><PERSON><PERSON><PERSON>\",\n  \"receipt\": \"Receipt No. 1\",\n  \"notes\": {\n    \"random_key_1\": \"Make it so.\",\n    \"random_key_2\": \"Tea. Earl Grey. Hot.\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payout-links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payout-links"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payout-links/#create-a-payout-link) to create a Payout Link."}, "response": []}, {"name": "Fetch Payout Links", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payout-links", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payout-links"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payout-links/#fetch-all-payout-links) to fetch all Payout Links."}, "response": []}, {"name": "Fetch Payout Link by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payout-links/{poutlk_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payout-links", "{poutlk_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payout-links/#fetch-payout-link-by-id) to fetch a specific Payout Link."}, "response": []}, {"name": "Cancel Payout Link", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v1/payout-links/{poutlk_id}/cancel", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payout-links", "{poutlk_id}", "cancel"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/payout-links/#cancel-a-payout-link) to cancel a payout link."}, "response": []}], "description": "Payout Links enable you to make payouts to those contacts whose fund accounts details are not readily available with you. You can use these links to collect the customer's fund account details and then process refunds, reimbursement and cashback to them without additional follow up.\n\nKnow more about [Payout Links API](https://razorpay.com/docs/api/razorpayx/payout-links/).\n\n## **List of APIs**\n\n*   [Create a Payout Link](https://razorpay.com/docs/api/razorpayx/payout-links/#create-a-payout-link)\n*   [Fetch All Payout Links](https://razorpay.com/docs/api/razorpayx/payout-links/#fetch-all-payout-links)\n*   [Fetch Payout Link by ID](https://razorpay.com/docs/api/razorpayx/payout-links/#fetch-payout-link-by-id)\n*   [Cancel a Payout Link](https://razorpay.com/docs/api/razorpayx/payout-links/#cancel-a-payout-link)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Transaction Statements", "item": [{"name": "Fetch all Transactions", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.razorpay.com/v1/transactions?account_number={account_number}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transactions"], "query": [{"key": "account_number", "value": "{account_number}"}]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/transactions/#fetch-all-transactions) to fetch details of all transactions."}, "response": []}, {"name": "Fetch Transaction by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/transactions/{txn_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transactions", "{txn_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/transactions/#fetch-transaction-by-id) to fetch details of a specific transaction."}, "response": []}], "description": "The inflow of funds to your business account, payouts to a contact's fund account and reversals are all recorded as transactions against your business account. You can fetch details of a particular transaction or details of all transactions via [APIs](https://razorpay.com/docs/api/razorpayx/transactions/).\n\n## **List of APIs**\n\n*   [Fetch all Transactions](https://razorpay.com/docs/api/razorpayx/transactions/#fetch-all-transactions)\n*   [Fetch Transaction by ID](https://razorpay.com/docs/api/razorpayx/transactions/#fetch-transaction-by-id)"}, {"name": "Account Validation", "item": [{"name": "Balances", "item": [{"name": "<PERSON>tch Account <PERSON><PERSON>", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "{{API_KEY_SECRET}}", "type": "string"}, {"key": "username", "value": "{{API_Key_ID}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/banking_balances", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "banking_balances"]}, "description": "Visit [https://razorpay.com/docs/api/x/account-validation/balance-fetch-api/](https://razorpay.com/docs/api/x/account-validation/balance-fetch-api/) to know more."}, "response": []}]}, {"name": "Create Contact", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test\",\n  \"email\": \"<EMAIL>\",\n  \"contact\": \"**********\",\n  \"type\": \"vendor\",\n  \"reference_id\": \"12345\",\n  \"notes\": {\n    \"note_key\": \"Beam me up <PERSON><PERSON> Updated\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/contacts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts"]}, "description": "\t\t\t"}, "response": []}, {"name": "Fetch all Contacts", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.razorpay.com/v1/contacts?count=100", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts"], "query": [{"key": "from", "value": "**********", "disabled": true}, {"key": "to", "value": "**********", "disabled": true}, {"key": "count", "value": "100"}]}, "description": "Use this API to fetch all contacts."}, "response": []}, {"name": "Create Fund Account - Bank Account", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"contact_id\": \"{contact_id}\",\n  \"account_type\": \"bank_account\",\n  \"bank_account\": {\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"ifsc\": \"SBIN0040252\",\n    \"account_number\": \"***********\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/fund_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/account-validation/#create-fund-account) to create Fund Account with Bank Account details"}, "response": []}, {"name": "Fetch all Fund Accounts", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/fund_accounts?count=100", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"], "query": [{"key": "count", "value": "100"}]}, "description": "Use this API to fetch all fund accounts."}, "response": []}, {"name": "Create - Fund Account - VPA", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"contact_id\": \"{contact_id}\",\n  \"account_type\": \"vpa\",\n  \"vpa\": {\n    \"address\": \"gaurav-kumar@exampleupi\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/fund_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"]}, "description": "Create Fund Account with UPI VPA"}, "response": []}, {"name": "Validate Fund Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"{account_number}\",\n  \"fund_account\": {\n    \"id\": \"{fund_account_id}\"\n  },\n  \"amount\": 100,\n  \"currency\": \"INR\",\n  \"notes\": {\n    \"random_key_1\": \"Make it so.\",\n    \"random_key_2\": \"Tea. Earl Grey. Hot.\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/fund_accounts/validations", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts", "validations"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/account-validation/#validate-bank-account) to create an account validation transaction."}, "response": []}, {"name": "Fetch  Account Validation Transactions by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/fund_accounts/validations/{fav_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts", "validations", "{fav_id}"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/account-validation/#fetch-account-validation-transaction-by-id) to fetch details of a specific account validation transaction."}, "response": []}, {"name": "Fetch  all Account Validation Transactions", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v1/fund_accounts/validations", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts", "validations"]}, "description": "Use this [API](https://razorpay.com/docs/api/razorpayx/account-validation/#fetch-all-account-validation-transactions) to fetch all account validation transactions."}, "response": []}], "description": "Account validation is the process of validating your contact's bank account details or virtual payment address (VPA). Know more about this [API](https://razorpay.com/docs/api/razorpayx/account-validation/).\n\n## List of APIs\n\n*   [Validate a Bank Account](https://razorpay.com/docs/api/razorpayx/account-validation/#validate-a-bank-account)\n    *   [Create a Contact](https://razorpay.com/docs/api/razorpayx/account-validation/#create-a-contact)\n    *   [Create Fund Account](https://razorpay.com/docs/api/razorpayx/account-validation/#create-fund-account)\n    *   [Validate Bank Account](https://razorpay.com/docs/api/razorpayx/account-validation/#validate-bank-account)\n*   [Validate a VPA](https://razorpay.com/docs/api/razorpayx/account-validation/#validate-a-vpa)\n    *   [Create a Contact](https://razorpay.com/docs/api/razorpayx/account-validation/#create-a-contact-1)\n    *   [Create Fund Account](https://razorpay.com/docs/api/razorpayx/account-validation/#create-fund-account-1)\n    *   [Validate VPA](https://razorpay.com/docs/api/razorpayx/account-validation/#validate-vpa)\n*   [Fetch all Account Validation Transactions](https://razorpay.com/docs/api/razorpayx/account-validation/#fetch-all-account-validation-transactions)\n*   [Fetch Account Validation Transaction by ID](https://razorpay.com/docs/api/razorpayx/account-validation/#fetch-account-validation-transaction-by-id)"}, {"name": "Payouts to Cards", "item": [{"name": "Payouts to tokenised cards", "item": [{"name": "RazorpayX TokenHQ", "item": [{"name": "Create Fund Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"contact_id\": \"cont_00000000000001\",\n    \"account_type\": \"card\",\n    \"token\": \"token_4lsdksD31GaZ09\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}, {"name": "Create Payout", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_number\": \"****************\",\n    \"fund_account_id\": \"fa_00000000000001\",\n    \"amount\": 1000000,\n    \"currency\": \"INR\",\n    \"mode\": \"card\",\n    \"purpose\": \"refund\",\n    \"queue_if_low_balance\": true,\n    \"reference_id\": \"Acme Transaction ID 12345\",\n    \"narration\": \"Acme Corp Fund Transfer\",\n    \"notes\": {\n        \"notes_key_1\": \"<PERSON>, <PERSON>, <PERSON>\",\n        \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}], "description": "Tokenizing the card with Razorpay TokenHQ and then making a payout to the token via RazorpayX.\n\nstep1: Create a Fund account\n\nStep2: Create a Payout"}, {"name": "External Tokenization Providers", "item": [{"name": "Create Fund Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"contact_id\": \"cont_00000000000001\",\n    \"account_type\": \"card\",\n    \"card\": {\n        \"number\": \"****************\",\n        \"expiry_month\": \"12\",\n        \"expiry_year\": \"21\",\n        \"tokenised_card\": true,\n        \"token_provider\": \"payu\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}, {"name": "Create Payouts", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_number\": \"****************\",\n    \"fund_account_id\": \"fa_00000000000001\",\n    \"amount\": 1000000,\n    \"currency\": \"INR\",\n    \"mode\": \"card\",\n    \"purpose\": \"refund\",\n    \"queue_if_low_balance\": true,\n    \"reference_id\": \"Acme Transaction ID 12345\",\n    \"narration\": \"Acme Corp Fund Transfer\",\n    \"notes\": {\n        \"notes_key_1\": \"<PERSON>, <PERSON>, <PERSON>\",\n        \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}], "description": "Tokenizing the card with non-Razorpay token services and then making a payout to the token via RazorpayX.\n\nStep1: Create a Fund Account.\n\nStep2: Create a Payout."}], "description": "There are three ways to make a payout to a card, after collecting a card number from the customer:\n\n1.  Making a payout to it via RazorpayX payouts to cards API. Here, card numbers will not be saved.\n2.  Tokenizing the card with Razorpay TokenHQ and then making a payout to the token via RazorpayX.\n3.  Tokenizing the card with non-Razorpay token services and then making a payout to the token via RazorpayX."}, {"name": "Payouts to Cards", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_number\": \"****************\",\n    \"amount\": 1000000,\n    \"currency\": \"INR\",\n    \"mode\": \"NEFT\",\n    \"purpose\": \"refund\",\n    \"fund_account\": {\n        \"account_type\": \"card\",\n        \"card\": {\n            \"number\": \"***************\",\n            \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n        },\n        \"contact\": {\n            \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"contact\": \"**********\",\n            \"type\": \"employee\",\n            \"reference_id\": \"Acme Contact ID 12345\",\n            \"notes\": {\n                \"notes_key_1\": \"<PERSON>, <PERSON>, Hot\",\n                \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n            }\n        }\n    },\n    \"queue_if_low_balance\": true,\n    \"reference_id\": \"Acme Transaction ID 12345\",\n    \"narration\": \"Acme Corp Fund Transfer\",\n    \"notes\": {\n        \"notes_key_1\": \"Beam me up <PERSON><PERSON>\",\n        \"notes_key_2\": \"Engage\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}], "description": "With RazorpayX, you can make payouts directly to a credit card, debit card, or prepaid card. Making payout to a card is the same as normal payouts.\n\nPayouts via IMPS, NEFT, and UPI are supported on both RazorpayX virtual accounts and current accounts. Payouts via ‘card’ mode are supported only on virtual accounts."}, {"name": "Amazon-Payout Wallet", "item": [{"name": "Normal Payout", "item": [{"name": "Create a Contact", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\n  \"email\":\"<EMAIL>\",\n  \"contact\":\"**********\",\n  \"type\":\"employee\",\n  \"reference_id\":\"Acme Contact ID 12345\",\n  \"notes\":{\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/contacts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "contacts"]}}, "response": []}, {"name": "Create a Fund Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"contact_id\": \"cont_00000000000001\",\n  \"account_type\": \"wallet\",\n  \"wallet\": {\n    \"provider\": \"amazonpay\",\n    \"phone\": \"+91**********\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/fund_accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "fund_accounts"]}}, "response": []}, {"name": "Create a Payout", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"****************\",\n  \"fund_account_id\": \"fa_00000000000001\",\n  \"amount\": 1000000,\n  \"currency\": \"INR\",\n  \"mode\": \"amazonpay\",\n  \"purpose\": \"refund\",\n  \"queue_if_low_balance\": true,\n  \"reference_id\": \"Acme Transaction ID 12345\",\n  \"notes\": {\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}], "description": "To create a payout using the normal route you must:\n\n  \n1\\. Create a Contact\n\n2\\. Create a Fund Account\n\n3\\. Create a Payout"}, {"name": "Composite Payout", "item": [{"name": "Create a Payout", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"account_number\": \"****************\",\n    \"amount\": 1000000,\n    \"currency\": \"INR\",\n    \"mode\": \"amazonpay\",\n    \"purpose\": \"refund\",\n    \"fund_account\": {\n        \"account_type\": \"wallet\",\n        \"wallet\": {\n            \"provider\": \"amazonpay\",\n            \"phone\": \"+91**********\",\n            \"email\": \" <EMAIL>\",\n            \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n        },\n        \"contact\": {\n            \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"contact\": \"**********\",\n            \"type\": \"employee\",\n            \"reference_id\": \"Acme Contact ID 12345\",\n            \"notes\": {\n                \"notes_key_1\": \"<PERSON>, <PERSON>, Hot\",\n                \"notes_key_2\": \"<PERSON>, <PERSON>… decaf.\"\n            }\n        }\n    },\n    \"queue_if_low_balance\": true,\n    \"reference_id\": \"Acme Transaction ID 12345\",\n    \"narration\": \"Acme Corp Fund Transfer\",\n    \"notes\": {\n        \"notes_key_1\": \"Beam me up <PERSON>\",\n        \"notes_key_2\": \"Engage\"\n    }\n}"}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}}, "response": []}], "description": "The composite API allows you to make a payout using a single API call. This reduces the number of calls you would otherwise need to make to create a payout.\n\nComposite API also gives you the flexibility to either create a new contact and fund account or use the existing Contact and Fund account details (`contact_id` and `fund_account_id`) to make a payout."}], "description": "You can make payouts to multiple fund accounts of your contacts as necessary. One such supported fund account type is an Amazon Gift Card, which functions as the end customer's wallet.  \n  \nYou can make payouts to your end customer's Amazon Pay Gift Card using Payout Wallet APIs from your [RazorpayX Lite](/docs/x/get-started/account-types/razorpayx-lite). Each day, you can process payouts to the tune of ₹10,000 (per transaction) many times.  \nUsing Payout Wallet API, you are creating a contact whose fund account is an Amazon Pay Gift Card. The account type is identified as a `wallet` to which you can create and process the payouts.", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Idempotent Request", "request": {"method": "POST", "header": [{"key": "X-Payout-Idempotency", "value": "53cda91c-8f81-4e77-bbb9-7388f4ac6bf4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"account_number\": \"****************\",\n  \"fund_account_id\": \"fa_00000000000001\",\n  \"amount\": 1000000,\n  \"currency\": \"INR\",\n  \"mode\": \"IMPS\",\n  \"purpose\": \"refund\",\n  \"queue_if_low_balance\": true,\n  \"reference_id\": \"Acme Transaction ID 12345\",\n  \"narration\": \"Acme Corp Fund Transfer\",\n  \"notes\": {\n    \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\n    \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payouts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payouts"]}, "description": "To make a request idempotent, add the header `X-Payout-Idempotency` to the request and pass an idempotency key against it.\n\nAn idempotency key is a unique value generated by you. Our servers use this key to recognize subsequent retries of the same request.\n\nThe idempotency key (4-36 characters) can only contain alphabets, numbers, hyphens, underscores and space. For example, `53cda91c-8f81-4e77-bbb9-7388f4ac6bf4`. We recommend you generate the key using a version 4 (random) UUID generator.\n\nKnow more about [Idempotent Requests](https://razorpay.com/docs/api/razorpayx/idempotency/)."}, "response": []}], "description": "RazorpayX supercharges your business banking experience. We help business owners and finance teams automate manual, repetitive financial tasks and provide insights into money flow.\n\nUsing RazorpayX, you can process:\n\n*   Refunds for customers.\n*   Salary payouts with automated statutory payments such as PF and TDS.\n*   Vendor payouts with automated TDS payments.\n    \n\nYou can make timely payouts on RazorpayX using our developer-friendly [APIs](https://razorpay.com/docs/api/razorpayx/).\n\n## **List of APIs**\n\n*   [Account Validation](https://razorpay.com/docs/api/razorpayx/account-validation/)\n*   [Contacts](https://razorpay.com/docs/api/razorpayx/contacts/)\n*   [Fund Accounts](https://razorpay.com/docs/api/razorpayx/fund-accounts/)\n*   [Payouts](https://razorpay.com/docs/api/razorpayx/payouts/)\n*   [Payout to Cards](https://razorpay.com/docs/api/razorpayx/payouts-cards/)\n*   [Payouts - Composite API](https://razorpay.com/docs/api/razorpayx/composite-api/)\n*   [Payout Idempotency](https://razorpay.com/docs/api/razorpayx/idempotency/)\n*   [Payout Links](https://razorpay.com/docs/api/razorpayx/payout-links/)\n*   [Account Statement](https://razorpay.com/docs/api/razorpayx/transactions/)"}, {"name": "Partners APIs", "item": [{"name": "Sub-merchant Onboarding APIs", "item": [{"name": "Account APIs", "item": [{"name": "Create an Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"**********\",\n  \"legal_business_name\": \"Acme Corp\",\n  \"business_type\": \"partnership\",\n  \"customer_facing_business_name\": \"Example\",\n  \"profile\": {\n    \"category\": \"healthcare\",\n    \"subcategory\": \"clinic\",\n    \"addresses\": {\n      \"operation\": {\n        \"street1\": \"507, Koramangala 6th block\",\n        \"street2\": \"Kormanagala\",\n        \"city\": \"Bengaluru\",\n        \"state\": \"Karnataka\",\n        \"postal_code\": 560047,\n        \"country\": \"IN\"\n      },\n      \"registered\": {\n        \"street1\": \"507, Koramangala 1st block\",\n        \"street2\": \"MG Road\",\n        \"city\": \"Bengaluru\",\n        \"state\": \"Karnataka\",\n        \"postal_code\": 560034,\n        \"country\": \"IN\"\n      }\n    },\n    \"business_model\": \"Healthcare E-commerce platform\"\n  },\n  \"legal_info\": {\n    \"pan\": \"**********\",\n    \"gst\": \"18AABCU9603R1ZM\"\n  },\n  \"brand\": {\n    \"color\": \"FFFFFF\"\n  },\n  \"notes\": {\n    \"internal_ref_id\": \"123123\"\n  },\n  \"tos_acceptance\": {\n    \"date\": null,\n    \"ip\": null,\n    \"user_agent\": null\n  },\n  \"contact_info\": {\n    \"chargeback\": {\n      \"email\": \"<EMAIL>\"\n    },\n    \"refund\": {\n      \"email\": \"<EMAIL>\"\n    },\n    \"support\": {\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"**********\",\n      \"policy_url\": \"https://www.google.com\"\n    }\n  },\n  \"apps\": {\n    \"websites\": [\n      \"https://www.example.org\"\n    ],\n    \"android\": [\n      {\n        \"url\": \"playstore.example.org\",\n        \"name\": \"Example\"\n      }\n    ],\n    \"ios\": [\n      {\n        \"url\": \"appstore.example.org\",\n        \"name\": \"Example\"\n      }\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts"]}}, "response": []}, {"name": "<PERSON>tch an Account", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}"]}, "description": "Use this API to fetch the details of an account.\n\nKnow more about the [Fetch an Account API](https://razorpay.com/docs/api/partners/account-onboarding/#fetch-an-account)."}, "response": []}, {"name": "Update an Account", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"customer_facing_business_name\": \"ABCD Ltd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}"]}, "description": "Use this API to update the details of an account.\n\nKnow more about the [Update an Account](https://razorpay.com/docs/api/partners/account-onboarding/#update-an-account) API."}, "response": []}, {"name": "Delete an Account", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}"]}, "description": "Use this API to delete an account.\n\nKnow more about the [Delete an Account API](https://razorpay.com/docs/api/partners/account-onboarding/#delete-an-account)."}, "response": []}], "description": "You can use the Account APIs to create a sub-merchant account. After an account gets created, an `account_id` is assigned.\n\nYou can create, fetch, and update a sub-merchant account using these APIs."}, {"name": "Stakeholder APIs", "item": [{"name": "Create a Stakeholder", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"percentage_ownership\": 10,\n  \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"relationship\": {\n    \"director\": true,\n    \"executive\": false\n  },\n  \"phone\": {\n    \"primary\": \"**********\",\n    \"secondary\": \"**********\"\n  },\n  \"addresses\": {\n    \"residential\": {\n      \"street\": \"506, Koramangala 1st block\",\n      \"city\": \"Bengaluru\",\n      \"state\": \"Karnataka\",\n      \"postal_code\": \"560034\",\n      \"country\": \"IN\"\n    }\n  },\n  \"kyc\": {\n    \"pan\": \"**********\"\n  },\n  \"notes\": {\n    \"random_key_by_partner\": \"random_value\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/stakeholders", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "stakeholders"]}, "description": "Use this API to create a stakeholder.\n\nKnow more about the [Create a Stakeholder API](https://razorpay.com/docs/api/partners/stakeholder/#create-a-stakeholder)."}, "response": []}, {"name": "Fetch a Stakeholder", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/stakeholders/{stakeholder_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "stakeholders", "{stakeholder_id}"]}, "description": "Use this API to fetch a stakeholder.\n\nKnow more about the [Fetch a Stakeholder API](https://razorpay.com/docs/api/partners/stakeholder/#fetch-a-stakeholder)."}, "response": []}, {"name": "Fetch all Stakeholders", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/stakeholders", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "stakeholders"]}, "description": "Use this API to fetch all stakeholders.\n\nKnow more about the [Fetch all Stakeholders API](https://razorpay.com/docs/api/partners/stakeholder/#fetch-all-stakeholders)."}, "response": []}, {"name": "Update a Stakeholder", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"percentage_ownership\": 20,\n    \"name\": \"<PERSON><PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"relationship\": {\n        \"director\": false,\n        \"executive\": true\n    },\n    \"phone\": {\n        \"primary\": \"**********\",\n        \"secondary\": \"**********\"\n    },\n    \"addresses\": {\n        \"residential\": {\n            \"street\": \"507, Koramangala 1st block\",\n            \"city\": \"Bangalore\",\n            \"state\": \"Karnataka\",\n            \"postal_code\": \"560035\",\n            \"country\": \"IN\"\n        }\n    },\n    \"kyc\": {\n        \"pan\": \"**********\"\n    },\n    \"notes\": {\n        \"random_key_by_partner\": \"random_value2\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/stakeholders/{stakeholder_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "stakeholders", "{stakeholder_id}"]}, "description": "Use this API to update a stakeholder.\n\nKnow more about the [Update a Stakeholder API](https://razorpay.com/docs/api/partners/stakeholder/#update-a-stakeholder)."}, "response": []}], "description": "You can use the Stakeholders APIs to add a stakeholder for an account. Each stakeholder will have their KYC.\n\n**Handy Tip**  \n  \nA stakeholder can be a signatory or an owner of the business.\n\nYou can create, fetch and update stakeholders using [these](https://razorpay.com/docs/api/partners/stakeholder/) APIs."}, {"name": "Documents API", "item": [{"name": "Upload Stakeholder Documents", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "value": "@/Users/<USER>/Downloads/sample_uploaded.jpeg", "type": "default"}, {"key": "document_type", "value": "a<PERSON><PERSON>_front", "type": "default"}]}, "url": {"raw": "http://api.razorpay.in/v2/accounts/{account_id}/stakeholders/{stakeholder_id}/documents", "protocol": "http", "host": ["api", "razorpay", "in"], "path": ["v2", "accounts", "{account_id}", "stakeholders", "{stakeholder_id}", "documents"]}, "description": "Use the API to upload signatory documents for a stakeholder.\n\nKnow more about the [Upload Stakeholder Documents API](https://razorpay.com/docs/api/partners/upload-document/#upload-stakeholder-documents)."}, "response": []}, {"name": "Upload Account Documents", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "value": "@/Users/<USER>/Downloads/sample_uploaded.jpeg", "type": "default"}, {"key": "document_type", "value": "business_proof_url", "type": "default"}]}, "url": {"raw": "http://api.razorpay.in/v2/accounts/{account_id}/documents", "protocol": "http", "host": ["api", "razorpay", "in"], "path": ["v2", "accounts", "{account_id}", "documents"]}, "description": "Use the API to upload business documents for a sub-merchant's account.\n\nKnow more about the [Upload Account Documents API](https://razorpay.com/docs/api/partners/upload-document/#upload-account-documents)."}, "response": []}, {"name": "Fetch Account Documents", "request": {"method": "GET", "header": [], "url": {"raw": "http://api.razorpay.in/v2/accounts/{account_id}/documents", "protocol": "http", "host": ["api", "razorpay", "in"], "path": ["v2", "accounts", "{account_id}", "documents"]}, "description": "Use the API to fetch business documents for a sub-merchant's account.\n\nKnow more about the [Fetch Account Documents API](https://razorpay.com/docs/api/partners/upload-document/#fetch-account-documents)."}, "response": []}, {"name": "Fetch Stakeholders Documents", "request": {"method": "GET", "header": [], "url": {"raw": "http://api.razorpay.in/v2/accounts/{account_id}/stakeholders/{stakeholders_id}/documents", "protocol": "http", "host": ["api", "razorpay", "in"], "path": ["v2", "accounts", "{account_id}", "stakeholders", "{stakeholders_id}", "documents"]}, "description": "Use the API to fetch the files uploaded for a stakeholder.\n\nKnow more about the [Fetch Stakeholders Documents API](https://razorpay.com/docs/api/partners/upload-document/#fetch-stakeholder-documents)."}, "response": []}], "description": "Use the Document APIs to upload and fetch documents for the KYC verification process.\n\n**Watch Out!**\n\n*   The maximum supported file size for a JPG/PNG is 4MB.\n*   The maximum supported file size for a PDF is 2MB.\n*   Do not pass file URLs instead of uploading documents.\n*   If you have uploaded the document but mandatory field-level  \n    parameters are not passed in the API, you need to re-execute the  \n    Documents API with the same document and pass the fields.\n    \n\nKnow more about [Documents APIs.](https://razorpay.com/docs/api/partners/upload-document/)"}, {"name": "Product Configuration Request APIs", "item": [{"name": "Request a Product Configuration", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"product_name\": \"payment_gateway\",\n    \"tnc_accepted\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/products", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "products"]}, "description": "Use this API to request a product configuration. You can also use this API to accept terms and conditions for a product.\n\nKnow more about the [Request a Product Configuration API](https://razorpay.com/docs/api/partners/product-configuration/#request-a-product-configuration)."}, "response": []}, {"name": "Update a Product Configuration", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"notifications\": {\n        \"email\": [\n            \"<EMAIL>\",\n            \"<EMAIL>\"\n        ]\n    },\n    \"checkout\": {\n        \"theme_color\": \"#528FFF\"\n    },\n    \"refund\": {\n        \"default_refund_speed\": \"optimum\"\n    },\n    \"settlements\": {\n        \"account_number\": \"**********\",\n        \"ifsc_code\": \"HDFC0000317\",\n        \"beneficiary_name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n    },\n    \"tnc_accepted\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/products/{product_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "products", "{product_id}"]}, "description": "Use this API to update a product configuration. You can also use this API to accept terms and conditions.\n\nKnow more about the [Update a Product Configuration API](https://razorpay.com/docs/api/partners/product-configuration/#update-a-product-configuration)."}, "response": []}, {"name": "Fetch a Product Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/products/{product_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "products", "{product_id}"]}, "description": "Use this API to retrieve the details of a product for a given sub-merchant's account.\n\nKnow more about the [Fetch a Product Configuration API](https://razorpay.com/docs/api/partners/product-configuration/#fetch-a-product-configuration)."}, "response": []}], "description": "You can use the Product Configuration APIs to configure and activate Razorpay products for a sub-merchant account according to their requirements. For example, if you need our Payment Gateway product for all sub-merchants or Payment Gateway for one sub-merchant and Payment Link product for another sub-merchant, you can do so using this API. You can also accept terms and conditions using this API.\n\nYou can create, fetch and update product configuration requests using these APIs.\n\nKnow more about the [Product Configuration APIs](https://razorpay.com/docs/api/partners/product-configuration/)."}, {"name": "Webhooks API", "item": [{"name": "Create a Webhook", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"url\": \"https://en1mwkqoqioct.x.pipedream.net\",\n    \"alert_email\": \"<EMAIL>\",\n    \"secret\": \"12345\",\n    \"events\": [\n        \"payment.authorized\",\n        \"payment.failed\",\n        \"payment.captured\",\n        \"payment.dispute.created\",\n        \"refund.failed\",\n        \"refund.created\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/webhooks", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "webhooks"]}, "description": "Use this API to create a webhook.\n\nKnow more about the [Create a Webhook](https://razorpay.com/docs/api/partners/webhooks/#create-a-webhook) API."}, "response": []}, {"name": "Fetch a Webhook", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/webhooks/{webhook_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "webhooks", "{webhook_id}"]}, "description": "Use this API to fetch a webhook.\n\nKnow more about the [Fetch a Webhook](https://razorpay.com/docs/api/partners/webhooks/#fetch-a-webhook) API."}, "response": []}, {"name": "Fetch all Webhooks", "request": {"method": "GET", "header": [], "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/webhooks", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "webhooks"]}, "description": "Use this API to fetch all webhooks.\n\nKnow more about the [Fetch all Webhooks](https://razorpay.com/docs/api/partners/webhooks/#fetch-all-webhooks) API."}, "response": []}, {"name": "Update a Webhook", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"url\": \"https://www.linkedin.com\",\n    \"events\": [\n        \"refund.created\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/webhooks/{webhooks_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "webhooks", "{webhooks_id}"]}, "description": "Use this API to update a webhook.\n\nKnow more about the [Update a Webhook](https://razorpay.com/docs/api/partners/webhooks/#update-a-webhook) API."}, "response": []}, {"name": "Delete a Webhook", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"url\": \"https://www.linkedin.com\",\n    \"events\": [\n        \"refund.created\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/webhooks/{webhooks_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "webhooks", "{webhooks_id}"]}, "description": "Use this API to delete a webhook.\n\nKnow more about the [Delete a Webhook](https://razorpay.com/docs/api/partners/webhooks/#delete-a-webhook) API."}, "response": []}], "description": "You can use the Webhooks APIs to receive event notifications or subscribe to events happening in a sub-merchant's account for the integration installed.\n\nYou can create, fetch, update and delete webhooks using these APIs.\n\nKnow more about [Webhook Events](https://razorpay.com/docs/partners/api/#subscribe-to-webhooks) and check the [sample payloads](https://razorpay.com/docs/webhooks/payloads/partners) for Sub-Merchant Onboarding Events."}, {"name": "Terms and Conditions API", "item": [{"name": "[Deprecated] Accept Terms and Conditions", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"accepted\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v2/accounts/{account_id}/tnc", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "accounts", "{account_id}", "tnc"]}, "description": "**Deprecation Notice**\n\nWe deprecated this API in February 2022. We do not support this API anymore.\n\nUse the [Product Configuration APIs](https://razorpay.com/docs/api/partners/product-configuration/) to accept terms and conditions."}, "response": []}, {"name": "Fetch Terms and Conditions for a Sub-merchant", "request": {"method": "POST", "header": [], "url": {"raw": "https://api.razorpay.com/v2/products/{product_name}/tnc", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v2", "products", "{product_name}", "tnc"]}, "description": "Use this API to accept the terms and conditions for a merchant.\n\nKnow more about the [Fetch Terms and Conditions API](https://razorpay.com/docs/api/partners/terms-conditions/#fetch-terms-and-conditions-for-a-sub-merchant)."}, "response": []}], "description": "You can use the Terms and Conditions APIs to accept and fetch terms and conditions for a sub-merchant.\n\n# [Workflow🔗](https://razorpay.com/docs/api/partners/terms-conditions/#workflow)\n\nGiven below is the workflow:\n\n1\\. As a partner, it is your responsibility to show respective terms and conditions to the sub-merchants before you start onboarding to a product. The APIs used for it will be [Fetch Product Terms and Conditions API.](https://razorpay.com/docs/api/partners/terms-conditions/#fetch-terms-and-conditions-for-a-sub-merchant)\n\n2\\. You should display these web pages to your sub-merchants on your interface.\n\n3\\. Record the acceptance of terms and transmit that to Razorpay using either [Request a Product Configuration API](https://razorpay.com/docs/api/partners/product-configuration/#request-a-product-configuration) or [Update a Product Configuration API](https://razorpay.com/docs/api/partners/product-configuration/#update-a-product-configuration)."}], "description": "As a Partner, you can use the Sub-Merchant Onboarding APIs to onboard merchants from your platform. The sub-merchants can complete the KYC process in the Partner's platform itself and need not log into Razorpay's platform.\n\nRazorpay Sub-Merchant Onboarding APIs are RESTful. All our responses are returned in JSON.\n\nYou can use Razorpay APIs in two modes, `Test` and `Live`. The [API key](https://razorpay.com/docs/partners/onboarding-api/#generate-api-key) is different for each mode.\n\nTo complete the onboarding process for the sub-merchant, check the [KYC document requirements](https://razorpay.com/docs/partners/onboarding-api/appendix/#kyc-requirements)."}]}, {"name": "QR Codes", "item": [{"name": "Create a QR Code", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"type\": \"upi_qr\",\n    \"name\": \"Store_1\",\n    \"usage\": \"single_use\",\n    \"fixed_amount\": true,\n    \"payment_amount\": 300,\n    \"description\": \"For Store 1\",\n    \"customer_id\": \"cust_HKsR5se84c5LTO\",\n    \"close_by\": **********,\n    \"notes\": {\n        \"purpose\": \"Test UPI QR code notes\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/qr_codes", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "qr_codes"]}, "description": "You can create a QR Code using this [API](https://razorpay.com/docs/api/qr-codes/#create-a-qr-code)."}, "response": []}, {"name": "Close a QR Code", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/qr_codes/:qr_id/close", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "qr_codes", ":qr_id", "close"], "variable": [{"key": "qr_id", "value": null}]}, "description": "You can close a QR Code using this [API](https://razorpay.com/docs/api/qr-codes#close-a-qr-code)."}, "response": []}, {"name": "Fetch All QR Codes", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/qr_codes", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "qr_codes"]}, "description": "You can fetch all QR Codes using this [API](https://razorpay.com/docs/api/qr-codes#fetch-multiple-qr-codes)."}, "response": []}, {"name": "Fetch a QR Code", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/qr_codes/:qr_id", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "qr_codes", ":qr_id"], "variable": [{"key": "qr_id", "value": null}]}, "description": "You can fetch a specific QR Codes using this [API](https://razorpay.com/docs/api/qr-codes#fetch-a-qr-code)."}, "response": []}, {"name": "Fetch a QR Code for a Customer ID", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/qr_codes?customer_id={customer_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "qr_codes"], "query": [{"key": "customer_id", "value": "{customer_id}"}]}, "description": "You can fetch QR Codes for a specific customer using this [API](https://razorpay.com/docs/api/qr-codes#fetch-qr-code-for-a-customer-id)."}, "response": []}, {"name": "Fetch a QR Code for a Payment ID", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/qr_codes?payment_id={payment_id}", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "qr_codes"], "query": [{"key": "payment_id", "value": "{payment_id}"}]}, "description": "You can fetch QR Code for a specific payment using this [API](https://razorpay.com/docs/api/qr-codes#fetch-qr-code-for-a-payment-id)."}, "response": []}, {"name": "Fetch Payments for a QR Code", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/qr_codes/:qr_id/payments?count=2", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", "qr_codes", ":qr_id", "payments"], "query": [{"key": "count", "value": "2"}], "variable": [{"key": "qr_id", "value": null}]}, "description": "You can fetch payments for a specific QR Code using this [API](https://razorpay.com/docs/api/qr-codes#fetch-payments-for-a-qr-code)."}, "response": []}, {"name": "Create a Normal Refund", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"amount\": 10000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/payments/:pay_id/refund", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "payments", ":pay_id", "refund"], "variable": [{"key": "pay_id", "value": null}]}, "description": "Create a normal refund using this API. You can refund the entire amount or a partial amount. For example, for payment of INR100, you can refund the entire INR100 or INR50. \n\nKnow more about the <a href=\"https://razorpay.com/docs/api/refunds/#create-a-normal-refund\" target=\"_blank\">Create a Normal Refund API.</a>"}, "response": []}], "description": "Razorpay QR codes enables you to create QR codes and share them with customers to accept digital payments.\n\nYou can create, close and fetch QR codes using our APIs.\n\nKnow more about [QR Codes APIs](https://razorpay.com/docs/api/payments/qr-codes).\n\n## **List of APIs**\n\n*   [Create a QR Code](https://razorpay.com/docs/api/qr-codes#create-a-qr-code)\n*   [Close a QR Code](https://razorpay.com/docs/api/qr-codes#close-a-qr-code)\n*   [Fetch Multiple QR Codes](https://razorpay.com/docs/api/qr-codes#fetch-multiple-qr-codes)\n*   [Fetch a QR Code](https://razorpay.com/docs/api/qr-codes#fetch-a-qr-code)\n*   [Fetch QR Code for a Customer ID](https://razorpay.com/docs/api/qr-codes#fetch-qr-code-for-a-customer-id)\n*   [Fetch QR Code for a Payment ID](https://razorpay.com/docs/api/qr-codes#fetch-qr-code-for-a-payment-id)\n*   [Fetch Payments for a QR Code](https://razorpay.com/docs/api/qr-codes#fetch-payments-for-a-qr-code)\n*   [Refund a Payment](https://razorpay.com/docs/api/qr-codes#refund-a-payment)"}, {"name": "Bills", "item": [{"name": "Create a Bill - Retail", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"store_code\": \"V2_912\",\n  \"business_type\": \"retail\",\n  \"business_category\": \"retail_and_consumer_goods\",\n  \"customer\": {\n    \"contact\": \"**********\",\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n  },\n  \"order_service_type\": \"dine_in\",\n  \"order_number\": \"ORD212\",\n  \"receipt_timestamp\": 1722664800,\n  \"receipt_number\": \"INV001250010\",\n  \"receipt_type\": \"tax_invoice\",\n  \"receipt_delivery\": \"digital\",\n  \"qr_code_number\": \"T2322 ***********\",\n  \"cashier_code\": \"123\",\n  \"cashier_name\": \"<PERSON><PERSON><PERSON>\",\n  \"bar_code_number\": \"3412\",\n  \"billing_pos_number\": \"bn\",\n  \"pos_category\": \"traditional_pos\",\n  \"receipt_summary\": {\n    \"total_quantity\": 2,\n    \"sub_total_amount\": 150000,\n    \"total_tax_amount\": 37500,\n    \"total_tax_percent\": 25,\n    \"currency\": \"INR\",\n    \"net_payable_amount\": 187500,\n    \"payment_status\": \"success\",\n    \"delivery_charges\": 0,\n    \"cod_charges\": 0,\n    \"change_amount\": 0,\n    \"roundup_amount\": 0,\n    \"total_discount_percent\": 0,\n    \"total_discount_amount\": 0,\n    \"discounts\": [\n      {\n        \"name\": \"none\",\n        \"amount\": 0\n      }\n    ],\n    \"used_wallet_amount\": 0\n  },\n  \"taxes\": [\n    {\n      \"name\": \"CGST\",\n      \"percentage\": 10,\n      \"amount\": 15000\n    },\n    {\n      \"name\": \"SGST\",\n      \"percentage\": 10,\n      \"amount\": 15000\n    },\n    {\n      \"name\": \"cess\",\n      \"percentage\": 5,\n      \"amount\": 7500\n    }\n  ],\n  \"line_items\": [\n    {\n      \"name\": \"Acme Soap\",\n      \"quantity\": 1,\n      \"unit_amount\": 10000,\n      \"unit\": \"pc\",\n      \"hsn_code\": \"HC2000INCLT\",\n      \"product_code\": \"DRCO38\",\n      \"total_amount\": 10000,\n      \"taxes\": [\n        {\n          \"name\": \"CGST\",\n          \"percentage\": 10,\n          \"amount\": 1000\n        },\n        {\n          \"name\": \"SGST\",\n          \"percentage\": 10,\n          \"amount\": 1000\n        }\n      ]\n    },\n    {\n      \"name\": \"Acme Earphones\",\n      \"quantity\": 1,\n      \"unit_amount\": 80000,\n      \"unit\": \"pc\",\n      \"hsn_code\": \"HC2000INPPLT\",\n      \"product_code\": \"POPLT281\",\n      \"total_amount\": 80000,\n      \"taxes\": [\n        {\n          \"name\": \"CGST\",\n          \"percentage\": 10,\n          \"amount\": 8000\n        },\n        {\n          \"name\": \"SGST\",\n          \"percentage\": 10,\n          \"amount\": 8000\n        }\n      ]\n    }\n  ],\n  \"payments\": [\n    {\n      \"method\": \"paytm\",\n      \"currency\": \"INR\",\n      \"amount\": 90000,\n      \"payment_reference_id\": \"\",\n      \"financier_data\": {\n        \"reference\": \"\",\n        \"name\": \"v\"\n      }\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/bills", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "bills"]}, "description": "You can create a QR Code using this [API](https://razorpay.com/docs/api/bills/create)."}, "response": []}, {"name": "Create a Bill - Food & Beverage", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"store_code\": \"V2_912\",\n  \"business_type\": \"retail\",\n  \"business_category\": \"food_and_beverages\",\n  \"customer\": {\n    \"contact\": \"**********\",\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n  },\n  \"order_service_type\": \"dine_in\",\n  \"order_number\": \"ORD212\",\n  \"receipt_timestamp\": 1722664800,\n  \"receipt_number\": \"INV00125006\",\n  \"receipt_type\": \"tax_invoice\",\n  \"receipt_delivery\": \"digital\",\n  \"qr_code_number\": \"T2322 ***********\",\n  \"cashier_code\": \"123\",\n  \"cashier_name\": \"<PERSON><PERSON><PERSON>\",\n  \"bar_code_number\": \"3412\",\n  \"billing_pos_number\": \"bn\",\n  \"pos_category\": \"traditional_pos\",\n  \"receipt_summary\": {\n    \"total_quantity\": 2,\n    \"sub_total_amount\": 150000,\n    \"total_tax_amount\": 37500,\n    \"total_tax_percent\": 25,\n    \"currency\": \"INR\",\n    \"net_payable_amount\": 187500,\n    \"payment_status\": \"success\",\n    \"delivery_charges\": 0,\n    \"cod_charges\": 0,\n    \"change_amount\": 0,\n    \"roundup_amount\": 0,\n    \"total_discount_percent\": 0,\n    \"total_discount_amount\": 0,\n    \"discounts\": [\n      {\n        \"name\": \"none\",\n        \"amount\": 0\n      }\n    ],\n    \"used_wallet_amount\": 0\n  },\n  \"taxes\": [\n    {\n      \"name\": \"CGST\",\n      \"percentage\": 10,\n      \"amount\": 15000\n    },\n    {\n      \"name\": \"SGST\",\n      \"percentage\": 10,\n      \"amount\": 15000\n    },\n    {\n      \"name\": \"cess\",\n      \"percentage\": 5,\n      \"amount\": 7500\n    }\n  ],\n  \"line_items\": [\n    {\n      \"name\": \"Pop-corn Large TUB\",\n      \"quantity\": 1,\n      \"unit_amount\": 100000,\n      \"unit\": \"pc\",\n      \"hsn_code\": \"HC2000INCLT\",\n      \"product_code\": \"DRCO38\",\n      \"total_amount\": 120000,\n      \"taxes\": [\n        {\n          \"name\": \"CGST\",\n          \"percentage\": 10,\n          \"amount\": 10000\n        },\n        {\n          \"name\": \"SGST\",\n          \"percentage\": 10,\n          \"amount\": 10000\n        }\n      ]\n    },\n    {\n      \"name\": \"Coke 650ML\",\n      \"quantity\": 1,\n      \"unit_amount\": 50000,\n      \"unit\": \"pc\",\n      \"hsn_code\": \"HC2000INPPLT\",\n      \"product_code\": \"POPLT281\",\n      \"total_amount\": 60000,\n      \"taxes\": [\n        {\n          \"name\": \"CGST\",\n          \"percentage\": 10,\n          \"amount\": 5000\n        },\n        {\n          \"name\": \"SGST\",\n          \"percentage\": 10,\n          \"amount\": 5000\n        }\n      ]\n    }\n  ],\n  \"payments\": [\n    {\n      \"method\": \"paytm\",\n      \"currency\": \"INR\",\n      \"amount\": 187500,\n      \"payment_reference_id\": \"\",\n      \"financier_data\": {\n        \"reference\": \"\",\n        \"name\": \"v\"\n      }\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/bills", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "bills"]}, "description": "You can create a QR Code using this [API](https://razorpay.com/docs/api/bills/create)."}, "response": []}, {"name": "Create a Bill - Events", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"store_code\": \"V2_911\",\n  \"business_type\": \"retail\",\n  \"business_category\": \"events\",\n  \"customer\": {\n    \"contact\": \"**********\",\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\"\n  },\n  \"receipt_timestamp\": 1722664800,\n  \"cashier_name\": \"<PERSON><PERSON><PERSON>\",\n  \"cashier_code\": \"420\",\n  \"receipt_number\": \"INV00124912\",\n  \"receipt_type\": \"tax_invoice\",\n  \"receipt_delivery\": \"digital\",\n  \"qr_code_number\": \"T2322 ***********\",\n  \"billing_pos_number\": \"bn\",\n  \"pos_category\": \"traditional_pos\",\n  \"receipt_summary\": {\n    \"total_quantity\": 1,\n    \"sub_total_amount\": 50100,\n    \"total_tax_amount\": 500,\n    \"currency\": \"INR\",\n    \"net_payable_amount\": 50600,\n    \"payment_status\": \"success\",\n    \"delivery_charges\": 0,\n    \"cod_charges\": 0,\n    \"change_amount\": 0,\n    \"roundup_amount\": 0,\n    \"total_discount_percent\": 0,\n    \"total_discount_amount\": 0,\n    \"discounts\": [\n      {\n        \"name\": \"none\",\n        \"amount\": 0\n      }\n    ],\n    \"used_wallet_amount\": 0\n  },\n  \"taxes\": [\n    {\n      \"name\": \"cgst\",\n      \"percentage\": 20,\n      \"amount\": 200\n    },\n    {\n      \"name\": \"sgst\",\n      \"percentage\": 20,\n      \"amount\": 200\n    },\n    {\n      \"name\": \"cess\",\n      \"percentage\": 10,\n      \"amount\": 100\n    }\n  ],\n  \"line_items\": [\n    {\n      \"name\": \"3D Charge\",\n      \"quantity\": 1,\n      \"total_amount\": 1000\n    }\n  ],\n  \"payments\": [\n    {\n      \"method\": \"paytm\",\n      \"currency\": \"INR\",\n      \"amount\": 50000,\n      \"payment_reference_id\": \"\",\n      \"financier_data\": {\n        \"reference\": \"\",\n        \"name\": \"v\"\n      }\n    }\n  ],\n  \"event\": {\n    \"name\": \"My Party\",\n    \"start_timestamp\": 1722911400,\n    \"end_timestamp\": 1722924000,\n    \"location\": \"B-wing\",\n    \"room\": \"Auditorium 1\",\n    \"seats\": [\n      \"gold b1\",\n      \"gold b2\",\n      \"gold b3\"\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/bills", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "bills"]}, "description": "You can create a QR Code using this [API](https://razorpay.com/docs/api/bills/create)."}, "response": []}, {"name": "Update a Bill", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"store_code\": \"T102\",\n  \"customer\": {\n    \"contact\": \"9000090001\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"receipt_type\": \"tax_invoice\",\n  \"receipt_timestamp\": 1907416999,\n  \"receipt_delivery\": \"digital\",\n  \"line_items\": [\n    {\n      \"name\": \"T-Shirt\",\n      \"quantity\": 1,\n      \"total_amount\": 100000\n    }\n  ],\n  \"receipt_summary\": {\n    \"total_quantity\": 1,\n    \"sub_total_amount\": 100000,\n    \"currency\": \"INR\",\n    \"net_payable_amount\": 124000,\n    \"payment_status\": \"paid\"\n  },\n  \"taxes\": [\n    {\n      \"name\": \"cgst\",\n      \"percentage\": 1200,\n      \"amount\": 12000\n    },\n    {\n      \"name\": \"sgst\",\n      \"percentage\": 1200,\n      \"amount\": 12000\n    }\n  ],\n  \"payments\": [\n    {\n      \"method\": \"Bank Transfer\",\n      \"amount\": 124000,\n      \"currency\": \"INR\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/bills/:bill_id", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "bills", ":bill_id"], "variable": [{"key": "bill_id", "value": ""}]}, "description": "You can create a QR Code using this [API](https://razorpay.com/docs/api/bills/update/)."}, "response": []}, {"name": "Delete a Bill", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.razorpay.com/v1/bills/:bill_id", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "bills", ":bill_id"], "variable": [{"key": "bill_id", "value": ""}]}, "description": "You can create a QR Code using this [API](https://razorpay.com/docs/api/bills/update/)."}, "response": []}], "description": "You can create digital Bills and send it to your customers.\n\n**List of APIs:**\n\n- [Create a Bill](https://razorpay.com/docs/api/bills/create)\n    \n- [Update a Bill](https://razorpay.com/docs/api/bills/update)\n    \n- [Delete a Bill](https://razorpay.com/docs/api/bills/delete)"}, {"name": "Idempotent Request", "request": {"method": "POST", "header": [{"key": "content-type", "value": "application/json", "type": "text"}, {"key": "X-Transfer-Idempotency", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"account\": \"acc_CPRsN1LkFccllA\",\n  \"amount\": 100,\n  \"currency\": \"INR\"\n}"}, "url": {"raw": "https://api.razorpay.com/v1/transfers", "protocol": "https", "host": ["api", "razorpay", "com"], "path": ["v1", "transfers"]}}, "response": []}], "auth": {"type": "basic", "basic": [{"key": "password", "value": "{{API_KEY_SECRET}}", "type": "string"}, {"key": "username", "value": "{{API_Key_ID}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "API_Key_ID", "value": "{{api_key}}"}, {"key": "API_KEY_SECRET", "value": "{{api_secret}}"}]}