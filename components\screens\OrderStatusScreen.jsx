import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Image,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

const { width, height } = Dimensions.get("window");

const OrderStatusScreen = ({ 
  navigation, 
  orderData = {
    orderCode: "ADF27",
    restaurant: {
      name: "Flywheel Restaurant",
      location: "Kesavadasapuram",
      distance: "3 Km",
      logo: null
    },
    pickupWindow: "6:30-8:30 pm, Today",
    orderId: "YX12358WD",
    items: "1x Meal Bag",
    total: "Rs 150",
    paymentMethod: "UPI",
    orderStatus: "Processing",
    pickupCountdown: "01h:23m:45s"
  },
  onNeedHelp,
  onCancelOrder,
  onAskFriend,
  onBackToHome 
}) => {
  const [countdown, setCountdown] = useState(orderData.pickupCountdown);

  useEffect(() => {
    // Countdown timer logic
    const timer = setInterval(() => {
      // This would normally calculate the actual countdown
      // For demo purposes, we'll keep it static
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleBackToHome = () => {
    if (onBackToHome) {
      onBackToHome();
    } else if (navigation) {
      navigation.navigate("TabNavigator", { screen: "Discover" });
    }
  };

  const handleNeedHelp = () => {
    if (onNeedHelp) {
      onNeedHelp();
    } else if (navigation) {
      navigation.navigate("ContactUs");
    }
  };

  const handleCancelOrder = () => {
    if (onCancelOrder) {
      onCancelOrder();
    } else {
      // Default cancel order behavior
      console.log("Cancel order requested");
      handleBackToHome();
    }
  };

  const handleAskFriend = () => {
    if (onAskFriend) {
      onAskFriend();
    } else {
      // Default ask friend behavior
      console.log("Ask friend to pick up");
    }
  };

  const renderOrderCodeLetters = () => {
    return orderData.orderCode.split('').map((char, index) => (
      <View key={index} style={styles.codeLetterContainer}>
        <Text style={styles.codeLetter}>{char}</Text>
      </View>
    ));
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#F8F9FA" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackToHome}>
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Your Order</Text>
      </View>

      <View style={styles.content}>
        {/* Order Code Section */}
        <View style={styles.orderCodeSection}>
          <Text style={styles.orderCodeLabel}>ORDER CODE</Text>
          <View style={styles.orderCodeContainer}>
            {renderOrderCodeLetters()}
          </View>
        </View>

        {/* Restaurant Info */}
        <View style={styles.restaurantSection}>
          <View style={styles.restaurantInfo}>
            <View style={styles.restaurantLogo}>
              <View style={styles.logoPlaceholder}>
                <Text style={styles.logoText}>Imperial{"\n"}Kitchen</Text>
              </View>
            </View>
            <View style={styles.restaurantDetails}>
              <Text style={styles.restaurantName}>{orderData.restaurant.name}</Text>
              <Text style={styles.restaurantLocation}>
                {orderData.restaurant.location}{"\n"}{orderData.restaurant.distance}
              </Text>
            </View>
            <TouchableOpacity style={styles.locationIcon}>
              <Ionicons name="location-outline" size={24} color="#6C5CE7" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Order Details */}
        <View style={styles.orderDetailsSection}>
          <View style={styles.detailRow}>
            <View>
              <Text style={styles.detailLabel}>Pickup Window</Text>
              <Text style={styles.detailValue}>{orderData.pickupWindow}</Text>
            </View>
            <View style={styles.rightAlign}>
              <Text style={styles.detailLabel}>Order ID</Text>
              <Text style={styles.detailValue}>{orderData.orderId}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View>
              <Text style={styles.detailLabel}>Items</Text>
              <Text style={styles.detailValue}>{orderData.items}</Text>
            </View>
            <View style={styles.rightAlign}>
              <Text style={styles.detailLabel}>Total</Text>
              <Text style={styles.detailValue}>{orderData.total}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View>
              <Text style={styles.detailLabel}>Payment Method</Text>
              <Text style={styles.detailValue}>{orderData.paymentMethod}</Text>
            </View>
            <View style={styles.rightAlign}>
              <Text style={styles.detailLabel}>Order Status</Text>
              <Text style={styles.detailValue}>{orderData.orderStatus}</Text>
            </View>
          </View>
        </View>

        {/* Pickup Countdown */}
        <View style={styles.countdownSection}>
          <TouchableOpacity style={styles.countdownButton}>
            <Text style={styles.countdownLabel}>Your Pickup Window Starts in</Text>
            <Text style={styles.countdownTime}>{countdown}</Text>
          </TouchableOpacity>
        </View>

        {/* Ask Friend Button */}
        <TouchableOpacity style={styles.askFriendButton} onPress={handleAskFriend}>
          <Text style={styles.askFriendText}>Ask a friend to pick up</Text>
        </TouchableOpacity>

        {/* Bottom Actions */}
        <View style={styles.bottomActions}>
          <TouchableOpacity style={styles.helpButton} onPress={handleNeedHelp}>
            <Ionicons name="headset-outline" size={24} color="#6C5CE7" />
            <Text style={styles.helpText}>Need Help</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.cancelButton} onPress={handleCancelOrder}>
            <Ionicons name="trash-outline" size={24} color="#E53E3E" />
            <Text style={styles.cancelText}>Cancel Order</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8F9FA",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: "#F8F9FA",
  },
  backButton: {
    padding: 8,
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#6C5CE7",
  },
  content: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  orderCodeSection: {
    alignItems: "center",
    marginBottom: 30,
  },
  orderCodeLabel: {
    fontSize: 12,
    fontWeight: "600",
    color: "#6C5CE7",
    letterSpacing: 2,
    marginBottom: 15,
  },
  orderCodeContainer: {
    flexDirection: "row",
    gap: 8,
  },
  codeLetterContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#F7FAFC",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  codeLetter: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#6C5CE7",
  },
  restaurantSection: {
    marginBottom: 30,
  },
  restaurantInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  restaurantLogo: {
    marginRight: 15,
  },
  logoPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#E53E3E",
    justifyContent: "center",
    alignItems: "center",
  },
  logoText: {
    fontSize: 8,
    fontWeight: "bold",
    color: "#FFFFFF",
    textAlign: "center",
    lineHeight: 10,
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#2D3748",
    marginBottom: 4,
  },
  restaurantLocation: {
    fontSize: 14,
    color: "#718096",
    lineHeight: 18,
  },
  locationIcon: {
    padding: 8,
  },
  orderDetailsSection: {
    marginBottom: 30,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  rightAlign: {
    alignItems: "flex-end",
  },
  detailLabel: {
    fontSize: 14,
    color: "#718096",
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#2D3748",
  },
  countdownSection: {
    marginBottom: 20,
  },
  countdownButton: {
    backgroundColor: "#6C5CE7",
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 15,
    alignItems: "center",
  },
  countdownLabel: {
    fontSize: 14,
    color: "#FFFFFF",
    marginBottom: 8,
  },
  countdownTime: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  askFriendButton: {
    borderWidth: 2,
    borderColor: "#6C5CE7",
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 30,
    alignItems: "center",
    marginBottom: 40,
  },
  askFriendText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#6C5CE7",
  },
  bottomActions: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingBottom: 30,
  },
  helpButton: {
    alignItems: "center",
  },
  helpText: {
    fontSize: 14,
    color: "#6C5CE7",
    marginTop: 4,
  },
  cancelButton: {
    alignItems: "center",
  },
  cancelText: {
    fontSize: 14,
    color: "#E53E3E",
    marginTop: 4,
  },
});

export default OrderStatusScreen;
