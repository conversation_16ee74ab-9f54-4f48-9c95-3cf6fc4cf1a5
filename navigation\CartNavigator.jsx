import { createStackNavigator } from "@react-navigation/stack";

import Cart from "../screens/Cart";

const Stack = createStackNavigator();

const CartNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="Cart">
      <Stack.Screen
        name="Cart"
        component={Cart}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default CartNavigator;
