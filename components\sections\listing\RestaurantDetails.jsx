import { View, Text, StyleSheet, Image } from "react-native";
import React from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { AppImages } from "../../../utils/AppImages";
import { Icons } from "../../../utils/svgs";
import { AppColors } from "../../../utils/AppColors";

const RestaurantDetails = ({
  logo,
  name,
  mealType,
  actualPrice,
  price,
  pickupWindow,
}) => {
  return (
    <View style={styles.colmun}>
      <View style={styles.cardContainer}>
        <Image source={{ uri: logo }} style={styles.logo} />

        <View style={styles.infoContainer}>
          <Text style={styles.restaurantName}>{name}</Text>
          <View style={styles.row}>
            <Icons.BagIcon />
            <Text style={{ color: AppColors.SubColor }}>{mealType}</Text>
          </View>
        </View>

        <View style={styles.colmun}>
          <Text style={styles.price}>Rs{actualPrice}</Text>
          <View style={styles.rowCenter}>
            <Text style={{ fontSize: 18, color: "#5F22D9" }}>₹</Text>
            <Text style={styles.price1}>{price}</Text>
          </View>
        </View>
      </View>
      <View style={styles.rowLeft}>
        <Icons.ClockIcon />
        <Text style={styles.texts1}>Pickup Window:</Text>
        <Text style={styles.texts2}>{pickupWindow}</Text>
      </View>
    </View>
  );
};

export default RestaurantDetails;

const styles = StyleSheet.create({
  colmun: {
    flexDirection: "column",
  },
  cardContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 5,
    borderRadius: 12,
  },
  logo: {
    width: 54,
    height: 54,
    borderRadius: 100,
  },
  infoContainer: {
    flex: 1,
    marginLeft: 14,
  },
  restaurantName: {
    fontSize: 15,
    fontFamily: "Poppins_500Medium",
    color: "#545556",
  },
  price: {
    fontSize: 14,
    color: "#A0A0A0",
    textDecorationLine: "line-through",
    textAlign: "right",
    marginRight: 5,
  },
  rowCenter: {
    flexDirection: "row",
    gap: 3,
    alignItems: "flex-end",
  },
  logoCurrency: {
    height: 15,
    width: 15,
    resizeMode: "contain",
  },
  price1: {
    fontSize: 30,
    fontWeight: "bold",
    color: "#5F22D9",
  },
  rowLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
    paddingLeft: 15,
    paddingTop: 10,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  texts1: {
    fontSize: 14,
    color: "#C1C0C0",
  },
  texts2: {
    fontSize: 14,
    color: "#5F22D9",
  },
});
