// Razorpay Configuration
// Note: Replace these with your actual Razorpay keys

export const RAZORPAY_CONFIG = {
  // Test keys - replace with live keys for production
  KEY_ID: "rzp_test_caxIPIS7jeUeji", // Replace with your actual Razorpay Key ID

  // Company/App details
  COMPANY_NAME: "Plenti",
  COMPANY_LOGO:
    "https://framerusercontent.com/images/TOQOo63Dz9p3waeO0ZQwlcbpmyQ.png", // Replace with actual logo URL

  // Theme configuration
  THEME_COLOR: "#5F22D9",

  // Currency
  CURRENCY: "INR",

  // Payment description
  PAYMENT_DESCRIPTION: "Plenti Order Payment",
};

// Environment-based configuration
export const getRazorpayConfig = () => {
  // You can add environment-specific logic here
  // For example, different keys for development vs production
  
  if (__DEV__) {
    // Development configuration
    return {
      ...RAZORPAY_CONFIG,
      KEY_ID: "rzp_test_caxIPIS7jeUeji", // Development key
    };
  } else {
    // Production configuration
    return {
      ...RAZORPAY_CONFIG,
      KEY_ID: 'rzp_live_your_live_key_here', // Production key
    };
  }
};
