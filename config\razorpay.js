// Razorpay Configuration
// Note: Replace these with your actual Razorpay keys

export const RAZORPAY_CONFIG = {
  // Test keys - replace with live keys for production
  KEY_ID: 'rzp_test_your_key_here', // Replace with your actual Razorpay Key ID
  
  // Company/App details
  COMPANY_NAME: 'Plenti',
  COMPANY_LOGO: 'https://your-logo-url.com/logo.png', // Replace with actual logo URL
  
  // Theme configuration
  THEME_COLOR: '#5F22D9',
  
  // Currency
  CURRENCY: 'INR',
  
  // Payment description
  PAYMENT_DESCRIPTION: 'Plenti Order Payment',
};

// Environment-based configuration
export const getRazorpayConfig = () => {
  // You can add environment-specific logic here
  // For example, different keys for development vs production
  
  if (__DEV__) {
    // Development configuration
    return {
      ...RAZORPAY_CONFIG,
      KEY_ID: 'rzp_test_your_dev_key_here', // Development key
    };
  } else {
    // Production configuration
    return {
      ...RAZORPAY_CONFIG,
      KEY_ID: 'rzp_live_your_live_key_here', // Production key
    };
  }
};
