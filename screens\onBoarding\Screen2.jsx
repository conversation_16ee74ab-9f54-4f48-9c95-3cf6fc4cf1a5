import React from "react";
import { View, StyleSheet, Text, Image } from "react-native";

import { AppColors } from "../../utils/AppColors";
import { AppImages } from "../../utils/AppImages";

const Screen2 = () => {
  return (
    <View style={styles.container}>
      <Image source={AppImages.OB2_BACKGROUND} style={styles.backgroundImage} />
      <View style={styles.topLeftContainer}>
        <Text style={styles.headingText}>
          Meals to Baked Goods to Groceries
        </Text>
      </View>
      <Image source={AppImages.OB2_DONUTS} style={styles.centerImage} />
    </View>
  );
};

export default Screen2;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppColors.primaryColor,
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "50%",
    resizeMode: "cover",
  },
  topLeftContainer: {
    position: "absolute",
    top: 60,
    left: 20,
    zIndex: 1,
    paddingHorizontal: 10,
  },
  headingText: {
    fontSize: 34,
    fontFamily: "Poppins_700Bold",
    color: "#FFFFFF",
  },
  centerImage: {
    top: 40,
    width: "100%",
    height: "100%",
    resizeMode: "contain",
    zIndex: 2,
  },

});
