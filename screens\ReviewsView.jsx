import React, { useEffect, useRef, useState } from "react";
import { View, Text, StyleSheet, Animated, ScrollView } from "react-native";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import StarRating from "../components/sections/rattings/RattingsStars";
import Dropdown from "../components/common/Select/selectDropdown";
import { GetVendorAllReviewsApi, GetVendorReviewsApi } from "../api/Discover";
import UserReview from "../components/sections/listing/UserReview";

const ReviewsView = ({ navigation, route }) => {
  const { vendor } = route.params;
  const [reviews, setReviews] = useState(null);
  const [allReviews, setAllReviews] = useState(null);
  const [selectedFilter, setSelectedFilter] = useState("latest");
  const reviewCounts = [
    reviews?.five_star_reviews || 0,
    reviews?.four_star_reviews || 0,
    reviews?.three_star_reviews || 0,
    reviews?.two_star_reviews || 0,
    reviews?.one_star_reviews || 0,
  ];
  const animationsRef = useRef(
    new Array(5).fill(null).map(() => new Animated.Value(0))
  );

  const fetchReviews = async () => {
    if (!vendor?.vendor_id) return;
    try {
      const response = await GetVendorReviewsApi(vendor.vendor_id);

      setReviews(response);
    } catch (error) {
      console.error("Error fetching reviews:", error);
    }
  };

  const fetchAllReviews = async () => {
    try {
      const res = await GetVendorAllReviewsApi(
        vendor.vendor_id,
        selectedFilter
      );
      setAllReviews(res);
    } catch (error) {
      console.error("Error fetching all reviews:", error);
    }
  };

  useEffect(() => {
    if (vendor?.vendor_id) {
      fetchReviews();
    }
  }, [vendor?.vendor_id]);

  useEffect(() => {
    if (vendor?.vendor_id) {
      fetchAllReviews();
    }
  }, [vendor?.vendor_id, selectedFilter]);

  const calculateBarWidths = () => {
    if (!reviews) return [0, 0, 0, 0, 0];
    const totalReviews = reviews.num_reviews || 1;

    return [
      (reviews.five_star_reviews || 0) / totalReviews,
      (reviews.four_star_reviews || 0) / totalReviews,
      (reviews.three_star_reviews || 0) / totalReviews,
      (reviews.two_star_reviews || 0) / totalReviews,
      (reviews.one_star_reviews || 0) / totalReviews,
    ];
  };

  const animateBars = (barWidths) => {
    barWidths.forEach((width, index) => {
      Animated.timing(animationsRef.current[index], {
        toValue: width,
        duration: 1000,
        useNativeDriver: false,
      }).start();
    });
  };

  useEffect(() => {
    const barWidths = calculateBarWidths();
    animateBars(barWidths);
  }, [reviews]);

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <Text style={styles.headingtext}>Reviews</Text>
      </View>

      <View style={styles.ReviewsConatiner}>
        <Text style={styles.ratingTXT}>
          {reviews?.avg_rating ? reviews.avg_rating.toFixed(1) : "0.0"}
        </Text>
        <StarRating
          rating={reviews?.avg_rating ? reviews.avg_rating.toFixed(1) : "0.0"}
        />
        <Text style={styles.reviewNumber}>({reviews?.num_reviews || 0})</Text>
      </View>

      <View style={styles.barsContainer}>
        {animationsRef.current.map((anim, index) => (
          <View key={index} style={styles.row}>
            <Text style={styles.starText}>{5 - index} Stars</Text>

            <View style={styles.track}>
              <Animated.View
                style={[
                  styles.bar,
                  {
                    width: anim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ["0%", "100%"],
                    }),
                  },
                ]}
              />
            </View>

            <Text style={styles.reviewCount}>{reviewCounts[index]}</Text>
          </View>
        ))}
      </View>
      <Dropdown
        selectedFilter={selectedFilter}
        setSelectedFilter={setSelectedFilter}
      />
      <ScrollView>
        {allReviews?.map((review, index) => (
          <View key={index}>
            <UserReview review={review} />
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  starText: {
    fontSize: 14,
    fontFamily: "Poppins_600SemiBold",
    color: "#000000",
    marginRight: 8,
  },
  line1: {
    marginTop: 5,
    marginBottom: 5,
    width: "100%",
    height: 2,
    backgroundColor: "#F4F5F7",
  },
  headingtext: {
    flex: 1,
    textAlign: "center",
    fontFamily: "Poppins_600SemiBold",
    fontSize: 22,
    color: "#5F22D9",
    marginRight: 40,
  },
  ReviewsConatiner: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: 5,
  },
  ratingTXT: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 42,
    color: "#333",
  },
  reviewNumber: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#858585",
  },
  barsContainer: {
    justifyContent: "space-between",
    paddingHorizontal: 26,
    marginTop: 20,
  },
  track: {
    height: 8,
    backgroundColor: "#E3E9ED",
    borderRadius: 5,
    marginVertical: 5,
    overflow: "hidden",
    flex: 1,
    marginHorizontal: 8,
  },
  bar: {
    height: "100%",
    backgroundColor: "#5F22D9",
    borderRadius: 5,
  },
  reviewCount: {
    fontSize: 14,
    fontFamily: "Poppins_600SemiBold",
    color: "#000000",
    marginLeft: 8,
  },
});

export default ReviewsView;
