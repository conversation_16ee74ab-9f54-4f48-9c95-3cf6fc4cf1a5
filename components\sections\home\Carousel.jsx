import { StatusBar } from "expo-status-bar";
import React, { useRef, useState } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { carouselItem } from "../../../utils/Constants";
const { width, height } = Dimensions.get("window");

const viewConfigRef = { viewAreaCoveragePercentThreshold: 95 };

export default function OffersCarousel() {
  let flatListRef = useRef();
  const [currentIndex, setCurrentIndex] = useState(0);

  const onViewRef = useRef(({ changed }) => {
    if (changed[0].isViewable) {
      setCurrentIndex(changed[0].index);
    }
  });
  const scrollToIndex = (index) => {
    flatListRef.current?.scrollToIndex({ animated: true, index: index });
  };

  const renderItems = ({ item }) => {
    return (
      <View style={styles.cardContainer}>
        <View style={styles.textContainer}>
          <Text style={styles.offerText}>{item.offer}</Text>
          <View style={styles.line1}></View>

          <Text style={styles.restaurantText}>{item.restaurant_name}</Text>
        </View>
        <View
          style={{
            width: "100%",
          }}
        >
          <Image source={item.image} style={styles.image} />
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      <FlatList
        data={carouselItem}
        renderItem={renderItems}
        keyExtractor={(item, index) => index.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        ref={(ref) => {
          flatListRef.current = ref;
        }}
        style={styles.carousel}
        viewabilityConfig={viewConfigRef}
        onViewableItemsChanged={onViewRef.current}
      />

      <View style={styles.dotView}>
        {carouselItem.map(({}, index) => (
          <TouchableOpacity
            key={index.toString()}
            style={[
              styles.circle,
              {
                backgroundColor:
                  index == currentIndex ? "#181E22" : "#181E221A",
              },
            ]}
            onPress={() => scrollToIndex(index)}
          />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingLeft: 10,
    paddingRight: 10,
    marginTop: 10,
  },
  carousel: {
    maxHeight: 150,
  },
  line1: {
    width: "60%",
    height: 3,
    backgroundColor: "#F4F5F7",
  },
  cardContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#4685FC",
    borderRadius: 15,
    width: width - 40,
    marginHorizontal: 10,
    overflow: "hidden",
    elevation: 4,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
    borderWidth: 3,
    borderColor: "#ededed",
  },
  textContainer: {
    paddingLeft: 20,
    width: "50%",
    height: "100%",
    flexDirection: "column",
    justifyContent: "flex-end",
    marginBottom: 20,
  },
  offerText: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#fff",
  },
  restaurantText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
    marginTop: 5,
  },
  image: {
    height: 150,
    resizeMode: "contain",
  },
  dotView: {
    flexDirection: "row",
    justifyContent: "center",
    marginVertical: 5,
  },
  circle: {
    width: 8,
    height: 8,
    backgroundColor: "grey",
    borderRadius: 50,
    marginHorizontal: 5,
  },
});
