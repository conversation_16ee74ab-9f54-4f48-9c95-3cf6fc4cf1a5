import React from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";

import { AppImages } from "../../../utils/AppImages";
import { Pickup_Restaurants } from "../../../utils/Constants";

const SCREEN_WIDTH = Dimensions.get("window").width;
const CARD_WIDTH = SCREEN_WIDTH * 0.75;

const PickupSlider = ({ navigation }) => {
  const renderRestaurantCard = ({ item }) => (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() => navigation.navigate("ListingNonVeg", { item })}
      style={styles.cardContainer}
    >
      <View style={styles.imageContainer}>
        <Image source={item.image} style={styles.mainImage} />

        <View style={styles.ratingBadge}>
          <Text style={styles.ratingText}>{item.rating}</Text>
          <MaterialIcons name="star" size={16} color="#FFD700" />
        </View>

        <View style={styles.heartContainer}>
          <Image source={AppImages.LIKE} />
        </View>

        <View style={styles.priceBadge}>
          <Text style={styles.quantityText}>{item.quantity}</Text>
          <Text style={styles.priceText}>Rs{item.price}</Text>
        </View>
      </View>

      <View style={styles.infoContainer}>
        <Image source={item.logo} style={styles.logoImage} />
        <View style={styles.textContainer}>
          <Text style={styles.restaurantName}>{item.name}</Text>
          <View style={styles.detailsContainer}>
            <MaterialIcons name="location-on" size={16} color="#5F22D9" />
            <Text style={styles.detailsText}>{item.distance}</Text>
            <MaterialIcons name="access-time" size={16} color="#5F22D9" />
            <Text style={styles.detailsText}>{item.timing}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={Pickup_Restaurants}
      renderItem={renderRestaurantCard}
      keyExtractor={(item) => item.id}
      horizontal
      showsHorizontalScrollIndicator={false}
      snapToAlignment="start"
      decelerationRate="fast"
      snapToInterval={CARD_WIDTH}
      contentContainerStyle={styles.sliderContainer}
    />
  );
};

const styles = StyleSheet.create({
  sliderContainer: {
    paddingLeft: 15,
    paddingRight: 15,
    marginTop: 8,
    height: 210,
    marginBottom: 10,
  },
  cardContainer: {
    width: CARD_WIDTH,
    marginRight: 15,
    borderRadius: 20,
    height: 210,
    backgroundColor: "#FFFFFF",
    shadowColor: "#3e3e3e",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  imageContainer: {
    width: "100%",
    height: 140,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: "hidden",
    position: "relative",
  },
  mainImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
    borderRadius: 20,
  },
  ratingBadge: {
    position: "absolute",
    top: 10,
    left: 10,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
    gap: 2,
  },

  ratingText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#000",
  },
  heartContainer: {
    position: "absolute",
    top: 10,
    right: 10,
    backgroundColor: "#5F22D9",
    padding: 8,
    borderRadius: 20,
  },
  priceBadge: {
    position: "absolute",
    bottom: 10,
    right: 10,
    backgroundColor: "white",
    padding: 8,
    borderRadius: 15,
    flexDirection: "row",
    alignItems: "center",
  },
  quantityText: {
    marginRight: 3,
    marginTop: 3,
    color: "#666",
    fontSize: 11,
  },
  priceText: {
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9",
    fontWeight: 700,
    fontSize: 14,
  },
  infoContainer: {
    flexDirection: "row",
    padding: 15,
    alignItems: "center",
  },
  logoImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    resizeMode: "contain",
  },
  textContainer: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 5,
  },
  detailsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailsText: {
    color: "#666",
    marginRight: 10,
    marginLeft: 3,
  },
});

export default PickupSlider;
