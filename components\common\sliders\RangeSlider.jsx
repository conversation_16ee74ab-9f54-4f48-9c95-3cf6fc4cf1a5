import { View, Text, PanResponder, StyleSheet } from "react-native";
import React, { useState, useCallback } from "react";
import { FULL_WIDTH } from "../../../utils/Constants";

const SLIDER_WIDTH = FULL_WIDTH - 40;
const SLIDER_HEIGHT = 6;
const THUMB_SIZE = 20;

const RangeSlider = ({ minValue, maxValue, setMinValue, setMaxValue }) => {
  const [minPosition, setMinPosition] = useState(minValue);
  const [maxPosition, setMaxPosition] = useState(SLIDER_WIDTH);

  // Helper function to convert position to value
  const positionToValue = useCallback((position, isMin = false) => {
    const baseValue = Math.round((position / SLIDER_WIDTH) * 1000);
    return isMin ? baseValue + 50 : baseValue;
  }, []);

  const minPanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onPanResponderMove: (evt, gestureState) => {
      const newPosition = Math.max(
        0,
        Math.min(gestureState.moveX, maxPosition - THUMB_SIZE)
      );

      // Update position
      setMinPosition(newPosition);

      // Update value separately to avoid setState during render
      const newValue = positionToValue(newPosition, true);
      setMinValue(newValue);
    },
  });

  const maxPanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onPanResponderMove: (evt, gestureState) => {
      const newPosition = Math.max(
        minPosition + THUMB_SIZE,
        Math.min(gestureState.moveX, SLIDER_WIDTH)
      );

      // Update position
      setMaxPosition(newPosition);

      // Update value separately to avoid setState during render
      const newValue = positionToValue(newPosition, false);
      setMaxValue(newValue);
    },
  });

  return (
    <View style={{ width: "100%" }}>
      <Text style={styles.headingPOPUP}>Price Scale</Text>
      <View style={styles.sliderContainer}>
        <View style={styles.track} />
        <View
          style={[
            styles.selectedTrack,
            {
              left: minPosition,
              width: maxPosition - minPosition,
            },
          ]}
        />
        <View
          style={[styles.thumb, { left: minPosition - THUMB_SIZE / 2 }]}
          {...minPanResponder.panHandlers}
        />
        <View
          style={[styles.thumb, { left: maxPosition - THUMB_SIZE / 2 }]}
          {...maxPanResponder.panHandlers}
        />
      </View>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text style={styles.label}>Rs {minValue}</Text>
        <Text style={styles.label}>Rs {maxValue}</Text>
      </View>
    </View>
  );
};

export default RangeSlider;

const styles = StyleSheet.create({
  headingPOPUP: {
    fontSize: 16,
    color: "#171725",
    fontFamily: "Poppins_500Medium",
    marginVertical: 10,
  },
  sliderContainer: {
    width: SLIDER_WIDTH,
    height: THUMB_SIZE,
    justifyContent: "center",
    position: "relative",
  },
  track: {
    height: SLIDER_HEIGHT,
    backgroundColor: "#E0E0E0",
    borderRadius: SLIDER_HEIGHT / 2,
    position: "absolute",
    width: "100%",
  },
  selectedTrack: {
    height: SLIDER_HEIGHT,
    backgroundColor: "#5F22D9",
    borderRadius: SLIDER_HEIGHT / 2,
    position: "absolute",
  },
  thumb: {
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    backgroundColor: "#5F22D9",
    borderRadius: THUMB_SIZE / 2,
    position: "absolute",
  },
  label: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    marginVertical: 10,
    color: "#5F22D9",
  },
});
