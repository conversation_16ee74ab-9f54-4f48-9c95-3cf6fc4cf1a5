import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from "react-native";
import GlobalStyles from "../styles/GlobalStyles";

const { width } = Dimensions.get("window");

const allergens = [
  { id: 1, name: "<PERSON>", icon: "🍎" },
  { id: 2, name: "<PERSON><PERSON>", icon: "🥩" },
  { id: 3, name: "<PERSON><PERSON>", icon: "🍌" },
  { id: 4, name: "<PERSON>rab", icon: "🦀" },
  { id: 5, name: "Egg<PERSON>", icon: "🥚" },
  { id: 6, name: "<PERSON>", icon: "🐟" },
  { id: 7, name: "Milk", icon: "🥛" },
  { id: 8, name: "Mushroom", icon: "🍄" },
  { id: 9, name: "Shrimp", icon: "🦐" },
  { id: 10, name: "Pork", icon: "🐷" },
  { id: 11, name: "Mushroom", icon: "🍄" },
  { id: 12, name: "Walnut", icon: "🌰" },
];

const Allergens = ({ navigation, route }) => {
  const [selectedAllergens, setSelectedAllergens] = useState([]);
  const { modalVisible = true } = route?.params || {};

  const toggleAllergen = (allergenId) => {
    setSelectedAllergens((prev) =>
      prev.includes(allergenId)
        ? prev.filter((id) => id !== allergenId)
        : [...prev, allergenId]
    );
  };

  const handleApply = () => {
    // Handle apply logic here
    navigation.goBack();
  };

  const handleClose = () => {
    navigation.goBack();
  };

  const renderAllergenItem = (allergen) => {
    const isSelected = selectedAllergens.includes(allergen.id);
    const isEggs = allergen.name === "Eggs";
    const isMushroom = allergen.name === "Mushroom";

    return (
      <TouchableOpacity
        key={allergen.id}
        style={[
          styles.allergenItem,
          (isEggs || isMushroom) && isSelected && styles.selectedAllergenItem,
        ]}
        onPress={() => toggleAllergen(allergen.id)}
      >
        <Text style={styles.allergenIcon}>{allergen.icon}</Text>
        <Text style={styles.allergenName}>{allergen.name}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={modalVisible}
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
          
          <Text style={styles.title}>
            Let us know what are your{"\n"}allergens & dislikes
          </Text>
          
          <View style={styles.allergensGrid}>
            {allergens.map((allergen) => renderAllergenItem(allergen))}
          </View>
          
          <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
            <Text style={styles.applyButtonText}>Apply</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 20,
    padding: 24,
    width: width * 0.9,
    maxWidth: 400,
    position: "relative",
  },
  closeButton: {
    position: "absolute",
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  closeButtonText: {
    fontSize: 20,
    color: "#666",
    lineHeight: 20,
  },
  title: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#8E8E8E",
    textAlign: "center",
    marginBottom: 32,
    marginTop: 16,
    lineHeight: 24,
  },
  allergensGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  allergenItem: {
    width: "30%",
    aspectRatio: 1,
    backgroundColor: "#F8F9FA",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedAllergenItem: {
    backgroundColor: "#5F22D9",
  },
  allergenIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  allergenName: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#333",
    textAlign: "center",
  },
  applyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: "center",
  },
  applyButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
  },
});

export default Allergens;
