import axiosClient from "../AxiosClient";

export const LoginApi = async (number) => {
  const response = await axiosClient.post(
    `user/me/login/sms/send?phone_number=${number}`
  );
  return response.data;
};

export const RegisterFCMToken = async (token) => {
  const response = await axiosClient.post(
    `user/me/register/fcm-token?fcm_token=${token}`
  );
  return response.data;
};

export const VerifyLoginApi = async (data) => {
  const response = await axiosClient.post(`user/me/login/sms/verify`, data);
  return response.data;
};

export const getMe = async () => {
  const response = await axiosClient.get(`user/me/get`);
  console.log("get user details data....");
  console.log(response.data);
  return response.data;
};

export const loginWithGoogle = async (token) => {
  const response = await axiosClient.post(
    `/user/me/login/google?token=${token}`
  );

  return response.data;
};

export const loginWithApple = async (token) => {
  const response = await axiosClient.post(
    `/user/me/login/apple?token=${token}`
  );
  return response.data;
};

export const DeleteAccount = async () => {
  const response = await axiosClient.delete(`user/me/account/delete`);
  return response.data;
};
