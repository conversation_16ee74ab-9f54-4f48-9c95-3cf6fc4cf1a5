import { View, Text, Image } from "react-native";
import React from "react";
import categories from "../../../Data/Categories";
import { TouchableOpacity } from "react-native";
import { StyleSheet } from "react-native";

const CategoriesList = ({ filterData, setFilterData }) => {
  const activeCategory = filterData.activeCategory;

  const handleCategorySelect = (category) => {
    setFilterData({ ...filterData, activeCategory: category });
  };

  return (
    <View style={styles.headerContainer}>
      {categories.map((category, index) => (
        <TouchableOpacity
          key={category.id}
          style={[
            styles.categories,
            {
              backgroundColor:
                activeCategory.id === category.id ? "#5F22D9" : "#CFC9DB6E",
            },
          ]}
          onPress={() => handleCategorySelect(category)}
        >
          <Image style={styles.categoryImage} source={category.image} />
          <View
            style={[
              styles.textContainer,
              index === categories.length - 1 && { marginLeft: 5 },
            ]}
          >
            <Text
              style={[
                styles.categoryText,
                {
                  color:
                    activeCategory.id === category.id ? "white" : "#0F0E0E",
                },
              ]}
            >
              {category.name}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default CategoriesList;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 12,
    paddingBottom: 16,
  },

  categories: {
    borderRadius: 25,
    paddingVertical: 5,
    width: 110,
    flexDirection: "row",
    alignItems: "center",
    paddingLeft: 5,
    paddingRight: 5,
    marginHorizontal: 2,
    shadowColor: "#A24CFE",
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowRadius: 30,
    shadowOpacity: 0.5,
    elevation: 15,
  },

  categoryImage: {
    height: 36,
    width: 36,
    borderRadius: 20,
    backgroundColor: "white",
    resizeMode: "contain",
  },

  textContainer: {
    flex: 1,
    marginLeft: 10,
    justifyContent: "center",
  },

  categoryText: {
    fontSize: 12,
    fontWeight: "500",
    textAlign: "left",
  },
});
