import React from "react";
import { View, Text, Image, StyleSheet } from "react-native";

import { AppImages } from "../../utils/AppImages";

const EmptyCart = ({ navigation }) => {
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: "#FFF",
      }}
    >
      <View style={styles.noResultsContainer}>
        <View style={styles.imageContainer}>
          <Image style={styles.backgroundImage} source={AppImages.EMPTYCART} />
        </View>
        <Text style={styles.noResultsText}>Cart Empty</Text>
        <Text style={styles.subText}>Your cart is empty.</Text>
        <Text style={styles.subText}>
          {" "}
          Start adding items to enjoy shopping!
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 15,
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
    backgroundColor: "#FFF",
  },
  backButton: {
    padding: 8,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
    position: "absolute",
    left: 16,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },

  noResultsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 30,
  },
  imageContainer: {
    position: "relative",
    width: 300,
    height: 300,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
    resizeMode: "contain",
  },
  searchIconCentered: {
    position: "absolute",
    left: 30,
    top: 50,
    width: 270,
    height: 270,
    resizeMode: "contain",
  },
  noResultsText: {
    textAlign: "center",
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    marginTop: 10,
    color: "#333333",
  },
  subText: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#838282",
    marginTop: 5,
    textAlign: "center",
  },
});

export default EmptyCart;
