import React, { useEffect, useState } from "react";
import {
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { CountryPicker } from "react-native-country-codes-picker";
import { s } from "react-native-size-matters";
import { Poppins_500Medium, useFonts } from "@expo-google-fonts/poppins";
import { AppColors } from "../../../utils/AppColors";

const PhoneField = (props) => {
  const [showCountryCode, setShowCountryCode] = useState(false);

  const pickerStyle = {
    modal: {
      height: 500,
      backgroundColor: AppColors.whiteColor,
    },
    backdrop: {
      backgroundColor: AppColors.whiteColor,
    },
    textInput: {
      height: 70,
      borderRadius: 10,
      paddingLeft: 20,
    },
    countryButtonStyles: {
      height: 70,
    },
    searchMessageText: {
      fontFamily: "Poppins_500Medium",
    },
    dialCode: {
      fontFamily: "Poppins_500Medium",
    },
    countryName: {
      fontFamily: "Poppins_500Medium",
    },
  };

  useFonts({
    Poppins_500Medium,
  });
  return (
    <SafeAreaView
      style={{
        width: "80%",
        backgroundColor: AppColors.whiteColor,
        flexDirection: "column",
      }}
    >
      <Text
        style={{
          fontFamily: "Poppins_500Medium",
          fontSize: 14,
          color: AppColors.SubColor,
        }}
      >
        Phone Number
      </Text>

      <View style={styles.container}>
        <TouchableOpacity
          onPress={() => setShowCountryCode(true)}
          style={styles.codeContainer}
        >
          <Text style={styles.codeText}>{props.code}</Text>
        </TouchableOpacity>
        <CountryPicker
          style={pickerStyle}
          show={showCountryCode}
          onBackdropPress={() => setShowCountryCode(false)}
          pickerButtonOnPress={(item) => {
            props.setCode(item.dial_code);
            setShowCountryCode(false);
          }}
          lang={""}
          inputPlaceholder={"Country code"}
        />
        <View style={styles.phoneWrapper}>
          <TextInput
            style={styles.input}
            placeholder={"Your Phone Number"}
            placeholderTextColor={AppColors.blackColor}
            defaultValue={props.number}
            onChangeText={props.setNumber}
            keyboardType="phone-pad"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
export default PhoneField;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },

  codeContainer: {
    flex: 1,
    borderColor: AppColors.primaryColor,
    borderWidth: 1,
    paddingLeft: 10,
    borderRadius: 10,
    height: 50,
    marginVertical: 10,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "transparent",
    marginHorizontal: s(5),
    paddingHorizontal: s(5),
  },
  codeText: {
    color: AppColors.blackColor,
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
  },

  phoneWrapper: {
    borderColor: AppColors.primaryColor,
    borderWidth: 1,
    paddingLeft: 10,
    borderRadius: 10,
    height: 50,
    marginVertical: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "transparent",
  },

  input: {
    fontFamily: "Poppins_500Medium",
    color: AppColors.blackColor,
    paddingHorizontal: s(5),
    width: "80%",
  },
});
