import {
  View,
  Text,
  Image,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Pressable,
  SafeAreaView,
  ScrollView,
  PanResponder,
  Dimensions,
  Modal,
  Animated,
  Easing,
} from "react-native";
import React, { useRef, useState, useEffect } from "react";
import { Ionicons } from "@expo/vector-icons";
import categories from "../../../Data/Categories";
import RangeSlider from "../../common/sliders/RangeSlider";
import TimeSlider from "../../common/sliders/TimeSlider";

const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);
tomorrow.setHours(0, 0, 0, 0);

const oneWeekLater = new Date(tomorrow);
oneWeekLater.setDate(oneWeekLater.getDate() + 7);
oneWeekLater.setHours(23, 59, 59, 999);

const START_TIME = Math.floor(tomorrow.getTime() / 1000);
const END_TIME = Math.floor(oneWeekLater.getTime() / 1000);

function FilterPopup({
  setModalVisible,
  modalVisible,
  setFilterData,
  filterData,
}) {
  const [slideAnim] = useState(new Animated.Value(300));

  const [minValue, setMinValue] = useState(filterData.minValue || 0);
  const [maxValue, setMaxValue] = useState(filterData.maxValue || 1000);

  const [selectedDistance, setSelectedDistance] = useState("upto 5Km");

  const [minTime, setMinTime] = useState(START_TIME);
  const [maxTime, setMaxTime] = useState(END_TIME);

  const [selectedType, setSelectedType] = useState("Meals");

  const handlePress = (type) => {
    setSelectedType(type);
    console.log(`${type} selected`);
  };

  const distances = ["upto 5Km", "upto 10Km", "Upto 20Km"];
  const mapDistanceToValue = (label) => {
    switch (label) {
      case "upto 5Km":
        return 4;
      case "upto 10Km":
        return 10;
      case "Upto 20Km":
        return 50;
      default:
        return 0;
    }
  };

  const handleDistancePress = (dist) => {
    setSelectedDistance(dist);

    const distanceValue = mapDistanceToValue(dist);
    console.log("Numeric distance to send to API:", distanceValue);
  };

  useEffect(() => {
    if (modalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      slideAnim.setValue(300);
    }
  }, [modalVisible]);

  const handleApplyFilter = () => {
    const distanceValue = mapDistanceToValue(selectedDistance);

    setFilterData({
      minValue,
      maxValue,
      activeCategory: categories.find((cat) => cat.name === selectedType),
      selectedDistance: distanceValue,
      minTime,
      maxTime,
    });
    setModalVisible(false);
  };

  const handleClearFilters = () => {
    setMinValue(0);
    setMaxValue(1000);
    setSelectedDistance(0);
    setMinTime(0);
    setMaxTime(0);
    setSelectedType("Meals");
    setFilterData({
      minValue: 0,
      maxValue: 1000,
      activeCategory: categories[0],
      selectedDistance: 0,
      minTime: 0,
      maxTime: 0,
      searchQuery: "",
    });
    setModalVisible(false);
  };

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(false);
        slideAnim.setValue(300);
      }}
      onDismiss={() => {
        slideAnim.setValue(300);
      }}
    >
      <View style={styles.filterPopUpcontainer}>
        <Animated.View style={[{ transform: [{ translateY: slideAnim }] }]}>
          <View style={styles.modalContainer}>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "flex-end",
              }}
            >
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons name="close" size={20} color="#000" />
              </TouchableOpacity>
            </View>

            {/* Price Scale */}
            <RangeSlider
              minValue={minValue}
              maxValue={maxValue}
              setMinValue={setMinValue}
              setMaxValue={setMaxValue}
            />

            <Text style={styles.headingPOPUP}>Surprise Bag Type</Text>
            <View style={styles.optionsContainer}>
              {categories.map((type) => (
                <TouchableOpacity
                  key={type.name}
                  style={[
                    styles.CategoryOptionButton,
                    selectedType === type.name
                      ? styles.optionButton
                      : styles.inactiveOptionButton,
                  ]}
                  onPress={() => handlePress(type.name)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      selectedType === type.name
                        ? styles.activeText
                        : styles.inactiveText,
                    ]}
                  >
                    {type.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.headingPOPUP}>Distance</Text>
            <View style={styles.optionsContainer}>
              {distances.map((dist) => (
                <TouchableOpacity
                  key={dist}
                  style={[
                    styles.optionButton,
                    selectedDistance === dist
                      ? styles.optionButton
                      : styles.inactiveOptionButton,
                  ]}
                  onPress={() => handleDistancePress(dist)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      selectedDistance === dist
                        ? styles.activeText
                        : styles.inactiveText,
                    ]}
                  >
                    {dist}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <TimeSlider
              minTime={minTime}
              setMinTime={setMinTime}
              maxTime={maxTime}
              setMaxTime={setMaxTime}
              startTime={START_TIME}
              endTime={END_TIME}
            />

            <TouchableOpacity
              style={styles.applyButton}
              onPress={handleApplyFilter}
            >
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClearFilters}
            >
              <Text style={styles.clearButtonText}>Clear Filters</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
}

export default FilterPopup;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterPopUpcontainer: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  button: { backgroundColor: "#6200EA", padding: 15, borderRadius: 10 },
  buttonText: { color: "white", fontWeight: "bold" },
  modalContainer: {
    backgroundColor: "white",
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    elevation: 5,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: -1 },
    shadowRadius: 5,
  },
  headingPOPUP: {
    fontSize: 16,
    color: "#171725",
    fontFamily: "Poppins_500Medium",
    marginVertical: 10,
  },
  optionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  CategoryOptionButton: {
    width: "30%",
    paddingVertical: 8,
    backgroundColor: "#5F22D9",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#A24CFE",
    shadowOffset: {
      width: 0,
      height: 14,
    },
    shadowRadius: 30,
    shadowOpacity: 0.25,
    elevation: 15,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#5F22D9",
    borderRadius: 20,
    elevation: 10,
    shadowColor: "#A24CFE",
    shadowOffset: {
      width: 0,
      height: 14,
    },
    shadowRadius: 30,
    shadowOpacity: 0.25,
    elevation: 15,
  },
  inactiveOptionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "rgba(196, 196, 196, 1)",
    borderRadius: 20,
    elevation: 10,
  },
  optionText: {
    fontSize: 16,
    color: "#FFF",
    textAlign: "center",
  },
  applyButton: {
    backgroundColor: "#6200EA",
    padding: 15,
    borderRadius: 30,
    marginTop: 20,
  },
  applyButtonText: { color: "white", fontWeight: "bold", textAlign: "center" },

  label: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    marginVertical: 10,
    color: "#5F22D9",
  },
  clearButton: {
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  clearButtonText: {
    color: "black",
    fontWeight: "bold",
    textAlign: "center",
  },
});
