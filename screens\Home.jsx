import {
  View,
  Text,
  Image,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Pressable,
  SafeAreaView,
  ScrollView,
  PanResponder,
  Dimensions,
  Modal,
  RefreshControl,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import { AppImages } from "../utils/AppImages";
import categories from "../Data/Categories";
import RestaurantSlider from "../components/sections/home/<USER>";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import GlobalStyles from "../styles/GlobalStyles";
import FilterPopup from "../components/sections/home/<USER>";
import PickupSlider from "../components/sections/home/<USER>";
import SpecialOfferSlider from "../components/sections/home/<USER>";
import HowWorks from "../components/sections/home/<USER>";
import BakeriesSlider from "../components/sections/home/<USER>";
import CardCarousel from "../components/sections/home/<USER>";
import OffersCarousel from "../components/sections/home/<USER>";
import NoLocationFound from "../components/NotFound/NoLocationFound";
import NoResultsFound from "../components/NotFound/NoResultsFound";
import CancelOrderPopup from "../components/sections/cancelOrder/CancelOrderPopup";
import { GetBannerApi, GetItemApi, GetVendorsApi } from "../api/Discover";
import MainAd from "../components/sections/home/<USER>";
import CartPopup from "../components/sections/home/<USER>";
import CategoriesList from "../components/sections/home/<USER>";
import HomeHeader from "../components/sections/home/<USER>";
import SearchFilters from "../components/sections/home/<USER>";
import { useSelector } from "react-redux";
import MainListHeading from "../components/sections/home/<USER>";
import Toast from "../components/common/Toast/Toast";
import SearchRestaurantCard from "../components/sections/home/<USER>";

const Home = ({ navigation }) => {
  const address = useSelector((state) => state.user.address);
  const [modalVisible, setModalVisible] = useState(false);
  const [showLocation, setShowLocation] = useState(false);
  const [banner, setbanner] = useState("");
  const [vendors, setVendors] = useState([]);
  const [pickupNow, setPickupNow] = useState([]);
  const [trending, setTrending] = useState([]);
  const [bestValue, setBestValue] = useState([]);
  const [featured, setFeatured] = useState([]);
  const [newOnPlenti, setNewOnPlenti] = useState([]);
  const [searchFilterVendors, setSearchFilterVendors] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const [itemPopupVisible, setItemPopupVisible] = useState({
    visible: false,
    vendor: null,
    items: [],
  });

  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // filter data
  const [filterData, setFilterData] = useState({
    minValue: 0,
    maxValue: 1000,
    activeCategory: categories[0],
    selectedDistance: 0,
    minTime: 0,
    maxTime: 0,
    searchQuery: "",
  });

  const toggelLocation = () => {
    setShowLocation(!showLocation);
  };

  const getVendors = async (setValue, section) => {
    try {
      const filters = {
        item_type: filterData.activeCategory?.value,
      };

      const res = await GetVendorsApi(
        address.latitude,
        address.longitude,
        filters,
        section
      );
      setValue(res);
    } catch (error) {
      console.log("Error response from get all vendors");
      console.error(error);
    }
  };

  const getFilterSearchVendors = async () => {
    try {
      const filters = {
        item_type: filterData.activeCategory?.value,
        price_min: filterData.minValue,
        price_max: filterData.maxValue !== 1000 ? filterData.maxValue : null,
        window_start_time: filterData.minTime,
        window_end_time: filterData.maxTime,
        radius_km: filterData.selectedDistance,
        search_query: filterData.searchQuery,
      };

      const filteredFilters = Object.fromEntries(
        Object.entries(filters).filter(
          ([_, value]) =>
            value !== null && value !== undefined && value !== 0 && value !== ""
        )
      );

      const res = await GetVendorsApi(
        address.latitude,
        address.longitude,
        filteredFilters
      );
      setSearchFilterVendors(res);
    } catch (error) {
      console.log("Error response from get all vendors");
      console.error(error);
    }
  };

  useEffect(() => {
    console.log(filterData);

    if (
      filterData.searchQuery !== "" ||
      filterData.minValue !== 0 ||
      filterData.maxValue !== 1000 ||
      filterData.selectedDistance !== 0 ||
      filterData.minTime !== 0 ||
      filterData.maxTime !== 0
    ) {
      setIsSearching(true);
      getFilterSearchVendors();
    } else {
      setIsSearching(false);
      setSearchFilterVendors([]);
    }
  }, [filterData]);

  useEffect(() => {
    getVendors(setVendors, "near_you");
    getVendors(setPickupNow, "pickup_now");
    getVendors(setTrending, "trending");
    getVendors(setBestValue, "best_value");
    getVendors(setFeatured, "featured");
    getVendors(setNewOnPlenti, "new_on_plenti");
  }, [filterData.activeCategory]);

  const getBanner = async () => {
    try {
      const banner = await GetBannerApi();
      setbanner(banner.image_url);
    } catch (error) {
      console.error("Error response from get banner image");
      console.error(error);
    }
  };

  useEffect(() => {
    getBanner();
  }, []);

  const getVendorItems = async (vendor) => {
    try {
      const res = await GetItemApi({ item_ids: vendor?.item_ids });
      setItemPopupVisible({
        visible: true,
        vendor: vendor,
        items: res,
      });
    } catch (error) {
      error.response.status === 404 &&
        setItemPopupVisible({
          visible: true,
          vendor: vendor,
          items: [],
        });
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
      getVendors(setVendors, "near_you");
      getVendors(setPickupNow, "pickup_now");
      getVendors(setTrending, "trending");
      getVendors(setBestValue, "best_value");
      getVendors(setFeatured, "featured");
      getVendors(setNewOnPlenti, "new_on_plenti");
      getBanner();
    }, 2000);
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        GlobalStyles.androidSafeArea,
        { backgroundColor: "#fff" },
      ]}
    >
      <HomeHeader navigation={navigation} />

      <SearchFilters
        setModalVisible={setModalVisible}
        setFilterData={setFilterData}
        filterData={filterData}
      />

      <CategoriesList filterData={filterData} setFilterData={setFilterData} />

      {isSearching ? (
        searchFilterVendors.length !== 0 ? (
          <ScrollView
            style={{ paddingHorizontal: 20, flexDirection: "column" }}
          >
            <View style={{ marginTop: 20 }} />
            {searchFilterVendors.map((vendor, index) => (
              <SearchRestaurantCard
                item={vendor}
                key={index}
                getVendorItems={getVendorItems}
              />
            ))}
          </ScrollView>
        ) : (
          <NoResultsFound
            message="No results found"
            subMessage="Please try changing your search filters or search query."
          />
        )
      ) : (
        <ScrollView
          contentContainerStyle={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          <MainAd image={banner} />
          {/* near_you */}
          {vendors.length !== 0 && (
            <>
              <MainListHeading
                activeCategory={1}
                navigation={navigation}
                vendors={vendors}
              />
              <RestaurantSlider
                setItemPopupVisible={setItemPopupVisible}
                getVendorItems={getVendorItems}
                vendors={vendors.slice(0, 3)}
                setToastMessage={setToastMessage}
                setShowToast={setShowToast}
              />
            </>
          )}
          {/* pickup_now */}
          {pickupNow.length !== 0 && (
            <>
              <MainListHeading
                activeCategory={2}
                navigation={navigation}
                vendors={pickupNow}
              />
              <RestaurantSlider
                setItemPopupVisible={setItemPopupVisible}
                getVendorItems={getVendorItems}
                vendors={pickupNow.slice(0, 3)}
                setToastMessage={setToastMessage}
                setShowToast={setShowToast}
              />
            </>
          )}
          {/* trending */}
          {trending.length !== 0 && (
            <>
              <MainListHeading
                activeCategory={3}
                navigation={navigation}
                vendors={trending}
              />
              <RestaurantSlider
                setItemPopupVisible={setItemPopupVisible}
                getVendorItems={getVendorItems}
                vendors={trending.slice(0, 3)}
                setToastMessage={setToastMessage}
                setShowToast={setShowToast}
              />
            </>
          )}
          {/* best_value */}
          {bestValue.length !== 0 && (
            <>
              <MainListHeading
                activeCategory={4}
                navigation={navigation}
                vendors={bestValue}
              />
              <RestaurantSlider
                setItemPopupVisible={setItemPopupVisible}
                getVendorItems={getVendorItems}
                vendors={bestValue.slice(0, 3)}
                setToastMessage={setToastMessage}
                setShowToast={setShowToast}
              />
            </>
          )}
          {/* featured */}
          {featured.length !== 0 && (
            <>
              <MainListHeading
                activeCategory={5}
                navigation={navigation}
                vendors={featured}
              />
              <RestaurantSlider
                setItemPopupVisible={setItemPopupVisible}
                getVendorItems={getVendorItems}
                vendors={featured.slice(0, 3)}
                setToastMessage={setToastMessage}
                setShowToast={setShowToast}
              />
            </>
          )}
          {/* new_on_plenti */}
          {newOnPlenti.length !== 0 && (
            <>
              <MainListHeading
                activeCategory={6}
                navigation={navigation}
                vendors={newOnPlenti}
              />
              <RestaurantSlider
                setItemPopupVisible={setItemPopupVisible}
                getVendorItems={getVendorItems}
                vendors={newOnPlenti.slice(0, 3)}
                setToastMessage={setToastMessage}
                setShowToast={setShowToast}
              />
            </>
          )}
          <View>
            <HowWorks />
          </View>
          <View>
            <OffersCarousel />
          </View>
        </ScrollView>
      )}

      {/* filters */}
      {modalVisible && (
        <FilterPopup
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          filterData={filterData}
          setFilterData={setFilterData}
        />
      )}

      {/* vendor items */}
      {itemPopupVisible.visible && (
        <CartPopup
          modalVisible={itemPopupVisible}
          setModalVisible={setItemPopupVisible}
          navigation={navigation}
        />
      )}

      {showToast && <Toast type="success" message={toastMessage} />}
    </SafeAreaView>
  );
};

export default Home;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  marketContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 20,
    paddingRight: 20,
    marginTop: 10,
  },
  heading: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 21.6,
    color: "#323643",
  },
  recommendConatiner: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  viewAllText: {
    fontFamily: "Poppins_400Regular",
    color: "#5F22D9",
  },
  viewAllArrow: {
    resizeMode: "contain",
    marginLeft: 5,
  },
});
