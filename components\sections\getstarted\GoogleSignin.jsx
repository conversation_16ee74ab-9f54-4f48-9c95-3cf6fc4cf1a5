// GoogleSignin.tsx
import React, { useEffect } from "react";
import {
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
  Platform,
} from "react-native";

import {
  GoogleSignin,
  statusCodes,
} from "@react-native-google-signin/google-signin";
import { useDispatch } from "react-redux";
import { storeToken, setUser } from "../../../redux/slices/userSlice";

import { AppImages } from "../../../utils/AppImages";
import { AppColors } from "../../../utils/AppColors";
import { useNavigation } from "@react-navigation/native";
import { getMe, loginWithGoogle } from "../../../api/Auth";

GoogleSignin.configure({
  iosClientId:
    "298219936678-hh46l5ha8icih4cie5m2nvm6hm59h63r.apps.googleusercontent.com",
  scopes: ["profile", "email"],
});

const GoogleSigninButton = ({ buttonStyle, iconStyle }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const handleGoogleSignIn = async () => {
    try {
      await GoogleSignin.hasPlayServices();

      const userInfo = await GoogleSignin.signIn();

      const token = userInfo?.idToken || userInfo?.data?.idToken;

      if (!token) {
        console.warn("⚠️ No ID token found in userInfo");
        Alert.alert("Sign-In Error", "Google token not found. Try again.");
        return;
      }

      console.log("user token before sending request");

      console.log(token);

      const res = await loginWithGoogle(token);

      console.log("response from google login API,,,,,,");
      console.log(res);

      if (!res.access_token) {
        console.warn("⚠️ loginWithGoogle response is null or undefined");
        Alert.alert("Login Failed", "Unable to authenticate with Google.");
        return;
      }

      storeToken(res.access_token);

      await getMe();

      navigation.navigate("TabNavigator", { screen: "Discover" });
    } catch (error) {
      Alert.alert("Sign-In Error", "Something went wrong. Please try again.");
      console.error("❌ [loginWithGoogle] Error during API call:", error);
      if (error.response) {
        console.error(
          "📛 [loginWithGoogle] Error Response:",
          error.response.data
        );
      }
      throw error;
    }
  };

  return (
    <TouchableOpacity
      onPress={handleGoogleSignIn}
      style={[buttonStyle, { backgroundColor: AppColors.whiteColor }]}
    >
      <Image style={iconStyle} source={AppImages.GOOGLE_ICON} />
      <Text style={styles.buttonTextStyle}>Continue with Google</Text>
    </TouchableOpacity>
  );
};

export default GoogleSigninButton;

const styles = StyleSheet.create({
  buttonTextStyle: {
    color: AppColors.blackColor,
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    marginHorizontal: 10,
  },
});
