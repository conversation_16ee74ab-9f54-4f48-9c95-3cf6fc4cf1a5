// Test script for checkout update functionality
import { 
  Checkout<PERSON><PERSON>, 
  GetCheckout<PERSON>pi, 
  updateCheckout,
  updateCheckoutItemQuantity,
  removeCheckoutItem
} from '../api/Checkout';

export const testCheckoutUpdateFlow = async () => {
  console.log("🧪 Starting Checkout Update Flow Test...");
  
  try {
    // Step 1: Create initial checkout
    console.log("📝 Step 1: Creating initial checkout...");
    const initialCheckoutData = {
      "checkout_items": [
        {
          "item_id": "itm_ldOwzesXd9",
          "quantity": 1
        }
      ],
      "dislikes": [],
      "vendor_id": "vdr_11b6466a145b"
    };
    
    const createResponse = await CheckoutApi(initialCheckoutData);
    console.log("✅ Initial checkout created:", createResponse);
    const checkoutId = createResponse.checkout_id;
    
    // Step 2: Test quantity update using correct API format
    console.log("📝 Step 2: Testing quantity update...");
    const updateResponse = await updateCheckoutItemQuantity(
      checkoutId, 
      createResponse.checkout_items[0].id, 
      3
    );
    console.log("✅ Quantity updated:", updateResponse);
    
    // Step 3: Test adding new item using correct format
    console.log("📝 Step 3: Testing add new item...");
    const addItemData = {
      "items": [
        {
          "item_id": createResponse.checkout_items[0].id,
          "quantity": 3
        },
        {
          "item_id": "itm_new_item_123", // Replace with actual item ID
          "quantity": 1
        }
      ],
      "dislikes": ["seafood", "nuts"]
    };
    
    const addResponse = await updateCheckout(checkoutId, addItemData);
    console.log("✅ New item added:", addResponse);
    
    // Step 4: Test item removal
    console.log("📝 Step 4: Testing item removal...");
    const removeResponse = await removeCheckoutItem(
      checkoutId, 
      createResponse.checkout_items[0].id
    );
    console.log("✅ Item removed:", removeResponse);
    
    // Step 5: Verify final state
    console.log("📝 Step 5: Verifying final checkout state...");
    const finalCheckout = await GetCheckoutApi();
    console.log("✅ Final checkout state:", finalCheckout);
    
    console.log("🎉 All checkout update tests completed successfully!");
    return true;
    
  } catch (error) {
    console.error("❌ Checkout update test failed:", error);
    if (error.response) {
      console.log("📊 Error response status:", error.response.status);
      console.log("📋 Error response data:", error.response.data);
    }
    return false;
  }
};

export const validateCheckoutUpdateRequest = (requestData) => {
  console.log("🔍 Validating checkout update request format...");
  
  // Check required fields
  if (!requestData.items || !Array.isArray(requestData.items)) {
    console.error("❌ Missing or invalid 'items' field");
    return false;
  }
  
  if (!requestData.dislikes || !Array.isArray(requestData.dislikes)) {
    console.error("❌ Missing or invalid 'dislikes' field");
    return false;
  }
  
  // Validate items structure
  for (const item of requestData.items) {
    if (!item.item_id || typeof item.quantity !== 'number') {
      console.error("❌ Invalid item structure:", item);
      return false;
    }
  }
  
  console.log("✅ Checkout update request format is valid");
  return true;
};

export const mockCheckoutUpdateData = {
  "items": [
    {
      "item_id": "itm_ldOwzesXd9",
      "quantity": 2
    },
    {
      "item_id": "itm_suggested_123",
      "quantity": 1
    }
  ],
  "dislikes": ["seafood", "nuts", "dairy"]
};

// Usage example:
// import { testCheckoutUpdateFlow, validateCheckoutUpdateRequest, mockCheckoutUpdateData } from './utils/CheckoutUpdateTest';
// 
// // Run the test
// testCheckoutUpdateFlow().then(success => {
//   console.log('Test result:', success);
// });
// 
// // Validate request format
// const isValid = validateCheckoutUpdateRequest(mockCheckoutUpdateData);
// console.log('Request format valid:', isValid);
