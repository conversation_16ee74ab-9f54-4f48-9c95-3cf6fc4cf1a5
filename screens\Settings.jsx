import {
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { AppImages } from "../utils/AppImages";
import { SettingsMoreOptions, SettingsOptions } from "../utils/Constants";
import { useSelector, useDispatch } from "react-redux";
import { removeToken, logoutUser } from "../redux/slices/userSlice";
import { OpenWebsite, SignUpStore } from "../utils/WebLinks";
import { DeleteAccount } from "../api/Auth";
import * as StoreReview from "expo-store-review";
import { Icons } from "../utils/svgs";
import { CommonActions } from "@react-navigation/native";

function Settings({ navigation }) {
  const user = useSelector((state) => state.user.user);
  const dispatch = useDispatch();

  const selectedOptionHandler = (index) => {
    if (index === 0) {
      navigation.navigate("Profile_Details");
    } else if (index === 1) {
      navigation.navigate("PaymentMethods");
    } else if (index === 2) {
      navigation.navigate("Referal");
    } else {
      console.log("inside else");
    }
  };

  const handleDeleteAccount = async () => {
    try {
      await DeleteAccount();
      dispatch(logoutUser());
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: "GetStarted" }],
        })
      );
    } catch (error) {
      console.error("Error deleting account:", error);
    }
  };

  const selectedMoreOptionHandler = async (index) => {
    if (index === 0) {
      SignUpStore();
    } else if (index === 1) {
      navigation.navigate("ContactUs");
    } else if (index === 2) {
      if (await StoreReview.hasAction()) {
        StoreReview.requestReview();
      }
    } else if (index === 3) {
      OpenWebsite();
    } else {
      console.log("inside else");
    }
  };

  const handleLogout = () => {
    dispatch(logoutUser());
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: "GetStarted" }],
      })
    );
  };

  return (
    <View
      style={{
        backgroundColor: "#FFFFFF",
        height: "100%",
      }}
    >
      <View style={styles.container}>
        <Image source={AppImages.E5} style={styles.leftCircle} />
        <Image source={AppImages.E4} style={styles.middleCircle} />
        <Image source={AppImages.E3} style={styles.rightCircle} />

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>

        <View style={styles.profileContainer}>
          <Image
            source={
              user?.user_profile_picture_url
                ? { uri: user.user_profile_picture_url }
                : null
            }
            style={styles.profileImage}
          />
        </View>
      </View>

      <View style={styles.centeredTextContainer}>
        <Text style={styles.profileName}>User Name</Text>
      </View>

      <ScrollView>
        <View
          style={{
            paddingLeft: 15,
            paddingRight: 15,
          }}
        >
          <View style={styles.card}>
            <View style={[styles.headerContainer, { paddingHorizontal: 0 }]}>
              <Text style={styles.header}>General</Text>
              <View style={styles.headerLine} />
            </View>
            {SettingsOptions.map((option, index) => (
              <View style={styles.options} key={index}>
                <TouchableOpacity
                  style={[
                    styles.optionContainer,
                    index === SettingsOptions.length - 1 && {
                      borderBottomWidth: 0,
                    },
                  ]}
                  onPress={() => selectedOptionHandler(index)}
                >
                  <Image source={option.icon} style={styles.iconStyle} />
                  <View style={styles.textContainer}>
                    <Text style={styles.title}>{option.title}</Text>
                    <Text style={styles.description}>{option.description}</Text>
                  </View>
                  <Ionicons
                    name="chevron-forward-outline"
                    size={20}
                    color="#A0AEC0"
                  />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>

        <View
          style={{
            paddingLeft: 15,
            paddingRight: 15,
          }}
        >
          <View style={styles.card1}>
            <View style={[styles.headerContainer, { paddingHorizontal: 0 }]}>
              <Text style={styles.header}>More</Text>
              <View style={styles.headerLine} />
            </View>
            {SettingsMoreOptions.map((option, index) => (
              <View style={styles.options} key={index}>
                <TouchableOpacity
                  style={[
                    styles.optionContainer,
                    index === SettingsMoreOptions.length - 1 && {
                      borderBottomWidth: 0,
                    },
                  ]}
                  onPress={() => selectedMoreOptionHandler(index)}
                >
                  <Image
                    source={option.icon}
                    style={index === 1 ? styles.iconStyle1 : styles.iconStyle}
                  />
                  <View
                    style={
                      index === 1 ? styles.textContainer1 : styles.textContainer
                    }
                  >
                    <Text style={styles.title}>{option.title}</Text>
                    <Text style={styles.description}>{option.description}</Text>
                  </View>
                  <Ionicons
                    name="chevron-forward-outline"
                    size={20}
                    color="#A0AEC0"
                  />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>

        <View
          style={{
            paddingLeft: 15,
            paddingRight: 15,
            marginBottom: 20,
          }}
        >
          <View style={styles.card1}>
            <View style={styles.options}>
              <TouchableOpacity
                style={styles.optionContainerLogout}
                onPress={handleLogout}
              >
                <View
                  style={{
                    flexDirection: "row",
                    gap: 10,
                  }}
                >
                  <Ionicons name="log-out-outline" size={20} color="#A0AEC0" />
                  <Text style={styles.title}>Logout</Text>
                </View>

                <Ionicons
                  name="chevron-forward-outline"
                  size={20}
                  color="#A0AEC0"
                />
              </TouchableOpacity>
            </View>
            <View style={styles.options}>
              <TouchableOpacity
                style={[styles.optionContainer, { borderBottomWidth: 0 }]}
                onPress={handleDeleteAccount}
              >
                <Icons.DeleteIcon />
                <View style={styles.textContainer}>
                  <Text style={[styles.title, { color: "#DC3333" }]}>
                    Delete Account
                  </Text>
                  <Text style={styles.description}>
                    Permanently delete your account
                  </Text>
                </View>
                <Ionicons
                  name="chevron-forward-outline"
                  size={20}
                  color="#A0AEC0"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 200,
    backgroundColor: "#FFFFFF",
    overflow: "hidden",
    position: "relative",
  },
  options: {
    paddingTop: 5,
    paddingRight: 20,
    paddingBottom: 10,
    paddingLeft: 20,
  },
  leftCircle: {
    position: "absolute",
    zIndex: 1,
    opacity: 0.8,
    height: 160,
    resizeMode: "contain",
  },
  middleCircle: {
    position: "absolute",
    width: 140,
    height: 140,
    top: -50,
    left: "25%",
    zIndex: 0,
    opacity: 0.8,
    resizeMode: "contain",
  },
  rightCircle: {
    position: "absolute",
    right: -20,
    top: -30,
    zIndex: 1,
    opacity: 0.8,
    resizeMode: "contain",
    transform: [{ rotate: "10deg" }],
  },
  backButton: {
    position: "absolute",
    top: 40,
    left: 35,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 8,
    zIndex: 2,
    shadowColor: "#AB4CFE",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.35,
    shadowRadius: 8,
    elevation: 8,

    ...Platform.select({
      android: {
        elevation: 8,
      },
    }),
  },
  centeredTextContainer: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 12,
  },

  profileName: {
    fontSize: 20,
    fontFamily: "Poppins_500Medium",
    color: "#000",
  },
  profileContainer: {
    position: "absolute",
    bottom: 20,
    left: "50%",
    transform: [{ translateX: -50 }],
    alignItems: "center",
    zIndex: 2,
    shadowColor: "#FFE5B4",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#FFC107",
    borderWidth: 7,
    borderColor: "white",
  },
  cameraButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "white",
    borderRadius: 16,
    padding: 6,
    borderWidth: 2,
    borderColor: "#FFF8EE",
  },
  container1: {
    padding: 16,
    gap: 24,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    color: "#9796A1",
    marginLeft: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: "#5F22D9",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
  },
  input1: {
    borderWidth: 1,
    borderColor: "#EEEEEE",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
  },
  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#FFB4B4",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  card1: {
    marginTop: 20,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  headerContainer: {
    marginBottom: 10,
  },
  header: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#172B4D",
    padding: 20,
  },
  headerLine: {
    width: "100%", // Full width
    height: 1, // Line thickness
    backgroundColor: "#EDF2F7", // Line color
  },
  optionContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#EDF2F7",
  },
  optionContainerLogout: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
  },
  title: {
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color: "#172B4D",
  },
  description: {
    fontSize: 12,
    fontFamily: "Poppins_400Regular",
    color: "#7A869A",
    marginTop: 2,
  },

  textContainer1: {
    flex: 1,
    marginLeft: 5,
  },

  iconStyle: {
    width: 18,
    height: 18,
    resizeMode: "contain",
    tintColor: "#C1C7D0",
  },

  iconStyle1: {
    width: 25,
    height: 25,
    resizeMode: "contain",
    tintColor: "#C1C7D0",
  },
});

export default Settings;
