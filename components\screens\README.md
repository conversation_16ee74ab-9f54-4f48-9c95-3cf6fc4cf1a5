# Order Status Screens

This folder contains three screens for handling order payment results and status tracking:

## Components

### 1. OrderSuccessScreen
- Shows when payment is successful
- Displays animated success icon with decorative elements
- Auto-navigates to OrderStatusScreen after 8 seconds
- Includes "Track Order Status" and "Back to home" buttons

### 2. OrderFailedScreen
- Shows when payment fails
- Displays failure icon with error message
- Auto-hides after 8 seconds
- Includes "Please Try Again" and "Back to home" buttons

### 3. OrderStatusScreen
- Shows detailed order information
- Displays order code, restaurant info, pickup details
- Includes countdown timer for pickup window
- Has options for "Ask a friend to pick up", "Need Help", and "Cancel Order"

### 4. PaymentResultHandler
- Main component that orchestrates the flow between screens
- Handles payment status and shows appropriate screen
- Manages navigation between success/failure and order status

## Usage

### ✅ Integration Complete!

The screens have been integrated into your app's navigation system:

**Navigation Stack (RootStack.jsx):**
- `PaymentResult` - Main handler component
- `OrderSuccess` - Individual success screen
- `OrderFailed` - Individual failed screen
- `NewOrderStatus` - Individual order status screen

**Payment Flow Integration:**
- Payment success/failure automatically navigates to appropriate screens
- 8-second auto-transitions work as specified
- Proper navigation to home screen (TabNavigator > Discover)
- Help navigation goes to ContactUs screen

### Navigation Examples

```jsx
// Navigate to payment result (done automatically in PersonalDetailsPopup)
navigation.navigate('PaymentResult', {
  paymentStatus: 'success', // or 'failed'
  orderData: {
    orderCode: "ADF27",
    restaurant: {
      name: "Flywheel Restaurant",
      location: "Kesavadasapuram",
      distance: "3 Km"
    },
    pickupWindow: "6:30-8:30 pm, Today",
    orderId: "YX12358WD",
    items: "1x Meal Bag",
    total: "Rs 150",
    paymentMethod: "UPI",
    orderStatus: "Processing",
    pickupCountdown: "01h:23m:45s"
  }
});

// Navigate directly to individual screens
navigation.navigate('OrderSuccess');
navigation.navigate('OrderFailed');
navigation.navigate('NewOrderStatus');
```

### Individual Screen Usage

```jsx
import OrderSuccessScreen from './components/screens/OrderSuccessScreen';
import OrderFailedScreen from './components/screens/OrderFailedScreen';
import OrderStatusScreen from './components/screens/OrderStatusScreen';

// Use individual screens
<OrderSuccessScreen
  navigation={navigation}
  onTrackOrder={() => navigation.navigate('OrderStatus')}
  onBackToHome={() => navigation.navigate('Home')}
/>
```

## Features

### Auto-Navigation
- Success screen automatically navigates to order status after 8 seconds
- Failed screen automatically hides after 8 seconds

### Customizable Callbacks
All screens accept callback props for handling user actions:
- `onTrackOrder`: Navigate to order tracking
- `onTryAgain`: Retry payment process
- `onBackToHome`: Navigate to home screen
- `onNeedHelp`: Navigate to support/help
- `onCancelOrder`: Cancel the order
- `onAskFriend`: Share pickup request with friends

### Responsive Design
- Adapts to different screen sizes
- Uses proper spacing and typography
- Includes proper touch targets for accessibility

## Styling

The screens use a consistent design system with:
- Primary color: #6C5CE7 (purple)
- Success color: #B794F6 (light purple)
- Error color: #E53E3E (red)
- Background: #FFFFFF (white)
- Text colors: #2D3748 (dark), #718096 (gray), #A0AEC0 (light gray)

## Dependencies

Required packages:
- `react-native`
- `@expo/vector-icons` (for icons)
- `react-native-svg` (for decorative elements in success screen)

Make sure these are installed in your project:

```bash
npm install @expo/vector-icons react-native-svg
# or
yarn add @expo/vector-icons react-native-svg
```
