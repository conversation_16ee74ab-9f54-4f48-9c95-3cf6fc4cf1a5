import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  SafeAreaView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { AppImages } from "../utils/AppImages";
import RecentOrders from "../components/sections/profile/OrderCard";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { useSelector } from "react-redux";
import { getRecentOrders } from "../api/Orders";

const Profile = (props) => {
  const user = useSelector((state) => state.user.user);
  const [activeOrders, setActiveOrders] = useState([]);

  useEffect(() => {
    const getAllOrders = async () => {
      try {
        const res = await getRecentOrders(true);

        console.log("all recent orders");
        console.log(res);
        setActiveOrders(res);
      } catch (error) {
        console.error(error);
      }
    };

    getAllOrders();
  }, []);

  const profileImage = user?.user_profile_picture_url
    ? { uri: user.user_profile_picture_url }
    : AppImages.DEFAULT_AVATAR;

  const displayName = user?.name || "Guest User";
  const displayEmail = user?.email || "email not available";
  const totalSavings = user?.total_savings ?? 0;
  const totalCO2 = parseFloat(user?.total_CO2_saved ?? 0).toFixed(2);
  const totalBags = user?.total_bags_saved ?? 0;

  return (
    <SafeAreaView style={GlobalStyles.androidSafeArea}>
      <View>
        <View style={styles.container}>
          <BackButton navigation={props.navigation} />

          <View style={styles.profileContainer}>
            <View style={styles.avatarContainer}>
              <Image
                style={{
                  width: 47,
                  height: 47,
                  borderRadius: 10,
                  overflow: "hidden",
                }}
                source={profileImage}
              />
            </View>

            <View style={styles.infoContainer}>
              <Text style={styles.name}>{displayName}</Text>
              <Text style={styles.email}>{displayEmail}</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => props.navigation.navigate("Settings")}
          >
            <Ionicons name="settings-outline" size={24} color="#9796A1" />
          </TouchableOpacity>
        </View>

        <View style={styles.container1}>
          <View style={styles.card}>
            <Image source={AppImages.Saving} style={styles.icon} />
            <Text style={styles.amount}>
              {totalSavings}
              <Text style={styles.Subamount}> Rs</Text>
            </Text>
            <Text style={styles.label}>Total Savings</Text>
          </View>

          <View style={styles.card}>
            <Image source={AppImages.CO2} style={styles.c02icon} />
            <Text style={styles.amount}>
              {totalCO2}
              <Text style={styles.Subamount}> Kg</Text>
            </Text>
            <Text style={styles.label}>CO2 Saved</Text>
          </View>
        </View>

        <View
          style={{
            paddingLeft: 14,
            paddingRight: 14,
            backgroundColor: "#F6F6F6",
          }}
        >
          <View style={styles.container2}>
            <Text style={styles.title}>Surprise Bags Ordered</Text>
            <View
              style={{
                flexDirection: "row",
                gap: 5,
                alignItems: "center",
              }}
            >
              <Image
                style={{
                  height: 30,
                  width: 30,
                }}
                source={AppImages.SHOPIINGBAG}
              />
              <Text
                style={{
                  fontFamily: "Poppins_600SemiBold",
                  fontSize: 36,
                  fontWeight: "bold",
                }}
              >
                {totalBags}
              </Text>
            </View>
          </View>
        </View>

        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            paddingLeft: 25,
            paddingRight: 25,
            marginTop: 20,
          }}
        >
          <Text style={styles.heading}>Your Orders</Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <TouchableOpacity
              onPress={() => props.navigation.navigate("YourOrders")}
            >
              <Text
                style={{
                  fontFamily: "Poppins_400Regular",
                  color: "#5F22D9",
                }}
              >
                View all
              </Text>
            </TouchableOpacity>
            <Image
              style={{
                resizeMode: "contain",
                marginLeft: 5,
              }}
              source={AppImages.VIEW_ALL}
            />
          </View>
        </View>

        <View>
          <RecentOrders
            activeOrders={activeOrders}
            navigation={props.navigation}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#F6F6F6",
  },
  backButton: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
  },
  profileContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 8,
  },
  avatarContainer: {
    width: 45,
    height: 45,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FFD700",
  },
  infoContainer: {
    marginLeft: 12,
  },
  name: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#201F1F",
    fontWeight: "bold",
  },
  email: {
    fontFamily: "Poppins_500Medium",
    fontSize: 11,
    color: "#201F1F",
  },
  settingsButton: {
    padding: 8,
  },

  container1: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 16,
    padding: 16,
    backgroundColor: "#F6F6F6",
  },
  topRightImage: {
    position: "absolute",
    top: -20,
    right: -20,
    width: 100,
    height: 100,
    opacity: 0.1,
    resizeMode: "contain",
    backgroundColor: "#3b0c99",
  },
  bottomLeftImage: {
    position: "absolute",
    bottom: -20,
    left: -20,
    width: 100,
    height: 100,
    opacity: 0.1,
    resizeMode: "contain",
    backgroundColor: "#3b0c99",
  },

  container2: {
    backgroundColor: "#fff",
    overflow: "hidden",
    borderRadius: 30,
    padding: 10,
    paddingLeft: 20,
    paddingRight: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 5,
  },

  heading: {
    fontFamily: "Poppins_500Medium",
    fontSize: 16,
    color: "#5F22D9",
  },
  card: {
    flex: 1,
    width: 150,
    padding: 10,
    backgroundColor: "#ffffff",
    borderRadius: 24,
    shadowColor: "#6434F826",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
  },
  title: {
    fontFamily: "Poppins_400Regular",
    color: "#464255",
  },
  icon: {
    flexDirection: "row",
    justifyContent: "flex-start",
    width: 35,
    height: 35,
    tintColor: "#6c63ff",
  },
  c02icon: {
    flexDirection: "row",
    justifyContent: "flex-start",
    width: 45,
    height: 45,
    tintColor: "#6c63ff",
  },
  amount: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 38,
    width: "100%",
    color: "#464255",
    textAlign: "center",
  },
  Subamount: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 20,
    color: "#464255",
    marginLeft: 8, // Add gap between "75" and "Rs"
  },
  label: {
    width: "100%",
    fontFamily: "Poppins_400Regular",
    fontSize: 16,
    color: "#464255",
    marginBottom: 20,
    textAlign: "center",
  },
});

export default Profile;
