import { View, Text, StyleSheet } from "react-native";
import React from "react";
import { Icons } from "../../../utils/svgs";
import { Ionicons } from "@expo/vector-icons";

const Address = ({ address }) => {
  return (
    <View style={styles.rows}>
      <View style={styles.cardsRow}>
        <Icons.PinIcon />
        <View style={{ width: "80%" }}>
          <Text style={styles.textStyle}>{address}</Text>
        </View>
      </View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <Ionicons name="chevron-forward-outline" size={20} color="#111719" />
      </View>
    </View>
  );
};

export default Address;

const styles = StyleSheet.create({
  rows: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 15,
    marginTop: 10,
    paddingLeft: 30,
    paddingRight: 30,
  },
  cardsRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 13,
  },
  textStyle: {
    fontSize: 12,
    fontFamily: "Poppins_400Regular",
  },
});
