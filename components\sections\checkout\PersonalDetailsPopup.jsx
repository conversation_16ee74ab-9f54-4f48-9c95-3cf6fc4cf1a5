import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Image,
  Animated,
  Easing,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { AppImages } from "../../../utils/AppImages";

function PersonalDetailsPopup({ firstModalVisible, setFirstModalVisible }) {
  const [activeInput, setActiveInput] = useState(null);
  const [successPopupVisible, setSuccessPopupVisible] = useState(false);
  const [slideAnim] = useState(new Animated.Value(300));

  useEffect(() => {
    if (firstModalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      slideAnim.setValue(300);
    }
  }, [firstModalVisible]);

  const handleSave = () => {
    setSuccessPopupVisible(true);
  };

  const handleCloseSuccessPopup = () => {
    setSuccessPopupVisible(false);
    setFirstModalVisible(false);
  };

  const renderInputField = (label, defaultValue, keyboardType) => (
    <View style={styles.inputGroup}>
      <Text style={styles.label}>{label}</Text>
      <TextInput
        style={[
          styles.input,
          activeInput === label.toLowerCase().replace(" ", "") &&
            styles.activeInput,
        ]}
        placeholder={label}
        defaultValue={defaultValue}
        keyboardType={keyboardType}
        placeholderTextColor="#999"
        onFocus={() => setActiveInput(label.toLowerCase().replace(" ", ""))}
        onBlur={() => setActiveInput(null)}
      />
    </View>
  );

  return (
    <>
      <Modal
        animationType="none"
        transparent={true}
        visible={firstModalVisible}
        onRequestClose={() => {
          setFirstModalVisible(false);
          slideAnim.setValue(300);
        }}
        onDismiss={() => {
          slideAnim.setValue(300);
        }}
      >
        <KeyboardAvoidingView
          style={styles.popupBackground}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.popupBackground}>
            <Animated.View
              style={[
                styles.cardContainer,
                { transform: [{ translateY: slideAnim }] },
              ]}
            >
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setFirstModalVisible(false)}
              >
                <Ionicons name="close" size={20} color="#000" />
              </TouchableOpacity>
              <View style={styles.container1}>
                {renderInputField("Full name", "User Name", "default")}
                {renderInputField("E-mail", "<EMAIL>", "email-address")}
                {renderInputField(
                  "Phone Number",
                  "+91 940986 8786",
                  "phone-pad"
                )}
                <View style={styles.buttonWrapper}>
                  <TouchableOpacity
                    onPress={handleSave}
                    style={styles.button}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.buttonText}>Save</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </View>

          {successPopupVisible && (
            <TouchableOpacity
              style={styles.absoluteCenter}
              activeOpacity={1}
              onPress={handleCloseSuccessPopup}
            >
              <View style={styles.successCardContainer}>
                <Image
                  source={AppImages.PATTERN}
                  style={styles.backgroundIMG}
                />
                <Image
                  source={AppImages.ORERSUCCESS}
                  style={styles.successIMG}
                />
                <Text style={styles.successMessage}>Order Placed!</Text>
                <View style={styles.orderInfo}>
                  <Text style={styles.thankYouText}>
                    Thanks for ordering from
                  </Text>
                  <Text style={styles.plentiText}>Plenti</Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
        </KeyboardAvoidingView>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  popupBackground: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  cardContainer: {
    width: "100%",
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    marginBottom: 10,
  },
  closeButton: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  container1: {
    padding: 16,
    gap: 24,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    color: "#9796A1",
    marginLeft: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: "#EEEEEE",
    borderRadius: 12,
    padding: 20,
    fontSize: 16,
    color: "#000",
  },
  activeInput: {
    borderColor: "#5F22D9",
  },
  buttonWrapper: {
    alignItems: "center",
  },
  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    shadowColor: "#FFB4B4",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  absoluteCenter: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  successCardContainer: {
    width: "90%",
    maxWidth: 400,
    backgroundColor: "#FFF",
    borderRadius: 20,
    padding: 20,
    alignItems: "center",
    elevation: 10,
    shadowColor: "#000",
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },
  backgroundIMG: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    borderRadius: 20,
    resizeMode: "cover",
  },
  successIMG: {
    width: 150,
    height: 150,
    marginVertical: 20,
    resizeMode: "contain",
  },
  successMessage: {
    fontSize: 26,
    color: "#6B50F6",
    marginVertical: 10,
  },
  orderInfo: {
    flexDirection: "row",
    gap: 5,
    alignItems: "center",
  },
  thankYouText: {
    fontSize: 16,
  },
  plentiText: {
    color: "#6B50F6",
    fontSize: 16,
  },
});

export default PersonalDetailsPopup;
