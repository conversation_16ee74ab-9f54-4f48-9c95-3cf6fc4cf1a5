import React from "react";
import {
  Image,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import GlobalStyles from "../styles/GlobalStyles";
import { AppImages } from "../utils/AppImages";

function Thankyou({ navigation }) {
  return (
    <View
      style={{
        backgroundColor: "#5F22D9",
        height: "100%",
      }}
    >
      <View style={styles.container}>
        <Image source={AppImages.E10} style={styles.leftCircle} />
        <Image source={AppImages.E4} style={styles.middleCircle} />
        <Image source={AppImages.E11} style={styles.rightCircle} />

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <View
        style={{
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Image source={AppImages.THANK_HEART} />
        <Text style={styles.thankHeading}>Thankyou</Text>
        <Text style={styles.thanksSubHeading}>
          Thanks for your feedback &lt;3
        </Text>

        <TouchableOpacity
          style={styles.button}
          activeOpacity={0.8}
          onPress={() =>
            navigation.navigate("TabNavigator", {
              screen: "HomeNavigator",
              params: { screen: "Home" },
            })
          }
        >
          <Text style={styles.buttonText}>Close</Text>
        </TouchableOpacity>
      </View>

      <View
        style={{
          flex: 1,
          flexDirection: "row",
          alignItems: "flex-end",
          justifyContent: "flex-end",
        }}
      >
        <Image
          style={{
            height: "90%",
            width: "100%",
          }}
          source={AppImages.T_LOGO}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 200,
    backgroundColor: "#5F22D9",
    overflow: "hidden",
    position: "relative",
  },
  leftCircle: {
    position: "absolute",
    zIndex: 1,
    opacity: 0.8,
    resizeMode: "contain",
  },
  middleCircle: {
    position: "absolute",
    width: 140,
    height: 140,
    top: -50,
    left: "25%",
    zIndex: 0,
    opacity: 0.8,
    resizeMode: "contain",
  },
  rightCircle: {
    position: "absolute",
    right: -20,
    top: -30,
    zIndex: 1,
    opacity: 0.8,
    resizeMode: "contain",
    transform: [{ rotate: "10deg" }],
  },
  backButton: {
    position: "absolute",
    top: 40,
    left: 35,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 8,
    zIndex: 2,
    shadowColor: "#AB4CFE",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.35,
    shadowRadius: 8,
    elevation: 8,

    ...Platform.select({
      android: {
        elevation: 8,
      },
    }),
  },
  profileContainer: {
    position: "absolute",
    bottom: 20,
    left: "50%",
    transform: [{ translateX: -50 }],
    alignItems: "center",
    zIndex: 2,
    shadowColor: "#FFE5B4",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#FFC107",
    borderWidth: 7,
    borderColor: "white",
  },
  cameraButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "white",
    borderRadius: 16,
    padding: 6,
    borderWidth: 2,
    borderColor: "#FFF8EE",
  },
  container1: {
    padding: 16,
    gap: 24,
  },
  inputGroup: {
    gap: 8,
  },
  thankHeading: {
    fontSize: 36,
    marginTop: 5,
    fontFamily: "Poppins_600SemiBold",
    color: "#FFF",
  },
  thanksSubHeading: {
    fontSize: 14,
    marginTop: 5,
    fontFamily: "Poppins_400Regular",
    color: "#FFF",
  },
  label: {
    fontSize: 16,
    color: "#9796A1",
    marginLeft: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: "#5F22D9",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
  },
  input1: {
    borderWidth: 1,
    borderColor: "#EEEEEE",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
  },
  button: {
    backgroundColor: "#FFFFFF",
    width: 248,
    marginTop: 15,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
  },
  buttonText: {
    color: "#161616",
    fontSize: 16,
  },
});

export default Thankyou;
