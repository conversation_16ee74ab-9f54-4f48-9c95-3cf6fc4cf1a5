import { useCallback, useEffect, useRef, useState } from "react";
import { Image, SafeAreaView, StyleSheet, View } from "react-native";
import MapView, { PROVIDER_GOOGLE, Marker } from "react-native-maps";
import Header from "../components/sections/browse/Header";
import SearchArea from "../components/sections/browse/SearchArea";
import GlobalStyles from "../styles/GlobalStyles";
import { useSelector } from "react-redux";
import { GetItemApi, GetVendorsApi } from "../api/Discover";
import { useFocusEffect } from "@react-navigation/native";
import CartPopup from "../components/sections/home/<USER>";
import RestaurantDetails from "../components/sections/browse/RestaurantDetails";

const Browse = ({ navigation }) => {
  const address = useSelector((state) => state.user.address);
  const mapRef = useRef(null);

  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [itemPopupVisible, setItemPopupVisible] = useState({
    visible: false,
    vendor: null,
    items: [],
  });

  const getVendors = useCallback(async () => {
    if (!address?.latitude || !address?.longitude) {
      console.log("Address not available, skipping vendor fetch.");
      setLoading(false); // Stop loading if we can't fetch
      return;
    }

    try {
      setLoading(true);
      const res = await GetVendorsApi(address.latitude, address.longitude, {});
      setVendors(res || []);
    } catch (error) {
      console.error("Error fetching vendors:", error);
      setVendors([]); // Clear vendors on error
    } finally {
      setLoading(false);
    }
  }, [address]);

  useFocusEffect(
    useCallback(() => {
      getVendors();
    }, [getVendors])
  );

  const handleVendorSelect = (vendor) => {
    setSelectedVendor(vendor);
  };

  const getVendorItems = async (vendor) => {
    try {
      const res = await GetItemApi({ item_ids: vendor?.item_ids });
      setItemPopupVisible({
        visible: true,
        vendor: vendor,
        items: res,
      });
    } catch (error) {
      error.response.status === 404 &&
        setItemPopupVisible({
          visible: true,
          vendor: vendor,
          items: [],
        });
    }
  };

  useEffect(() => {
    if (address?.latitude && address?.longitude && mapRef.current) {
      mapRef.current.animateToRegion(
        {
          latitude: address.latitude,
          longitude: address.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        },
        1000 // duration in ms
      );
    }
  }, [address]);

  if (loading) {
    return (
      <SafeAreaView
        style={[GlobalStyles.androidSafeArea, { backgroundColor: "#fff" }]}
      >
        <Header navigation={navigation} />
        <MapView
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          initialRegion={{
            latitude: address?.latitude,
            longitude: address?.longitude,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
          showsUserLocation={true}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[GlobalStyles.androidSafeArea, { backgroundColor: "#fff" }]}
    >
      <Header navigation={navigation} />
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        initialRegion={{
          latitude: address?.latitude,
          longitude: address?.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
        showsUserLocation={true}
      >
        {vendors
          .filter(
            (vendor) =>
              vendor && vendor.latitude && vendor.longitude && vendor.logo_url
          )
          .map((vendor) => (
            <Marker
              onPress={() => handleVendorSelect(vendor)}
              key={vendor.vendor_id}
              coordinate={{
                latitude: vendor.latitude,
                longitude: vendor.longitude,
              }}
            >
              <View style={styles.markerContainer}>
                <Image
                  source={{ uri: vendor.logo_url }}
                  style={styles.markerImage}
                />
              </View>
            </Marker>
          ))}
      </MapView>

      {selectedVendor && (
        <RestaurantDetails
          selectedVendor={selectedVendor}
          getVendorItems={getVendorItems}
        />
      )}
      <SearchArea />

      {itemPopupVisible.visible && (
        <CartPopup
          modalVisible={itemPopupVisible}
          setModalVisible={setItemPopupVisible}
          navigation={navigation}
        />
      )}
    </SafeAreaView>
  );
};

export default Browse;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: "relative",
  },
  map: {
    flex: 1,
  },
  markerContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "white",
    padding: 2,
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 6,
  },
  markerImage: {
    width: "100%",
    height: "100%",
    borderRadius: 20,
  },
});
