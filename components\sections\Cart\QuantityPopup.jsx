import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Easing,
  TextInput,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { convertTime } from "../home/<USER>";
import { CheckoutApi } from "../../../api/Checkout";
import { useDispatch } from "react-redux";
import {
  setSelectedItem,
  setSelectedVendor,
} from "../../../redux/slices/cartSlice";
import Toast from "../../common/Toast/Toast";

const { width, height } = Dimensions.get("window");

function QuantityPopup({
  modalVisible,
  setModalVisible,
  navigation,
  item,
  vendor,
  diet,
}) {
  const [slideAnim] = useState(new Animated.Value(300));
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const dispatch = useDispatch();

  React.useEffect(() => {
    if (modalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    }
  }, [modalVisible]);

  const handleQuantity = (type) => {
    if (type === "inc") {
      setQuantity(quantity + 1);
    } else {
      if (quantity > 1) {
        setQuantity(quantity - 1);
      }
    }
  };

  const handleCheckout = async () => {
    try {
      setLoading(true);

      const checkoutPayload = {
        vendor_id: vendor?.vendor_id,
        dislikes: [], // Fixed: changed from 'dislike' to 'dislikes'
        checkout_items: [
          {
            item_id: item?.id,
            quantity: quantity,
            diet: diet, // Added back: diet field is actually part of the API spec
          },
        ],
      };

      console.log("🛒 Checkout payload:", JSON.stringify(checkoutPayload, null, 2));
      console.log("🏪 Vendor details:", vendor);
      console.log("🍽️ Item details:", item);
      console.log("🥗 Diet preference:", diet);

      const response = await CheckoutApi(checkoutPayload);

      console.log("✅ Checkout successful:", JSON.stringify(response, null, 2));

      setLoading(false);
      setModalVisible(false);
      dispatch(setSelectedItem(item));
      dispatch(setSelectedVendor(vendor));

      // Show success message
      setToastMessage("Item added to cart successfully!");
      setShowToast(true);
      setTimeout(() => {
        setShowToast(false);
        navigation.navigate("Cart");
      }, 1500);
    } catch (error) {
      console.log("❌ Checkout API Error");
      console.log("Error object:", error);

      // Enhanced error logging
      if (error?.response) {
        console.log("Response status:", error.response.status);
        console.log("Response data:", error.response.data);
        console.log("Response headers:", error.response.headers);
      } else if (error?.request) {
        console.log("Request made but no response:", error.request);
      } else {
        console.log("Error setting up request:", error.message);
      }

      setLoading(false);

      const errorMsg =
        error?.response?.data?.detail ||
        error?.message ||
        "Something went wrong";

      if (errorMsg.includes("Insufficient quantity available")) {
        setToastMessage("Insufficient item quantity");
        setShowToast(true);
      } else {
        setToastMessage("Checkout failed. Please try again.");
        setShowToast(true);
      }
    }
  };

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(false);
        slideAnim.setValue(300);
      }}
      onDismiss={() => {
        slideAnim.setValue(300);
      }}
    >
      <View style={styles.popupBackground}>
        <Animated.View
          style={[
            styles.cardContainer,
            { transform: [{ translateY: slideAnim }] },
          ]}
        >
          <View
            style={styles.modalOverlay}
            onTouchEnd={() => setModalVisible(false)}
          />
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <View></View>
              <Text style={styles.restaurantName1}>{vendor?.vendor_name}</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons name="close" size={20} color="#000" />
              </TouchableOpacity>
            </View>
            <Text style={styles.pickupTime}>
              Pick up Today {convertTime(item?.window_start_time)} -{" "}
              {convertTime(item?.window_end_time)}
            </Text>
            <View style={styles.line3}></View>

            <View style={styles.quantityContainer}>
              <Text style={styles.selectQuantityText}>Select quantity</Text>
              <View style={styles.quantityControls}>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => handleQuantity("dec")}
                >
                  <Text style={styles.quantityButtonText}>-</Text>
                </TouchableOpacity>
                <Text style={styles.quantityText}>{quantity}</Text>
                <TouchableOpacity
                  style={styles.quantityButton1}
                  onPress={() => handleQuantity("inc")}
                >
                  <Text style={styles.quantityButtonText1}>+</Text>
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              onPress={handleCheckout}
              style={styles.checkoutButton}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#FFF" />
              ) : (
                <Text style={styles.checkoutText}>Add to Cart</Text>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
      {showToast && (
        <Toast key={toastMessage} type="error" message={toastMessage} />
      )}
    </Modal>
  );
}

const styles = StyleSheet.create({
  popupBackground: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    position: "absolute",
    bottom: 0,
    width: width,
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  line3: {
    height: 1,
    marginTop: 10,
    backgroundColor: "#B0B0B0",
  },
  header: {
    flexDirection: "row",
    gap: 5,
    justifyContent: "space-between",
    alignItems: "center",
  },
  restaurantName1: {
    color: "#545556",
    fontSize: 18,
    fontFamily: "Poppins_400Medium",
  },
  pickupTime: {
    flexDirection: "row",
    textAlign: "center",
    fontSize: 14,
    color: "#858992",
    fontFamily: "Poppins_400Regular",
    marginTop: 5,
    marginBottom: 5,
  },
  quantityContainer: {
    alignItems: "center",
    marginBottom: 20,
  },
  selectQuantityText: {
    fontSize: 16,
    marginTop: 10,
    color: "#545556",
    fontFamily: "Poppins_400Medium",
    marginBottom: 10,
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
  },
  quantityButton: {
    backgroundColor: "#FFF",
    height: 40,
    width: 40,
    borderRadius: 20,
    marginHorizontal: 10,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderColor: "#5F22D9",
    borderWidth: 1,
  },
  quantityButton1: {
    backgroundColor: "#5F22D9",
    height: 40,
    width: 40,
    borderRadius: 20,
    marginHorizontal: 10,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quantityButtonText: {
    fontSize: 20,
    color: "#000",
    fontFamily: "Poppins_400Regular",
    textAlignVertical: "center",
    lineHeight: 40,
  },
  quantityButtonText1: {
    fontSize: 20,
    color: "#FFF",
    fontFamily: "Poppins_400Regular",
    lineHeight: 40,
    textAlignVertical: "center",
  },
  quantityText: {
    fontSize: 18,
    fontWeight: "bold",
  },
  checkoutButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 15,
    borderRadius: 30,
    alignItems: "center",
    marginBottom: 10,
  },
  checkoutText: {
    color: "#FFF",
    fontSize: 16,
  },
});

export default QuantityPopup;
