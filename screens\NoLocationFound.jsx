import {
  View,
  Text,
  Image,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Pressable,
  SafeAreaView,
} from "react-native";
import React, {  useState } from "react";
import { AppImages } from "../utils/AppImages";

import { MaterialIcons } from "@expo/vector-icons";
import GlobalStyles from "../styles/GlobalStyles";

import { Icons } from "../utils/svgs";

const NoLocationFound = ({ navigation }) => {
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <SafeAreaView style={[styles.container, GlobalStyles.androidSafeArea]}>
      <View style={styles.headerContainer}>
        <View style={styles.colmun}>
          <View style={styles.row}>
            <Text style={styles.text}>Current Location</Text>
            <Image style={styles.arrowDown} source={AppImages.ARROW_DOWN} />
          </View>
          <Text style={styles.loctionText}>4102 Plattom, Trivandrum</Text>
        </View>

        <Pressable onPress={() => navigation.navigate("Profile")}>
          <Image style={styles.profileIMG} source={AppImages.PROFILE_IMG} />
        </Pressable>
      </View>
      <View style={styles.headerContainer}>
        <View style={styles.inputContainer}>
          <MaterialIcons
            style={styles.searchIcon}
            name="search"
            size={24}
            color="#434B67"
          />

          <TextInput
            style={styles.input}
            placeholder="Search for food or restaurant..."
          />
        </View>

        <TouchableOpacity
          onPress={() => setModalVisible(true)}
          style={styles.filter}
        >
          <Icons.Filter />
        </TouchableOpacity>
      </View>
      <View
        style={{
          flex: 1,
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          gap: 10,
          paddingLeft: 30,
          paddingRight: 30,
          marginBottom: 200,
        }}
      >
        <Image source={AppImages.NOLOCATION} />
        <Text
          style={{
            textAlign: "center",
            fontSize: 14,
            fontFamily: "Poppins_500Medium",
          }}
        >
          Sorry, no restaurants or bakeries are available at your location right
          now!
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default NoLocationFound;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 15,
    paddingRight: 15,
  },



  text: {
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
    color: "#8C9099",
  },
  inputContainer: {
    position: "relative",
    flexDirection: "row",
    alignItems: "center",
  },

  searchIcon: {
    position: "absolute",
    left: 15,
    top: "35%",
    marginRight: 5,
    transform: [{ translateY: -10 }],
    width: 20,
    height: 20,
  },
  input: {
    minHeight: 51,
    minWidth: 256,
    borderColor: "#EFEFEF",
    borderWidth: 3,
    paddingLeft: 35,
    paddingRight: 15,
    marginBottom: 20,
    borderRadius: 10,
    fontSize: 17,
  },
  heading: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 21.6,
    color: "#323643",
  },
  filter: {
    height: 51,
    width: 51,
    borderRadius: 14,
    backgroundColor: "#F2F2F2",
    justifyContent: "center",
    alignItems: "center",
    padding: 5,
    shadowColor: "#4d4242",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  colmun: {
    flexDirection: "column",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    rowGap: 5,
  },
  loctionText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    color: "#5F22D9",
  },
  arrowDown: {
    marginLeft: 3,
    fontColor: "#8C9099",
  },
  profileIMG: {
    width: 60,
    height: 60,
  },
  rowCenter: {
    flexDirection: "row",
    alignItems: "center",
  },
  heroIMGContainer: {
    flex: 2,
    marginRight: -1,
  },
  heroIMG: {
    resizeMode: "contain",
    width: "100%",
  },
});
