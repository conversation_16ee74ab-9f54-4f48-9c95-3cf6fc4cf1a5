import Svg, { Circle, Defs, Path } from "react-native-svg";

export const TabIcons = {
  Discover: () => (
    <Svg
      width="24"
      height="26"
      viewBox="0 0 24 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M11.969 0.421967C8.82485 0.421967 5.80943 1.75541 3.58618 4.12887C1.36294 6.50233 0.114014 9.72144 0.114014 13.078C0.114014 16.4346 1.36294 19.6537 3.58618 22.0272C5.80943 24.4006 8.82485 25.734 11.969 25.734C15.1131 25.734 18.1286 24.4006 20.3518 22.0272C22.5751 19.6537 23.824 16.4346 23.824 13.078C23.824 9.72144 22.5751 6.50233 20.3518 4.12887C18.1286 1.75541 15.1131 0.421967 11.969 0.421967ZM16.4031 9.33404L14.7161 15.4651C14.6811 15.5931 14.6163 15.7097 14.5281 15.8041C14.4398 15.8985 14.3308 15.9677 14.2109 16.0053L8.46289 17.8116C8.3333 17.8524 8.19601 17.8549 8.06519 17.8189C7.93437 17.7828 7.81513 17.7096 7.72021 17.607C7.6253 17.5043 7.55819 17.3761 7.52612 17.236C7.49405 17.0958 7.4982 16.9491 7.53809 16.8113L9.29395 10.7443C9.32976 10.6203 9.39333 10.5076 9.47925 10.4159C9.56517 10.3242 9.671 10.2562 9.78711 10.218L15.4661 8.34871C15.5949 8.30634 15.732 8.30197 15.863 8.33613C15.994 8.37029 16.1139 8.44166 16.21 8.54269C16.306 8.64372 16.3747 8.77066 16.4087 8.90999C16.4427 9.04931 16.4408 9.19583 16.4031 9.33404Z"
        fill="rgba(211, 209, 216, 1)"
      />
    </Svg>
  ),

  DiscoverActive: () => (
    <Svg
      width="24"
      height="26"
      viewBox="0 0 24 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M11.969 0.421967C8.82485 0.421967 5.80943 1.75541 3.58618 4.12887C1.36294 6.50233 0.114014 9.72144 0.114014 13.078C0.114014 16.4346 1.36294 19.6537 3.58618 22.0272C5.80943 24.4006 8.82485 25.734 11.969 25.734C15.1131 25.734 18.1286 24.4006 20.3518 22.0272C22.5751 19.6537 23.824 16.4346 23.824 13.078C23.824 9.72144 22.5751 6.50233 20.3518 4.12887C18.1286 1.75541 15.1131 0.421967 11.969 0.421967ZM16.4031 9.33404L14.7161 15.4651C14.6811 15.5931 14.6163 15.7097 14.5281 15.8041C14.4398 15.8985 14.3308 15.9677 14.2109 16.0053L8.46289 17.8116C8.3333 17.8524 8.19601 17.8549 8.06519 17.8189C7.93437 17.7828 7.81513 17.7096 7.72021 17.607C7.6253 17.5043 7.55819 17.3761 7.52612 17.236C7.49405 17.0958 7.4982 16.9491 7.53809 16.8113L9.29395 10.7443C9.32976 10.6203 9.39333 10.5076 9.47925 10.4159C9.56517 10.3242 9.671 10.2562 9.78711 10.218L15.4661 8.34871C15.5949 8.30634 15.732 8.30197 15.863 8.33613C15.994 8.37029 16.1139 8.44166 16.21 8.54269C16.306 8.64372 16.3747 8.77066 16.4087 8.90999C16.4427 9.04931 16.4408 9.19583 16.4031 9.33404Z"
        fill="rgba(95, 34, 217, 1)"
      />
    </Svg>
  ),

  Search: () => (
    <Svg
      width="25"
      height="27"
      viewBox="0 0 25 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M18 20.2162L24 25.5541"
        stroke="rgba(211, 209, 216, 1)"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <Path
        d="M11 22.3514C16.5228 22.3514 21 17.5717 21 11.6757C21 5.77966 16.5228 1 11 1C5.47715 1 1 5.77966 1 11.6757C1 17.5717 5.47715 22.3514 11 22.3514Z"
        stroke="rgba(211, 209, 216, 1)"
        strokeWidth="2"
      />
    </Svg>
  ),

  SearchActive: () => (
    <Svg
      width="25"
      height="27"
      viewBox="0 0 25 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M18 20.2162L24 25.5541"
        stroke="rgba(95, 34, 217, 1)"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <Path
        d="M11 22.3514C16.5228 22.3514 21 17.5717 21 11.6757C21 5.77966 16.5228 1 11 1C5.47715 1 1 5.77966 1 11.6757C1 17.5717 5.47715 22.3514 11 22.3514Z"
        stroke="rgba(95, 34, 217, 1)"
        strokeWidth="2"
      />
    </Svg>
  ),

  Favorite: () => (
    <Svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M15.8663 1.68285e-08C14.8402 0.000191092 13.8326 0.291934 12.9471 0.845269C12.0615 1.3986 11.3299 2.19359 10.8273 3.1486C10.3246 2.19337 9.59273 1.39823 8.70691 0.844882C7.82109 0.291533 6.81324 -8.08843e-05 5.78692 1.68285e-08C4.21506 0.0906285 2.73644 0.824704 1.65864 2.04952C0.580845 3.27434 -0.0131502 4.8956 0.000220938 6.57605C0.000220938 15.1455 10.8273 21.124 10.8273 21.124C10.8273 21.124 21.6544 15.1485 21.6544 6.57605C21.6678 4.89535 21.0736 3.27388 19.9955 2.04902C18.9174 0.824157 17.4384 0.0902479 15.8663 1.68285e-08V1.68285e-08Z"
        fill="rgba(211, 209, 216, 1)"
      />
    </Svg>
  ),
  FavoriteActive: () => (
    <Svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M15.8663 1.68285e-08C14.8402 0.000191092 13.8326 0.291934 12.9471 0.845269C12.0615 1.3986 11.3299 2.19359 10.8273 3.1486C10.3246 2.19337 9.59273 1.39823 8.70691 0.844882C7.82109 0.291533 6.81324 -8.08843e-05 5.78692 1.68285e-08C4.21506 0.0906285 2.73644 0.824704 1.65864 2.04952C0.580845 3.27434 -0.0131502 4.8956 0.000220938 6.57605C0.000220938 15.1455 10.8273 21.124 10.8273 21.124C10.8273 21.124 21.6544 15.1485 21.6544 6.57605C21.6678 4.89535 21.0736 3.27388 19.9955 2.04902C18.9174 0.824157 17.4384 0.0902479 15.8663 1.68285e-08V1.68285e-08Z"
        fill="rgba(95, 34, 217, 1)"
      />
    </Svg>
  ),

  Cart: () => (
    <Svg
      width="22"
      height="27"
      viewBox="0 0 22 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M20.9723 11.4286C20.864 10.398 20.4166 9.44174 19.71 8.73005C19.0033 8.01836 18.0833 7.59738 17.1132 7.54185V7.23889C17.1141 6.37687 16.9553 5.52327 16.6462 4.72692C16.3371 3.93058 15.8836 3.20723 15.3118 2.5986C14.7413 1.98813 14.0633 1.50405 13.317 1.17405C12.5707 0.844055 11.7707 0.674671 10.9629 0.675679C9.33226 0.676316 7.76865 1.3681 6.61561 2.59905C5.46258 3.82999 4.81457 5.4994 4.81398 7.24021V7.54654C3.8568 7.61916 2.95353 8.04756 2.26146 8.75719C1.56939 9.46682 1.13228 10.4129 1.02574 11.431L0 21.4064C0.00506676 22.7439 0.507103 24.0246 1.39596 24.9672C2.28481 25.9098 3.48801 26.4372 4.74087 26.4337H17.2594C18.5123 26.4372 19.7152 25.9098 20.604 24.9672C21.4929 24.0246 21.9949 22.7439 22 21.4064L20.9723 11.4286ZM6.61973 7.25092C6.61959 6.64207 6.73168 6.03916 6.94983 5.47661C7.16797 4.91407 7.48797 4.40297 7.89118 3.9724C8.2944 3.54182 8.7729 3.20017 9.29978 2.96714C9.82667 2.73411 10.3915 2.6143 10.9618 2.6143C11.5322 2.61372 12.0971 2.73335 12.6241 2.96641C13.1511 3.19946 13.63 3.54121 14.033 3.97225C14.4369 4.40235 14.7572 4.91339 14.9757 5.47603C15.1942 6.03866 15.3065 6.64185 15.3061 7.25092V7.53216H6.61973V7.25092ZM7.47644 14.0703C7.33314 14.0704 7.19109 14.0404 7.05867 13.982C6.92625 13.9235 6.80599 13.8377 6.70466 13.7295C6.60334 13.6213 6.52306 13.4929 6.46829 13.3516C6.41352 13.2102 6.38541 13.0587 6.38556 12.9057C6.38541 12.7527 6.41352 12.6012 6.46829 12.4599C6.52306 12.3185 6.60334 12.19 6.70466 12.0818C6.80599 11.9736 6.92625 11.8879 7.05867 11.8294C7.19109 11.771 7.33314 11.741 7.47644 11.7411C7.61994 11.7408 7.76208 11.7706 7.89476 11.829C8.02743 11.8874 8.14796 11.973 8.24959 12.0812C8.35121 12.1894 8.43197 12.3178 8.48706 12.4593C8.54214 12.6007 8.57046 12.7525 8.57061 12.9057C8.57046 13.0589 8.54214 13.2105 8.48706 13.352C8.43197 13.4935 8.35121 13.6219 8.24959 13.7301C8.14796 13.8382 8.02743 13.9239 7.89476 13.9823C7.76208 14.0406 7.61994 14.0706 7.47644 14.0703ZM14.526 14.0703C14.3825 14.0706 14.2404 14.0406 14.1077 13.9823C13.975 13.9239 13.8545 13.8382 13.7529 13.7301C13.6513 13.6219 13.5705 13.4935 13.5154 13.352C13.4603 13.2105 13.432 13.0589 13.4319 12.9057C13.432 12.7525 13.4603 12.6007 13.5154 12.4593C13.5705 12.3178 13.6513 12.1894 13.7529 12.0812C13.8545 11.973 13.975 11.8874 14.1077 11.829C14.2404 11.7706 14.3825 11.7408 14.526 11.7411C14.6693 11.741 14.8111 11.771 14.9435 11.8294C15.0759 11.8879 15.1965 11.9736 15.2978 12.0818C15.3991 12.19 15.4794 12.3185 15.5342 12.4599C15.589 12.6012 15.6171 12.7527 15.6169 12.9057C15.6171 13.0587 15.589 13.2102 15.5342 13.3516C15.4794 13.4929 15.3991 13.6213 15.2978 13.7295C15.1965 13.8377 15.0759 13.9235 14.9435 13.982C14.8111 14.0404 14.6693 14.0704 14.526 14.0703Z"
        fill="rgba(211, 209, 216, 1)"
      />
    </Svg>
  ),
  CartActive: () => (
    <Svg
      width="22"
      height="27"
      viewBox="0 0 22 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M20.9723 11.4286C20.864 10.398 20.4166 9.44174 19.71 8.73005C19.0033 8.01836 18.0833 7.59738 17.1132 7.54185V7.23889C17.1141 6.37687 16.9553 5.52327 16.6462 4.72692C16.3371 3.93058 15.8836 3.20723 15.3118 2.5986C14.7413 1.98813 14.0633 1.50405 13.317 1.17405C12.5707 0.844055 11.7707 0.674671 10.9629 0.675679C9.33226 0.676316 7.76865 1.3681 6.61561 2.59905C5.46258 3.82999 4.81457 5.4994 4.81398 7.24021V7.54654C3.8568 7.61916 2.95353 8.04756 2.26146 8.75719C1.56939 9.46682 1.13228 10.4129 1.02574 11.431L0 21.4064C0.00506676 22.7439 0.507103 24.0246 1.39596 24.9672C2.28481 25.9098 3.48801 26.4372 4.74087 26.4337H17.2594C18.5123 26.4372 19.7152 25.9098 20.604 24.9672C21.4929 24.0246 21.9949 22.7439 22 21.4064L20.9723 11.4286ZM6.61973 7.25092C6.61959 6.64207 6.73168 6.03916 6.94983 5.47661C7.16797 4.91407 7.48797 4.40297 7.89118 3.9724C8.2944 3.54182 8.7729 3.20017 9.29978 2.96714C9.82667 2.73411 10.3915 2.6143 10.9618 2.6143C11.5322 2.61372 12.0971 2.73335 12.6241 2.96641C13.1511 3.19946 13.63 3.54121 14.033 3.97225C14.4369 4.40235 14.7572 4.91339 14.9757 5.47603C15.1942 6.03866 15.3065 6.64185 15.3061 7.25092V7.53216H6.61973V7.25092ZM7.47644 14.0703C7.33314 14.0704 7.19109 14.0404 7.05867 13.982C6.92625 13.9235 6.80599 13.8377 6.70466 13.7295C6.60334 13.6213 6.52306 13.4929 6.46829 13.3516C6.41352 13.2102 6.38541 13.0587 6.38556 12.9057C6.38541 12.7527 6.41352 12.6012 6.46829 12.4599C6.52306 12.3185 6.60334 12.19 6.70466 12.0818C6.80599 11.9736 6.92625 11.8879 7.05867 11.8294C7.19109 11.771 7.33314 11.741 7.47644 11.7411C7.61994 11.7408 7.76208 11.7706 7.89476 11.829C8.02743 11.8874 8.14796 11.973 8.24959 12.0812C8.35121 12.1894 8.43197 12.3178 8.48706 12.4593C8.54214 12.6007 8.57046 12.7525 8.57061 12.9057C8.57046 13.0589 8.54214 13.2105 8.48706 13.352C8.43197 13.4935 8.35121 13.6219 8.24959 13.7301C8.14796 13.8382 8.02743 13.9239 7.89476 13.9823C7.76208 14.0406 7.61994 14.0706 7.47644 14.0703ZM14.526 14.0703C14.3825 14.0706 14.2404 14.0406 14.1077 13.9823C13.975 13.9239 13.8545 13.8382 13.7529 13.7301C13.6513 13.6219 13.5705 13.4935 13.5154 13.352C13.4603 13.2105 13.432 13.0589 13.4319 12.9057C13.432 12.7525 13.4603 12.6007 13.5154 12.4593C13.5705 12.3178 13.6513 12.1894 13.7529 12.0812C13.8545 11.973 13.975 11.8874 14.1077 11.829C14.2404 11.7706 14.3825 11.7408 14.526 11.7411C14.6693 11.741 14.8111 11.771 14.9435 11.8294C15.0759 11.8879 15.1965 11.9736 15.2978 12.0818C15.3991 12.19 15.4794 12.3185 15.5342 12.4599C15.589 12.6012 15.6171 12.7527 15.6169 12.9057C15.6171 13.0587 15.589 13.2102 15.5342 13.3516C15.4794 13.4929 15.3991 13.6213 15.2978 13.7295C15.1965 13.8377 15.0759 13.9235 14.9435 13.982C14.8111 14.0404 14.6693 14.0704 14.526 14.0703Z"
        fill="rgba(95, 34, 217, 1)"
      />
    </Svg>
  ),
};

export const Icons = {
  Filter: () => (
    <Svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M7.52905 16.1232L1.00005 16.1232"
        stroke="#5F22D9"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <Path
        d="M19.281 16.1232L7.52901 16.1232"
        stroke="#5F22D9"
        strokeOpacity="0.25"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <Path
        d="M10.3469 16C10.3469 14.3431 9.00378 13 7.34692 13C5.69007 13 4.34692 14.3431 4.34692 16C4.34692 17.6569 5.69007 19 7.34692 19C9.00378 19 10.3469 17.6569 10.3469 16Z"
        fill="#5F22D9"
        stroke="#5F22D9"
        strokeWidth="1.5"
      />
      <Path
        d="M12.752 3.87683L19.281 3.87683"
        stroke="#5F22D9"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <Path
        d="M1 3.87683L12.752 3.87683"
        stroke="#5F22D9"
        strokeOpacity="0.25"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <Path
        d="M9.93408 4C9.93408 5.65685 11.2772 7 12.9341 7C14.5909 7 15.9341 5.65685 15.9341 4C15.9341 2.34315 14.5909 1 12.9341 1C11.2772 1 9.93408 2.34315 9.93408 4Z"
        fill="#5F22D9"
        stroke="#5F22D9"
        strokeWidth="1.5"
      />
    </Svg>
  ),

  SearchIcon: () => (
    <Svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M11.0469 11.3999L14.4999 14.4999"
        stroke="#434B67"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Path
        d="M7 13C10.3137 13 13 10.3137 13 7C13 3.68629 10.3137 1 7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13Z"
        stroke="#767F9D"
        stroke-width="2"
      />
    </Svg>
  ),

  PinIcon: () => (
    <Svg
      width="23"
      height="31"
      viewBox="0 0 23 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.5 29.0714C18.5 21.2837 22 15.4461 22 11.5585C22 5.72722 17.299 1 11.5 1C5.70101 1 1 5.72722 1 11.5585C1 15.4461 4.5 21.2837 11.5 29.0714Z"
        stroke="#5F22D9"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M11.5 15.875C13.9162 15.875 15.875 13.9162 15.875 11.5C15.875 9.08375 13.9162 7.125 11.5 7.125C9.08375 7.125 7.125 9.08375 7.125 11.5C7.125 13.9162 9.08375 15.875 11.5 15.875Z"
        stroke="#5F22D9"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  ),
  BagIcon: () => (
    <Svg
      width="15"
      height="17"
      viewBox="0 0 15 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M12.75 3.74999H11C11 2.00041 9.43375 0.583328 7.5 0.583328C5.56625 0.583328 4 2.00041 4 3.74999H2.25C1.2875 3.74999 0.5 4.46249 0.5 5.33333V14.8333C0.5 15.7042 1.2875 16.4167 2.25 16.4167H12.75C13.7125 16.4167 14.5 15.7042 14.5 14.8333V5.33333C14.5 4.46249 13.7125 3.74999 12.75 3.74999ZM7.5 2.16666C8.4625 2.16666 9.25 2.87916 9.25 3.74999H5.75C5.75 2.87916 6.5375 2.16666 7.5 2.16666ZM12.75 14.8333H2.25V5.33333H4V6.91666C4 7.35208 4.39375 7.70833 4.875 7.70833C5.35625 7.70833 5.75 7.35208 5.75 6.91666V5.33333H9.25V6.91666C9.25 7.35208 9.64375 7.70833 10.125 7.70833C10.6062 7.70833 11 7.35208 11 6.91666V5.33333H12.75V14.8333Z"
        fill="#5F22D9"
      />
    </Svg>
  ),

  ClockIcon: () => (
    <Svg
      width="19"
      height="17"
      viewBox="0 0 19 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M9.5 15.625C12.8827 15.625 15.625 12.8827 15.625 9.5C15.625 6.11726 12.8827 3.375 9.5 3.375C6.11726 3.375 3.375 6.11726 3.375 9.5C3.375 12.8827 6.11726 15.625 9.5 15.625Z"
        stroke="#5F22D9"
        strokeWidth="2"
      />
      <Path
        d="M4.21939 1.744C3.6258 1.90298 3.08452 2.21545 2.64999 2.64998C2.21546 3.08451 1.903 3.62578 1.74402 4.21938M14.7806 1.744C15.3742 1.90298 15.9155 2.21545 16.35 2.64998C16.7846 3.08451 17.097 3.62578 17.256 4.21938M9.50002 6V9.28125C9.50002 9.402 9.59802 9.5 9.71877 9.5H12.125"
        stroke="#5F22D9"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </Svg>
  ),

  ArrowIcon: () => (
    <Svg
      width="7"
      height="13"
      viewBox="0 0 7 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M0.780029 1.34009L5.94003 6.50009L0.78003 11.6601"
        stroke="#5F22D9"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  ),

  INRIcon: () => (
    <Svg
      width="19"
      height="27"
      viewBox="0 0 19 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M19 3.375V0H6.04545H1.72727H0V3.375H1.72727H6.04545C8.29436 3.375 10.1926 4.78912 10.9077 6.75H0V10.125H10.9077C10.1926 12.0859 8.29264 13.5 6.04545 13.5H0V17.5736L9.64855 27H14.5333L4.16964 16.875H6.04545C10.2151 16.875 13.7059 13.9708 14.5056 10.125H19V6.75H14.5056C14.2448 5.4945 13.6748 4.3605 12.9096 3.375H19Z"
        fill="#5F22D9"
      />
    </Svg>
  ),
  DeleteIcon: () => (
    <Svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M5.83337 18C5.37504 18 4.98282 17.8369 4.65671 17.5108C4.3306 17.1847 4.16726 16.7922 4.16671 16.3333V5.5H3.33337V3.83333H7.50004V3H12.5V3.83333H16.6667V5.5H15.8334V16.3333C15.8334 16.7917 15.6703 17.1842 15.3442 17.5108C15.0181 17.8375 14.6256 18.0006 14.1667 18H5.83337ZM14.1667 5.5H5.83337V16.3333H14.1667V5.5ZM7.50004 14.6667H9.16671V7.16667H7.50004V14.6667ZM10.8334 14.6667H12.5V7.16667H10.8334V14.6667Z"
        fill="#C82F2F"
      />
    </Svg>
  ),
  DotIcon: () => (
    <Svg
      width="4"
      height="4"
      viewBox="0 0 4 4"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Circle cx="2" cy="2" r="2" fill="#C1C7D0" />
    </Svg>
  ),
};

export const SocialIcons = {
  Facebook: () => (
    <Svg
      width="8"
      height="15"
      viewBox="0 0 8 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.73473 14.4259H1.71348V7.98984H0.203613V5.50967H1.71348V4.02186C1.71348 1.99999 2.5659 0.796265 4.98789 0.796265H7.00407V3.27644H5.74433C4.80123 3.27644 4.73851 3.62264 4.73851 4.26958L4.73473 5.50967H7.01842L6.75091 7.98984H4.73473V14.4259Z"
        fill="white"
      />
    </Svg>
  ),
  Twitter: () => (
    <Svg
      width="14"
      height="12"
      viewBox="0 0 14 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.6853 1.38489C13.1841 1.60717 12.6445 1.75748 12.0792 1.82551C12.6565 1.47902 13.0999 0.931607 13.3084 0.278982C12.768 0.599361 12.1698 0.831934 11.5324 0.956921C11.0224 0.413462 10.2959 0.0740967 9.49168 0.0740967C7.94734 0.0740967 6.69487 1.32556 6.69487 2.86971C6.69487 3.08962 6.72053 3.30242 6.76784 3.5073C4.44412 3.39102 2.3842 2.2772 1.00504 0.585913C0.763686 0.998847 0.626572 1.47902 0.626572 1.99163C0.626572 2.96147 1.1205 3.8174 1.87022 4.31814C1.41157 4.3039 0.980182 4.17812 0.60412 3.96849C0.60412 3.98036 0.60412 3.99222 0.60412 4.00409C0.60412 5.35839 1.56713 6.48802 2.84686 6.74512C2.61192 6.80919 2.36576 6.84321 2.10997 6.84321C1.93036 6.84321 1.75476 6.82581 1.58397 6.79258C1.93998 7.90323 2.97275 8.7117 4.19635 8.73464C3.23896 9.48377 2.03299 9.93072 0.722792 9.93072C0.497476 9.93072 0.274566 9.91807 0.0556641 9.89196C1.2929 10.6854 2.76267 11.1482 4.34228 11.1482C9.48526 11.1482 12.2981 6.8883 12.2981 3.19404C12.2981 3.07222 12.2949 2.95277 12.2901 2.83253C12.8361 2.43858 13.3108 1.94575 13.6853 1.38568V1.38489Z"
        fill="white"
      />
    </Svg>
  ),
  Youtube: () => (
    <Svg
      width="16"
      height="13"
      viewBox="0 0 16 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M8.12988 0.648132C8.16721 0.648451 11.3672 0.675936 13.4961 0.837585C13.7869 0.864696 14.4477 0.865128 15.0293 1.54266C15.4787 2.05763 15.6377 3.22333 15.6377 3.22333C15.6403 3.24631 15.7959 4.61731 15.7959 5.98798V7.26141C15.7959 8.6028 15.6409 9.97093 15.6377 9.99872C15.6377 9.99872 15.4787 11.1644 15.0293 11.6794C14.4477 12.3299 13.7869 12.3573 13.4961 12.3845C11.3672 12.5731 8.16721 12.5739 8.12988 12.5739C8.12988 12.5739 4.1384 12.5472 2.92188 12.4118C2.57819 12.3305 1.81111 12.357 1.22949 11.6794C0.780213 11.1643 0.621094 9.99872 0.621094 9.99872C0.617655 9.96917 0.462891 8.62842 0.462891 7.26141V5.98798C0.462891 4.61798 0.618314 3.24766 0.621094 3.22333C0.621094 3.22333 0.75368 2.05771 1.22949 1.54266C1.811 0.892291 2.47181 0.864692 2.7627 0.837585C4.90406 0.674976 8.12988 0.648132 8.12988 0.648132ZM6.2793 9.75555L11.3018 6.88251L6.2793 3.87372V9.75555Z"
        fill="white"
      />
    </Svg>
  ),
  Instagram: () => (
    <Svg
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M10.3152 0.370361C12.694 0.370361 14.6296 2.306 14.6296 4.68481V10.5374C14.6296 12.9162 12.694 14.8518 10.3152 14.8518H4.46265C2.08384 14.8518 0.148193 12.9162 0.148193 10.5374V4.68481C0.148193 2.306 2.08384 0.370361 4.46265 0.370361H10.3152ZM4.46265 1.82739C2.88463 1.82739 1.60522 3.10677 1.60522 4.68481V10.5374C1.60522 12.1154 2.88463 13.3948 4.46265 13.3948H10.3162C11.894 13.3945 13.1726 12.1152 13.1726 10.5374V4.68481C13.1726 3.10693 11.894 1.82765 10.3162 1.82739H4.46265ZM7.3894 3.86548C9.45441 3.86574 11.1345 5.5465 11.1345 7.61157C11.1343 9.67643 9.45426 11.3564 7.3894 11.3567C5.32433 11.3567 3.64356 9.67656 3.64331 7.61157C3.64331 5.54637 5.32417 3.86548 7.3894 3.86548ZM7.3894 5.32251C6.12547 5.32251 5.10034 6.34761 5.10034 7.61157C5.10057 8.87534 6.12558 9.89966 7.3894 9.89966C8.65303 9.89946 9.67726 8.87519 9.67749 7.61157C9.67749 6.34773 8.6532 5.32271 7.3894 5.32251ZM11.1414 2.99634C11.637 2.99634 12.0388 3.39815 12.0388 3.8938C12.0388 4.38946 11.637 4.79126 11.1414 4.79126C10.6458 4.79109 10.2439 4.38936 10.2439 3.8938C10.2439 3.39826 10.6459 2.99651 11.1414 2.99634Z"
        fill="white"
      />
    </Svg>
  ),
  LinkedIn: () => (
    <Svg
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M3.22559 11.9996H0.768555V4.08746H3.22559V11.9996ZM9.47754 3.89508C11.9616 3.8951 12.4228 5.53245 12.4229 7.66364V11.9996H9.9707V8.15387C9.9707 7.23453 9.95671 6.05426 8.69629 6.05426C7.41797 6.05441 7.2266 7.05561 7.22656 8.08453V11.9996H4.77344V4.08746H7.12109V5.17145H7.15332C7.48213 4.54942 8.28106 3.89508 9.47754 3.89508ZM2.00195 0.15387C2.78734 0.15387 3.42661 0.794056 3.42676 1.58063C3.42676 2.36732 2.78743 3.00836 2.00195 3.00836C1.21192 3.00835 0.577148 2.36731 0.577148 1.58063C0.577293 0.794064 1.21201 0.153881 2.00195 0.15387Z"
        fill="white"
      />
    </Svg>
  ),
};
