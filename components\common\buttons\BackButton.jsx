import { Platform, StyleSheet, TouchableOpacity } from "react-native";
import React from "react";
import { Ionicons } from "@expo/vector-icons";

const BackButton = ({
  disable,
  pressHandler,
  buttonStyle,
  title,
  navigation,
}) => {
  const styles = StyleSheet.create({
    backButton: {
      padding: 8,
      backgroundColor: "#fff",
      borderRadius: 10,
      shadowColor: "#000",
      shadowOpacity: 0.15,
      shadowRadius: 10,
      shadowOffset: { width: 5, height: 5 },
      elevation: 5,

      ...Platform.select({
        android: {
          elevation: 8,
        },
      }),
    },
  });
  return (
    <TouchableOpacity
      onPress={() => navigation.goBack()}
      style={styles.backButton}
    >
      <Ionicons name="chevron-back" size={24} color="black" />
    </TouchableOpacity>
  );
};

export default BackButton;
