import { createStackNavigator } from "@react-navigation/stack";
import EmptyFavorites from "../components/Empty/EmptyFavorites";
import Favrites from "../screens/Favorites";

const Stack = createStackNavigator();

const FavoriteNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="Favorites">
      <Stack.Screen
        name="Favorites"
        component={EmptyFavorites}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default FavoriteNavigator;
