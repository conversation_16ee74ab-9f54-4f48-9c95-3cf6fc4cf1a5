import React, { useRef, useState, useEffect } from "react";
import {
  ScrollView,
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Text,
  StatusBar,
} from "react-native";
import Screen1 from "./Screen1";
import Screen2 from "./Screen2";
import Screen3 from "./Screen3";
import { AppColors } from "../../utils/AppColors";
import { Icons } from "../../utils/svgs";

const { width } = Dimensions.get("window");

const Onboarding = ({ navigation }) => {
  const scrollViewRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = prevIndex + 1;
        return nextIndex <= 2 ? nextIndex : 2;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (currentIndex === 3) {
      navigation.navigate("GetStarted");
    } else if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: currentIndex * width,
        animated: true,
      });
    }
  }, [currentIndex, navigation]);

  const handleManualScroll = (event) => {
    const newIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentIndex(newIndex);

    if (newIndex === 3) {
      navigation.navigate("GetStarted");
    }
  };

  const handleDotPress = (index) => {
    setCurrentIndex(index);
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: index * width,
        animated: true,
      });
    }
  };

  const handleNextPress = () => {
    navigation.navigate("GetStarted");
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleManualScroll}
      >
        <View style={{ width }}>
          <Screen1 />
        </View>
        <View style={{ width }}>
          <Screen2 />
        </View>
        <View style={{ width }}>
          <Screen3 />
        </View>
      </ScrollView>

      {currentIndex === 2 && (
        <TouchableOpacity style={styles.nextButton} onPress={handleNextPress}>
          <Icons.ArrowIcon />
        </TouchableOpacity>
      )}

      <View style={styles.indicatorContainer}>
        {[0, 1, 2].map((index) => (
          <TouchableOpacity
            key={index}
            onPress={() => handleDotPress(index)}
            style={[
              styles.dot,
              currentIndex === index ? styles.activeDot : styles.inactiveDot,
            ]}
          />
        ))}
      </View>
    </View>
  );
};

export default Onboarding;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  indicatorContainer: {
    position: "absolute",
    bottom: 100,
    flexDirection: "row",
    alignSelf: "center",
  },
  dot: {
    width: 12,
    height: 6,
    borderRadius: 3,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: "#51D6CA",
    width: 32,
  },
  inactiveDot: {
    backgroundColor: "#EBECF0",
  },
  nextButton: {
    position: "absolute",
    bottom: 50,
    alignSelf: "flex-end",
    backgroundColor: AppColors.whiteColor,
    width: 43,
    height: 43,
    borderRadius: 100,
    alignItems: "center",
    justifyContent: "center",
    right: 20,
  },
});
