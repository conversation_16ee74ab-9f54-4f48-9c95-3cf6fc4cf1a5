// Test utilities for Cart functionality
import {
  Checkout<PERSON><PERSON>,
  GetCheckoutApi,
  updateCheckoutItemQuantity,
  removeCheckoutItem,
  getSuggestions,
  updateCheckout
} from '../api/Checkout';

export const testCartFlow = async () => {
  console.log("🧪 Starting Cart Flow Test...");
  
  try {
    // Test 1: Create checkout with sample data
    console.log("📝 Test 1: Creating checkout...");
    const sampleCheckoutData = {
      "checkout_items": [
        {
          "item_id": "itm_ldOwzesXd9",
          "quantity": 1
        }
      ],
      "dislikes": [],
      "vendor_id": "vdr_11b6466a145b"
    };
    
    const createResponse = await CheckoutApi(sampleCheckoutData);
    console.log("✅ Checkout created:", createResponse);
    
    // Test 2: Get checkout data
    console.log("📋 Test 2: Getting checkout data...");
    const getResponse = await GetCheckoutApi();
    console.log("✅ Checkout retrieved:", getResponse);
    
    if (getResponse && getResponse.length > 0) {
      const checkout = getResponse[0];
      const firstItem = checkout.checkout_items[0];
      
      // Test 3: Update quantity
      console.log("🔄 Test 3: Updating quantity...");
      const updateResponse = await updateCheckoutItemQuantity(firstItem.id, 2);
      console.log("✅ Quantity updated:", updateResponse);
      
      // Test 4: Get updated data
      console.log("📋 Test 4: Getting updated checkout data...");
      const updatedResponse = await GetCheckoutApi();
      console.log("✅ Updated checkout retrieved:", updatedResponse);
      
      // Test 5: Remove item (optional - uncomment to test)
      // console.log("🗑️ Test 5: Removing item...");
      // const removeResponse = await removeCheckoutItem(firstItem.id);
      // console.log("✅ Item removed:", removeResponse);
    }
    
    console.log("🎉 All tests completed successfully!");
    return true;
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  }
};

export const validateCheckoutResponse = (response) => {
  const requiredFields = [
    'checkout_id',
    'user_id', 
    'vendor_id',
    'checkout_items',
    'payment_breakdown'
  ];
  
  const missingFields = requiredFields.filter(field => !response[field]);
  
  if (missingFields.length > 0) {
    console.warn("⚠️ Missing fields in checkout response:", missingFields);
    return false;
  }
  
  // Validate checkout items structure
  if (response.checkout_items && response.checkout_items.length > 0) {
    const itemRequiredFields = [
      'id',
      'price',
      'actual_price', 
      'quantity',
      'description',
      'image_url'
    ];
    
    const firstItem = response.checkout_items[0];
    const missingItemFields = itemRequiredFields.filter(field => firstItem[field] === undefined);
    
    if (missingItemFields.length > 0) {
      console.warn("⚠️ Missing fields in checkout item:", missingItemFields);
      return false;
    }
  }
  
  // Validate payment breakdown
  if (response.payment_breakdown) {
    const paymentRequiredFields = [
      'subtotal',
      'final_amount'
    ];
    
    const missingPaymentFields = paymentRequiredFields.filter(field => 
      response.payment_breakdown[field] === undefined
    );
    
    if (missingPaymentFields.length > 0) {
      console.warn("⚠️ Missing fields in payment breakdown:", missingPaymentFields);
      return false;
    }
  }
  
  console.log("✅ Checkout response validation passed");
  return true;
};

export const formatCartItemForDisplay = (checkoutItem, vendorInfo = null) => {
  return {
    id: checkoutItem.id,
    image_url: checkoutItem.image_url,
    description: checkoutItem.description,
    item_type: checkoutItem.item_type || "Surprise Bag",
    actual_price: checkoutItem.actual_price,
    price: checkoutItem.price,
    quantity: checkoutItem.quantity,
    diet: checkoutItem.diet,
    vendor_name: vendorInfo?.vendor_name || "Restaurant",
    vendor_id: vendorInfo?.vendor_id
  };
};

export const calculateTotalItems = (cartItems) => {
  return cartItems.reduce((total, item) => total + item.quantity, 0);
};

export const formatCurrency = (amount) => {
  return `₹${Math.round(amount)}`;
};

export const testSuggestionsFlow = async () => {
  console.log("🧪 Starting Suggestions Flow Test...");

  try {
    // Test 1: Get suggestions for a vendor and items
    console.log("📝 Test 1: Getting suggestions...");
    const vendor_id = "vdr_11b6466a145b";
    const item_ids = ["itm_ldOwzesXd9"];

    const suggestionsResponse = await getSuggestions(vendor_id, item_ids);
    console.log("✅ Suggestions retrieved:", suggestionsResponse);

    // Test 2: Test checkout update
    console.log("📝 Test 2: Testing checkout update...");
    const checkout_id = "chk_vsvqu2um7y"; // Replace with actual checkout ID
    const updateData = {
      items: [
        {
          item_id: "itm_ldOwzesXd9",
          quantity: 1
        },
        {
          item_id: "itm_suggested_item", // Replace with actual suggested item ID
          quantity: 1
        }
      ],
      dislikes: ["seafood"]
    };

    const updateResponse = await updateCheckout(checkout_id, updateData);
    console.log("✅ Checkout updated:", updateResponse);

    console.log("🎉 Suggestions flow test completed successfully!");
    return true;

  } catch (error) {
    console.error("❌ Suggestions test failed:", error);
    return false;
  }
};

export const validateSuggestionsResponse = (response) => {
  if (!Array.isArray(response)) {
    console.warn("⚠️ Suggestions response should be an array");
    return false;
  }

  if (response.length === 0) {
    console.log("ℹ️ No suggestions available");
    return true;
  }

  const requiredFields = ['id', 'item_type', 'image_url', 'price'];

  for (const item of response) {
    const missingFields = requiredFields.filter(field => !item[field]);
    if (missingFields.length > 0) {
      console.warn("⚠️ Missing fields in suggestion item:", missingFields);
      return false;
    }
  }

  console.log("✅ Suggestions response validation passed");
  return true;
};
