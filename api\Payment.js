import axiosClient from "../AxiosClient";

/**
 * Create a payment order using the checkout ID
 * @param {string} checkoutId - The checkout ID from the cart
 * @returns {Promise} - Payment order data including Razorpay order details
 */
export const createPaymentOrder = async (checkoutId) => {
  console.log("🔄 Creating payment order for checkout:", checkoutId);
  
  try {
    const response = await axiosClient.post(`v1/user/payment/order/create`, {
      checkout_id: checkoutId
    });
    
    console.log("✅ Payment order created successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Error creating payment order:", error);
    if (error.response) {
      console.error("📊 Error response status:", error.response.status);
      console.error("📋 Error response data:", error.response.data);
    }
    throw error;
  }
};

/**
 * Verify payment after successful Razorpay payment
 * @param {string} razorpayOrderId - The Razorpay order ID
 * @param {string} razorpayPaymentId - The Razorpay payment ID
 * @param {string} razorpaySignature - The Razorpay signature for verification
 * @returns {Promise} - Verification result
 */
export const verifyPayment = async (razorpayOrderId, razorpayPaymentId, razorpaySignature) => {
  console.log("🔄 Verifying payment:", { razorpayOrderId, razorpayPaymentId });
  
  try {
    const response = await axiosClient.post(`v1/user/payment/verify`, {
      razorpay_order_id: razorpayOrderId,
      razorpay_payment_id: razorpayPaymentId,
      razorpay_signature: razorpaySignature
    });
    
    console.log("✅ Payment verified successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Error verifying payment:", error);
    if (error.response) {
      console.error("📊 Error response status:", error.response.status);
      console.error("📋 Error response data:", error.response.data);
    }
    throw error;
  }
};

/**
 * Dismiss/cancel a payment order
 * @param {string} paymentOrderId - The payment order ID to dismiss
 * @returns {Promise} - Dismissal result
 */
export const dismissPayment = async (paymentOrderId) => {
  console.log("🔄 Dismissing payment order:", paymentOrderId);
  
  try {
    const response = await axiosClient.delete(`v1/user/payment/dismiss?payment_order_id=${paymentOrderId}`);
    
    console.log("✅ Payment dismissed successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Error dismissing payment:", error);
    if (error.response) {
      console.error("📊 Error response status:", error.response.status);
      console.error("📋 Error response data:", error.response.data);
    }
    throw error;
  }
};
