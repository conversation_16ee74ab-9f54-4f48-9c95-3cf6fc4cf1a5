import { View, Text, StyleSheet, Image } from "react-native";
import React from "react";
import { AppImages } from "../../../utils/AppImages";

const MainListHeading = ({ activeCategory, navigation, vendors }) => {
  return (
    <View style={styles.featureContainer}>
      <Text style={styles.heading}>
        {activeCategory === 1
          ? "Near You"
          : activeCategory === 2
          ? "Pickup Now"
          : activeCategory === 3
          ? "Trending"
          : activeCategory === 4
          ? "Best Value"
          : activeCategory === 5
          ? "Featured"
          : "New On Plenti"}
      </Text>
      <View style={styles.recommendConatiner}>
        <Text
          onPress={() =>
            navigation.navigate("Recommended", { vendors: vendors })
          }
          style={styles.viewAllText}
        >
          View all
        </Text>
        <Image style={styles.viewAllArrow} source={AppImages.VIEW_ALL} />
      </View>
    </View>
  );
};

export default MainListHeading;

const styles = StyleSheet.create({
  featureContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 20,
    paddingRight: 20,
    marginTop: 15,
  },
  heading: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    fontWeight: 700,
    lineHeight: 21.6,
    color: "#323643",
  },
  recommendConatiner: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  viewAllText: {
    fontFamily: "Poppins_400Regular",
    color: "#5F22D9",
  },
  viewAllArrow: {
    resizeMode: "contain",
    marginLeft: 5,
  },
});
