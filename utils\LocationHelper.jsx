import * as Location from "expo-location";
import axios from "axios";

const LocationHelper = {
  // Main function to handle location permission and get location data
  getLocationWithPermission: async () => {
    try {
      console.log("🔍 Starting location permission request...");

      // Request location permission
      const hasPermission = await LocationHelper.handleLocationPermission();

      if (hasPermission) {
        console.log("✅ Location permission granted");

        // Get current coordinates
        const coordinates = await LocationHelper.getCurrentCoordinates();

        if (coordinates) {
          console.log("📍 Current Coordinates:", {
            latitude: coordinates.latitude,
            longitude: coordinates.longitude,
          });

          // Get city from coordinates
          const cityName = await LocationHelper.getCityFromCoordinates(
            coordinates.latitude,
            coordinates.longitude
          );

          if (cityName) {
            console.log("🏙️ Current Location:", cityName);
            return {
              success: true,
              coordinates: coordinates,
              city: cityName,
            };
          } else {
            console.log("⚠️ Could not determine city from coordinates");
            return {
              success: true,
              coordinates: coordinates,
              city: null,
            };
          }
        } else {
          console.log("❌ Failed to get coordinates");
          return {
            success: false,
            error: "Failed to get coordinates",
          };
        }
      } else {
        console.log("❌ Location permission denied by user");
        return {
          success: false,
          error: "Location permission denied",
        };
      }
    } catch (error) {
      console.log("💥 Error in location process:", error.message);
      return {
        success: false,
        error: error.message,
      };
    }
  },

  // Handle location permission request
  handleLocationPermission: async () => {
    try {
      // Check current permission status
      const { status } = await Location.getForegroundPermissionsAsync();
      console.log("📋 Current permission status:", status);

      if (status === "granted") {
        return true;
      } else if (status === "undetermined") {
        console.log("🤔 Permission undetermined, requesting permission...");
        const { status: newStatus } =
          await Location.requestForegroundPermissionsAsync();
        console.log("📋 New permission status:", newStatus);
        return newStatus === "granted";
      } else {
        console.log("🚫 Permission previously denied");
        return false;
      }
    } catch (error) {
      console.log("💥 Error handling permission:", error.message);
      return false;
    }
  },

  // Get current coordinates
  getCurrentCoordinates: async () => {
    try {
      console.log("🎯 Getting current position...");

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Low,
        timeout: 10000, // 10 seconds timeout
      });

      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };
    } catch (error) {
      console.log("💥 Error getting coordinates:", error.message);
      return null;
    }
  },

  // Get city name from coordinates using Google Maps API
  getCityFromCoordinates: async (latitude, longitude) => {
    try {
      console.log("🗺️ Getting city from coordinates...");

      const apiKey = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY;
      const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;

      const response = await axios.get(url, {
        timeout: 5000, // 5 seconds timeout
      });

      if (response.data.status === "OK" && response.data.results.length > 0) {
        // Look for locality (city) in address components
        const cityComponent = response.data.results[0]?.address_components.find(
          (component) => component.types.includes("locality")
        );

        if (cityComponent) {
          return cityComponent.long_name;
        } else {
          // Fallback: look for administrative_area_level_1 (state/province)
          const stateComponent =
            response.data.results[0]?.address_components.find((component) =>
              component.types.includes("administrative_area_level_1")
            );
          return stateComponent ? stateComponent.long_name : null;
        }
      } else {
        console.log("⚠️ Geocoding API response:", response.data.status);
        return null;
      }
    } catch (error) {
      console.log("💥 Error getting city from coordinates:", error.message);
      return null;
    }
  },
};

// Usage example:
export const useLocationHelper = () => {
  const getLocation = async () => {
    const result = await LocationHelper.getLocationWithPermission();

    if (result.success) {
      console.log("🎉 Location data retrieved successfully!");
      return result;
    } else {
      console.log("😞 Failed to get location:", result.error);
      return result;
    }
  };

  return { getLocation };
};

export default LocationHelper;
