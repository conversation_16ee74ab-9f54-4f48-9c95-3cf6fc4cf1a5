import { View, Text, StyleSheet } from "react-native";
import React from "react";
import { AppColors } from "../../../utils/AppColors";
import { FULL_WIDTH } from "../../../utils/Constants";
import BackButton from "../../common/buttons/BackButton";

const Header = (props) => {
  return (
    <View style={styles.container}>
      <BackButton navigation={props.navigation} />

      <Text style={styles.text}>Login to Plenti</Text>
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    padding: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  text: {
    fontFamily: "Poppins_400Regular",
    position: "absolute",
    left: 100,
    right: 100,
    textAlign: "center",
    color: AppColors.blackColor,
  },
});
