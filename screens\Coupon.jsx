import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from "react-native";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { getCoupons } from "../api/Checkout";
import { useDispatch } from "react-redux";
import { setSelectedCoupon } from "../redux/slices/couponSlice";

const Coupon = ({ navigation }) => {
  const [coupons, setCoupons] = useState([]);
  const dispatch = useDispatch();

  const fetchCoupons = async () => {
    try {
      const response = await getCoupons();

      console.log("all coupons response");
      console.log(response);

      setCoupons(response);
    } catch (error) {
      console.error("Error fetching coupons:", error);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, []);

  const handleSelectCoupon = (item) => {
    console.log("selected item inside function");
    console.log(item);
    dispatch(setSelectedCoupon(item));
    navigation.navigate("Cart");
  };

  const generateOfferDescription = (item) => {
    const { discount_type, discount_value, max_discount, min_order_value } = item;

    if (discount_type === "PERCENTAGE") {
      let description = `Save ${discount_value}% on your order`;
      if (max_discount && max_discount > 0) {
        description += ` (up to ₹${max_discount})`;
      }
      if (min_order_value && min_order_value > 0) {
        description += `. Min order ₹${min_order_value}`;
      }
      return description + ".";
    } else if (discount_type === "FIXED") {
      let description = `Save ₹${discount_value} on your order`;
      if (min_order_value && min_order_value > 0) {
        description += `. Min order ₹${min_order_value}`;
      }
      return description + ".";
    }
    return "Save on your order.";
  };

  const renderItem = ({ item }) => {
    return (
      <View style={styles.card}>
        <View style={styles.discountLabel}>
          <Text style={styles.discountText}>DISCOUNT</Text>
        </View>
        <View style={styles.cardContent}>
          <View style={styles.textContent}>
            <Text style={styles.offerTitle}>{item.name || "Special Offer"}</Text>
            <Text style={styles.couponCode}>{item.code || "COUPON"}</Text>
            <Text style={styles.offerDescription}>
              {generateOfferDescription(item)}
            </Text>
            <Text style={styles.termsText}>*Terms & conditions</Text>
          </View>
          <TouchableOpacity
            style={styles.applyCodeButton}
            onPress={() => handleSelectCoupon(item)}
          >
            <Text style={styles.applyCodeText}>Apply Code</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.tagIcon}>
          {item.image_url ? (
            <Image
              source={{ uri: item.image_url }}
              style={styles.dislikeIcon}
              defaultSource={require("../assets/mastercard-logo.png")}
              onError={() => console.log('Failed to load coupon image')}
            />
          ) : (
            <View style={styles.defaultIcon}>
              <Text style={styles.defaultIconText}>%</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingText}>Coupons</Text>
        </View>
      </View>
      <View style={styles.container1}>
        <Text style={styles.sectionTitle1}>Recommended</Text>
        <FlatList
          data={coupons}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          contentContainerStyle={styles.listContainer}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 15,
  },
  headingText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#5F22D9",
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    position: "relative",
  },
  backButton: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
    position: "absolute",
    left: 16,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  secondConatiner: {
    marginTop: 10,
    backgroundColor: "#FFFFFF",
    height: "100%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    gap: 16,
  },
  container1: {
    flex: 1,
    backgroundColor: "#F8F9FA",
    paddingHorizontal: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#8E8E8E",
    marginVertical: 12,
  },

  sectionTitle1: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: "#BABABA",
    marginVertical: 12,
    textAlign: "center",
    width: "100%",
  },

  container2: {
    padding: 16,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: "relative",
    overflow: "hidden",
    minHeight: 120,
    flexDirection: "row",
  },
  discountLabel: {
    width: 60,
    backgroundColor: "#5F22D9",
    alignItems: "center",
    justifyContent: "center",
    borderTopLeftRadius: 16,
    borderBottomLeftRadius: 16,
  },
  discountText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontFamily: "Poppins_600SemiBold",
    transform: [{ rotate: "-90deg" }],
    letterSpacing: 2,
    textAlign: "center",
    width: 100,
  },
  cardContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingRight: 50,
    justifyContent: "space-between",
    minHeight: 120,
  },
  textContent: {
    flex: 1,
    justifyContent: "flex-start",
  },
  offerTitle: {
    fontSize: 12,
    fontFamily: "Poppins_500Medium",
    color: "#666666",
    marginBottom: 2,
    lineHeight: 16,
  },
  couponCode: {
    fontSize: 16,
    fontFamily: "Poppins_700Bold",
    color: "#000000",
    marginBottom: 6,
    lineHeight: 20,
    letterSpacing: 0.5,
  },
  offerDescription: {
    fontSize: 11,
    fontFamily: "Poppins_400Regular",
    color: "#666666",
    marginBottom: 6,
    lineHeight: 16,
    flexWrap: "wrap",
  },
  termsText: {
    fontSize: 10,
    fontFamily: "Poppins_400Regular",
    color: "#5F22D9",
    marginBottom: 8,
    lineHeight: 12,
  },
  tagIcon: {
    position: "absolute",
    top: 12,
    right: 12,
  },
  dislikeIcon: {
    width: 32,
    height: 32,
    resizeMode: "cover",
    borderRadius: 500,
  },
  defaultIcon: {
    width: 32,
    height: 32,
    backgroundColor: "#FF6B35",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  defaultIconText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
  },
  applyCodeButton: {
    alignSelf: "flex-start",
    backgroundColor: "#F8F9FA",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    marginTop: 8,
    width: "115%",
  },
  applyCodeText: {
    fontSize: 12,
    fontFamily: "Poppins_500Medium",
    color: "#333333",
    letterSpacing: 0.3,
    textAlign: "center",
  },
});

export default Coupon;
