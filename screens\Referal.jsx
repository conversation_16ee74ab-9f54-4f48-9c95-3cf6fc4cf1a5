import React, { useCallback, useEffect, useState } from "react";
import {
  Image,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import GlobalStyles from "../styles/GlobalStyles";
import { AppImages } from "../utils/AppImages";
import { Share } from "react-native";
import { getReferralCode } from "../api/Profile";
import { useFocusEffect } from "@react-navigation/native";
import { Icons } from "../utils/svgs";
import * as Clipboard from "expo-clipboard";
import Toast from "../components/common/Toast/Toast";

function Referal({ navigation }) {
  const [textToCopy, setTextToCopy] = useState("FIr2V");
  const [referralMessage, setReferralMessage] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const handleShare = async () => {
    try {
      await Share.share({
        message: referralMessage,
      });
    } catch (error) {
      console.log("Error sharing:", error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const fetchReferral = async () => {
        try {
          const data = await getReferralCode();

          setReferralMessage(data.referral_message);
        } catch (error) {
          console.log("Inside API error");
          console.log(error);
          console.error("Failed to fetch referral code:", error);
        }
      };

      fetchReferral();
    }, [])
  );

  const handleCopy = async () => {
    await Clipboard.setStringAsync(textToCopy);
    setToastMessage("Copied to clipboard");
    setShowToast(true);
  };

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          backgroundColor: "#FFFFFF",
          height: "100%",
        }}
      >
        <View style={styles.container}>
          <Image source={AppImages.E8} style={styles.leftCircle} />
          <Image source={AppImages.E9} style={styles.middleCircle} />
          <Image source={AppImages.PATTERN} style={styles.rightCircle} />

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color="#000" />
          </TouchableOpacity>

          <View style={styles.centerContainer}>
            <View style={styles.centerView}>
              <Icons.INRIcon />
              <Text style={styles.currencytext}>100</Text>
            </View>
            <View>
              <Text style={styles.inviteHeading}>Invite Friends</Text>
            </View>
          </View>
        </View>

        <View style={styles.secConatiner}>
          <Text style={styles.secheading}>
            Both you and your friends get Rs 100
          </Text>
          <Text style={styles.secText}>
            Share this invitation code, and you will both earn Plenti credits
            upon signup and first completed order of your friends.
          </Text>
          <View style={styles.container1}>
            <Text style={styles.text}>{textToCopy}</Text>
            <TouchableOpacity style={styles.copyButton} onPress={handleCopy}>
              <Text style={styles.copyText}>Copy</Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.button}
            activeOpacity={0.8}
            onPress={() => handleShare()}
          >
            <Text style={styles.buttonText}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>

      {showToast && (
        <Toast key={toastMessage} type="success" message={toastMessage} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 300,
    backgroundColor: "#FFFFFF",
    overflow: "hidden",
    position: "relative",
  },
  secConatiner: {
    height: "100%",
    width: "100%",
    padding: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: "#F7F5FF4D",
  },
  secheading: {
    color: "#000",
    textAlign: "center",
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
  },
  secText: {
    color: "#77777A",
    textAlign: "center",
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    marginTop: 5,
  },
  leftCircle: {
    position: "absolute",
    zIndex: 1,
    opacity: 0.8,
    resizeMode: "contain",
  },
  middleCircle: {
    position: "absolute",
    width: 240,
    height: 190,
    top: -50,
    left: "5%",
    zIndex: 0,
    opacity: 0.8,
    resizeMode: "contain",
  },
  rightCircle: {
    position: "absolute",
    zIndex: -1,
    opacity: 0.8,
    resizeMode: "contain",
  },
  backButton: {
    position: "absolute",
    top: 40,
    left: 35,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 8,
    zIndex: 2,
    shadowColor: "#AB4CFE",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.35,
    shadowRadius: 8,
    elevation: 8,

    ...Platform.select({
      android: {
        elevation: 8,
      },
    }),
  },
  container1: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    marginTop: 25,
    borderRadius: 25,
    paddingHorizontal: 10,
    paddingVertical: 7,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    width: "90%",
    alignSelf: "center",
  },
  text: {
    fontSize: 16,
    color: "#000",
  },
  inviteHeading: {
    fontFamily: "Poppins_400Regular",
    fontSize: 22,
    color: "#171725",
  },
  copyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  copyText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  centerContainer: {
    flexDirection: "column",
    padding: 5,
    justifyContent: "center",
    alignItems: "center",
    height: "50%",
    marginTop: 100,
  },
  centerView: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },
  buttonContainer: {
    position: "absolute",
    bottom: 30,
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
  },
  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#AB4CFE",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  currencytext: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 50,
    color: "#5F22D9",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default Referal;
