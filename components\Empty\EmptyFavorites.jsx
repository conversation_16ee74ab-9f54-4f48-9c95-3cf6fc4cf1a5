import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from "react-native";

import GlobalStyles from "../../styles/GlobalStyles";
import { AppImages } from "../../utils/AppImages";
import BackButton from "../common/buttons/BackButton";
import restaurants from "../../Data/Restaurants";
import { MaterialIcons } from "@expo/vector-icons";
import { getFavorite, removeFavorite } from "../../api/Favorites";
import Toast from "../common/Toast/Toast";
import { useFocusEffect } from "@react-navigation/native";
import { convertTime } from "../sections/home/<USER>";

const EmptyFavorites = ({ navigation }) => {
  const [showEmptyFav, setShowEmptyFav] = useState(false);
  const [favorites, setFavorites] = useState([]);

  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const fetchAllFav = async () => {
    try {
      const response = await getFavorite(8.597702, 76.8605644);

      if (Array.isArray(response) && response.length === 0) {
        setShowEmptyFav(true);
      } else {
        setFavorites(response);
        setShowEmptyFav(false);
      }
    } catch (error) {
      console.error("Error all fav:", error);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchAllFav();
    }, [])
  );

  const handleremoveFav = async (id) => {
    try {
      const response = await removeFavorite(id);
      if (response) {
        setToastMessage("Removed From Favorite");
        setShowToast(true);

        setTimeout(() => {
          setShowToast(false);
        }, 3000);
        fetchAllFav();
      }
    } catch (error) {
      console.error("Error while remove fav:", error);
    }
  };

  const renderRestaurantCard = ({ item }) => (
    <View style={styles.cardContainer}>
      <View style={styles.imageContainerfav}>
        <Image source={{ uri: item.backcover_url }} style={styles.mainImage} />

        <View style={styles.ratingBadge}>
          <Text style={styles.ratingText}>
            {(item.avg_rating || 0).toFixed(1)}
          </Text>
          <MaterialIcons name="star" size={16} color="#FFD700" />
        </View>

        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => handleremoveFav(item.vendor_id)}
          style={styles.heartContainer}
        >
          <Image source={AppImages.LIKE} />
        </TouchableOpacity>

        <View style={styles.priceBadge}>
          <Text style={styles.quantityText}>{item.quantity}</Text>
          <Text style={styles.priceText}>Rs {item.min_price}</Text>
        </View>
      </View>

      <View style={styles.infoContainer}>
        <Image source={{ uri: item.logo_url }} style={styles.logoImage} />

        <View style={styles.textContainer}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <Text style={styles.restaurantName}>{item.vendor_name}</Text>

            <View
              style={{
                flexDirection: "row",
                width: 15,
                height: 15,
                borderRadius: 20,
                backgroundColor: "#029094",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image
                source={AppImages.CHECKED}
                style={{
                  tintColor: "white",
                }}
              />
            </View>
          </View>
          <View style={styles.detailsContainer}>
            <MaterialIcons name="location-on" size={16} color="#5F22D9" />
            <Text style={styles.detailsText}>
              {item.distance_km.toFixed(1)} km
            </Text>
            <MaterialIcons name="access-time" size={16} color="#5F22D9" />
            <Text style={styles.detailsText}>
              {convertTime(item?.window_start_time)} -{" "}
              {convertTime(item?.window_end_time)}{" "}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <View style={[GlobalStyles.androidSafeArea, { backgroundColor: "#fff" }]}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text
            style={{
              fontFamily: "Poppins_500Medium",
              fontSize: 18,
              color: "#323643",
              marginRight: 35,
            }}
          >
            My Favorites
          </Text>
        </View>
      </View>
      <View
        style={{
          flex: 1,
          backgroundColor: "#FFF",
        }}
      >
        {showEmptyFav ? (
          <View style={styles.noResultsContainer}>
            <View style={styles.imageContainer}>
              <Image
                style={styles.backgroundImage}
                source={AppImages.BACKGROUND}
              />
              <Image style={styles.searchIconCentered} source={AppImages.FAV} />
            </View>
            <Text style={styles.noResultsText}>No Favorties</Text>
            <Text style={styles.subText}>
              You have nothing on your list yet. It's never too late to change
              it :)
            </Text>
          </View>
        ) : (
          <FlatList
            data={favorites}
            renderItem={renderRestaurantCard}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
      {showToast && (
        <Toast key={toastMessage} type="success" message={toastMessage} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 15,
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
    backgroundColor: "#FFF",
  },
  backButton: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
    position: "absolute",
    left: 16,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },

  noResultsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 30,
  },
  imageContainer: {
    position: "relative",
    width: 300,
    height: 300,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
    resizeMode: "contain",
  },
  searchIconCentered: {
    position: "absolute",
    left: 50,
    top: 50,
    width: 250,
    height: 250,
    resizeMode: "contain",
  },
  noResultsText: {
    textAlign: "center",
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    marginTop: 10,
    color: "#333333",
  },
  subText: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#838282",
    marginTop: 5,
    textAlign: "center",
  },

  cardContainer: {
    marginBottom: 15,
    borderRadius: 20,
    backgroundColor: "#FFFFFF",

    shadowColor: "#3e3e3e",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },

  imageContainerfav: {
    width: "100%",
    height: 182,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: "hidden",
    position: "relative",
  },
  mainImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
    borderRadius: 20,
  },
  ratingBadge: {
    position: "absolute",
    top: 10,
    left: 10,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 2,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#000",
  },
  heartContainer: {
    position: "absolute",
    top: 10,
    right: 10,
    backgroundColor: "#5F22D9",
    padding: 8,
    borderRadius: 20,
  },
  priceBadge: {
    position: "absolute",
    bottom: 10,
    right: 10,
    backgroundColor: "white",
    padding: 8,
    borderRadius: 15,
    flexDirection: "row",
    alignItems: "center",
  },
  quantityText: {
    marginRight: 3,
    textDecorationLine: "line-through",
    marginTop: 3,
    color: "#666",
    fontSize: 11,
  },
  priceText: {
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9",
    fontWeight: 700,
    fontSize: 14,
  },
  infoContainer: {
    flexDirection: "row",
    padding: 15,
    alignItems: "center",
  },
  logoImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    resizeMode: "contain",
  },
  textContainer: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 5,
  },
  detailsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailsText: {
    color: "#666",
    marginRight: 10,
    marginLeft: 3,
  },
});

export default EmptyFavorites;
