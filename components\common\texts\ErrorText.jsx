import { View, Text } from "react-native";
import React, { useEffect } from "react";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

const ErrorText = ({ errorText }) => {
  const opacity = useSharedValue(0);

  useEffect(() => {
    opacity.value = errorText ? 1 : 0;
  }, [errorText]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: withTiming(opacity.value, { duration: 300 }), // smooth fade
    height: withTiming(opacity.value ? 20 : 0, { duration: 300 }), // adjust height dynamically
  }));

  // Don't render anything if there's no error message
  if (!errorText) return null;

  return (
    <Animated.View style={[animatedStyle]}>
      <Animated.Text style={{ color: "red", fontSize: 12 }}>
        {errorText}
      </Animated.Text>
    </Animated.View>
  );
};

export default ErrorText;
