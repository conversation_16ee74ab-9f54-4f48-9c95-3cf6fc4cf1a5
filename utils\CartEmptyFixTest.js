// Test script to verify cart empty fix
import { 
  updateCheckoutItemQuantity,
  GetCheckoutApi
} from '../api/Checkout';

export const testCartEmptyFix = async () => {
  console.log("🧪 Testing Cart Empty Fix...");
  
  try {
    // Step 1: Get current checkout
    console.log("📋 Step 1: Getting current checkout...");
    const currentCheckout = await GetCheckoutApi();
    
    if (!currentCheckout || !currentCheckout.length || !currentCheckout[0].checkout_items?.length) {
      console.log("❌ No checkout items found - cannot test quantity update");
      return false;
    }
    
    const checkout = currentCheckout[0];
    const firstItem = checkout.checkout_items[0];
    const originalQuantity = firstItem.quantity;
    
    console.log("📦 Testing with item:", {
      id: firstItem.id,
      originalQuantity: originalQuantity,
      checkoutId: checkout.checkout_id
    });
    
    // Step 2: Test quantity increase
    console.log("📈 Step 2: Testing quantity increase...");
    const newQuantity = originalQuantity + 1;
    
    const increaseResponse = await updateCheckoutItemQuantity(
      checkout.checkout_id,
      firstItem.id,
      newQuantity
    );
    
    console.log("✅ Quantity increase response:", increaseResponse);
    
    // Verify the response has items
    if (!increaseResponse?.checkout_items || increaseResponse.checkout_items.length === 0) {
      console.error("❌ Response has no checkout_items!");
      return false;
    }
    
    // Step 3: Verify quantity was updated
    const updatedItem = increaseResponse.checkout_items.find(item => item.id === firstItem.id);
    if (!updatedItem) {
      console.error("❌ Updated item not found in response!");
      return false;
    }
    
    if (updatedItem.quantity !== newQuantity) {
      console.error("❌ Quantity not updated correctly!", {
        expected: newQuantity,
        actual: updatedItem.quantity
      });
      return false;
    }
    
    console.log("✅ Quantity updated correctly:", updatedItem.quantity);
    
    // Step 4: Test quantity decrease
    console.log("📉 Step 4: Testing quantity decrease...");
    const decreaseResponse = await updateCheckoutItemQuantity(
      checkout.checkout_id,
      firstItem.id,
      originalQuantity
    );
    
    console.log("✅ Quantity decrease response:", decreaseResponse);
    
    // Verify the response has items
    if (!decreaseResponse?.checkout_items || decreaseResponse.checkout_items.length === 0) {
      console.error("❌ Decrease response has no checkout_items!");
      return false;
    }
    
    // Step 5: Final verification
    const finalItem = decreaseResponse.checkout_items.find(item => item.id === firstItem.id);
    if (!finalItem) {
      console.error("❌ Final item not found in response!");
      return false;
    }
    
    if (finalItem.quantity !== originalQuantity) {
      console.error("❌ Final quantity not correct!", {
        expected: originalQuantity,
        actual: finalItem.quantity
      });
      return false;
    }
    
    console.log("✅ Final quantity restored correctly:", finalItem.quantity);
    console.log("🎉 Cart Empty Fix Test PASSED!");
    return true;
    
  } catch (error) {
    console.error("❌ Cart Empty Fix Test FAILED:", error);
    if (error.response) {
      console.error("📊 Error response status:", error.response.status);
      console.error("📋 Error response data:", error.response.data);
    }
    return false;
  }
};

export const validateCartState = (cartItems, checkoutData, isUpdating) => {
  console.log("🔍 Validating cart state...");
  
  const validation = {
    hasItems: cartItems && cartItems.length > 0,
    hasCheckoutData: checkoutData && checkoutData.checkout_id,
    hasCheckoutItems: checkoutData && checkoutData.checkout_items && checkoutData.checkout_items.length > 0,
    isUpdating: isUpdating,
    shouldShowCart: false,
    shouldShowEmpty: false
  };
  
  // Determine what should be shown
  if (validation.isUpdating) {
    validation.shouldShowCart = true;
    validation.shouldShowEmpty = false;
    console.log("🔄 Cart is updating - should show cart with loading");
  } else if (validation.hasItems && validation.hasCheckoutItems) {
    validation.shouldShowCart = true;
    validation.shouldShowEmpty = false;
    console.log("✅ Cart has items - should show cart");
  } else if (!validation.hasItems && !validation.hasCheckoutItems) {
    validation.shouldShowCart = false;
    validation.shouldShowEmpty = true;
    console.log("📭 Cart is empty - should show empty cart");
  } else {
    validation.shouldShowCart = true;
    validation.shouldShowEmpty = false;
    console.log("⚠️ Inconsistent state - defaulting to show cart");
  }
  
  console.log("📊 Cart state validation:", validation);
  return validation;
};

// Usage example:
// import { testCartEmptyFix, validateCartState } from './utils/CartEmptyFixTest';
// 
// // Run the test
// testCartEmptyFix().then(success => {
//   console.log('Cart empty fix test result:', success);
// });
// 
// // Validate current state
// const validation = validateCartState(cartItems, checkoutData, isUpdating);
// console.log('Should show cart:', validation.shouldShowCart);
