import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import Ionicons from "react-native-vector-icons/Ionicons";
import { AppImages } from "../../../utils/AppImages";
import { getCategoryName } from "../../../Data/Categories";

function CartPopup({ modalVisible, setModalVisible, navigation }) {
  return (
    <>
      {modalVisible.visible && <View style={styles.overlay} />}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible.visible}
        onRequestClose={() =>
          setModalVisible({ visible: false, vendor: null, items: [] })
        }
      >
        <View style={styles.popupBackground}>
          <View style={styles.cardContainer}>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  gap: 10,
                }}
              >
                <Image
                  source={{ uri: modalVisible?.vendor?.logo_url }}
                  style={styles.logo}
                />

                <View style={styles.infoContainer}>
                  <Text style={styles.restaurantName}>
                    {modalVisible?.vendor?.vendor_name}
                  </Text>
                  <Text style={styles.location}>
                    {modalVisible?.vendor?.address
                      ?.split(" ")
                      .slice(0, 4)
                      .join(" ") + "..."}
                  </Text>
                  <Text style={styles.distance}>
                    {" "}
                    {modalVisible?.vendor?.distance_km?.toFixed(1)} km
                  </Text>
                </View>
              </View>

              <TouchableOpacity
                style={styles.closeButton}
                onPress={() =>
                  setModalVisible({ visible: false, vendor: null, items: [] })
                }
              >
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            <View style={styles.line1}></View>
            <Text
              style={{
                color: "#545556",
                fontSize: 12,
                marginTop: 5,
                fontFamily: "Poppins_400Regular",
                marginBottom: 10,
              }}
            >
              Surprise Bags from this store
            </Text>
            <ScrollView
              style={{
                maxHeight: 400, // Limit the height of the scrollable area
              }}
              showsVerticalScrollIndicator={false}
            >
              {modalVisible?.items?.length > 0 ? (
                modalVisible.items.map((item, index) => (
                  <ItemCard
                    key={index}
                    item={item}
                    vendor={modalVisible.vendor}
                    navigation={navigation}
                    setModalVisible={setModalVisible}
                  />
                ))
              ) : (
                <Text
                  style={{
                    color: "#545556",
                    fontSize: 14,
                    marginTop: 5,
                    fontFamily: "Poppins_400Regular",
                  }}
                >
                  No Item available from the selected vendor
                </Text>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </>
  );
}

export const convertTime = (time) => {
  let hours = Math.floor(time / 60);
  let minutes = time % 60;
  let ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  // Add leading zero to minutes if less than 10
  let formattedMinutes = minutes < 10 ? "0" + minutes : minutes;
  let strTime = hours + ":" + formattedMinutes + " " + ampm;
  return strTime;
};

const ItemCard = ({ item, vendor, navigation, setModalVisible }) => {
  const categoryName = getCategoryName(item.item_type);

  useEffect(() => {
    console.log("inside item card.....");
    console.log(item);
    console.log(vendor);
  });

  return (
    <TouchableOpacity
      style={styles.card}
      activeOpacity={0.7}
      onPress={() => {
        setModalVisible({ visible: false, vendor: null, items: [] });
        navigation.navigate("ListingNonVeg", { item: item, vendor: vendor });
      }}
    >
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 10,
        }}
      >
        <Image source={{ uri: item.image_url }} style={styles.image} />
        <View style={styles.infoContainer}>
          <View style={styles.conatinerRoww}>
            <View>
              <Text style={styles.title}>{categoryName}</Text>
              <Text style={styles.subtitle}>
                Today {convertTime(item?.window_start_time)} -{" "}
                {convertTime(item?.window_end_time)}
              </Text>
              <Text
                style={{
                  color: "#5F22D9",
                  fontFamily: "Poppins_600SemiBold",
                  fontSize: 12,
                }}
              >
                Rs {item?.price}
              </Text>
            </View>
          </View>
        </View>
      </View>
      <TouchableOpacity style={styles.removeButton}>
        <Ionicons name="chevron-forward" size={20} color="#000" />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
  },

  popupBackground: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },

  cardContainer: {
    width: "100%", // Reduce width to avoid taking the whole screen
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20, // Rounded corners at the top
    borderTopRightRadius: 20,
    padding: 20,
    marginBottom: 10, // Add space from the bottom
  },
  card: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 10,
    backgroundColor: "#FFFFFF",
    marginVertical: 10,
  },
  conatinerRoww: {
    flexDirection: "row",
    gap: 10,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 40,
    resizeMode: "cover",
    overflow: "hidden",
  },
  infoContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 5,
    paddingRight: 20,
    marginLeft: 10,
  },
  title: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#000000",
  },
  subtitle: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#545556",
  },
  price: {
    fontSize: 16,
    color: "#5F22D9",
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 10,
  },
  quantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },
  logo: {
    height: 60,
    width: 60,
    resizeMode: "contain",
    marginBottom: 10,
  },
  infoContainer: {},
  restaurantName: {
    fontSize: 15,
    fontFamily: "Poppins_500Medium",
    color: "#000",
  },
  location: {
    fontSize: 14,
    fontFamily: "Poppins_400Medium",
    color: "#858992",
    marginTop: 1,
  },
  distance: {
    fontSize: 14,
    fontFamily: "Poppins_400Medium",
    color: "#858992",
    marginTop: 1,
  },
  line1: {
    width: "100%",
    height: 1,
    opacity: 75,
    backgroundColor: "#B0B0B0",
  },
});

export default CartPopup;
