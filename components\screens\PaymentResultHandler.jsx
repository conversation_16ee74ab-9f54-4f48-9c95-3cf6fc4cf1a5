import React, { useState, useEffect } from "react";
import { View } from "react-native";
import OrderSuccessScreen from "./OrderSuccessScreen";
import OrderFailedScreen from "./OrderFailedScreen";
import OrderStatusScreen from "./OrderStatusScreen";

const PaymentResultHandler = ({ 
  navigation, 
  route,
  paymentStatus = "success", // "success" or "failed"
  orderData = null 
}) => {
  const [currentScreen, setCurrentScreen] = useState("result");

  // Get payment status from route params if available
  const actualPaymentStatus = route?.params?.paymentStatus || paymentStatus;

  const handleTrackOrder = () => {
    setCurrentScreen("orderStatus");
  };

  const handleTryAgain = () => {
    // Navigate back to payment screen or checkout
    if (navigation) {
      navigation.goBack();
    }
  };

  const handleBackToHome = () => {
    // Navigate to home screen (TabNavigator with Discover tab)
    if (navigation) {
      navigation.navigate("TabNavigator", { screen: "Discover" });
    }
  };

  const handleNeedHelp = () => {
    // Navigate to help/support screen
    if (navigation) {
      navigation.navigate("ContactUs");
    }
  };

  const handleCancelOrder = () => {
    // Handle order cancellation
    // This would typically make an API call to cancel the order
    console.log("Cancel order requested");
    handleBackToHome();
  };

  const handleAskFriend = () => {
    // Handle asking friend to pick up
    // This could open a share dialog or navigate to a friend selection screen
    console.log("Ask friend to pick up");
  };

  // Sample order data - in real app this would come from API or route params
  const sampleOrderData = orderData || {
    orderCode: "ADF27",
    restaurant: {
      name: "Flywheel Restaurant",
      location: "Kesavadasapuram",
      distance: "3 Km",
      logo: null
    },
    pickupWindow: "6:30-8:30 pm, Today",
    orderId: "YX12358WD",
    items: "1x Meal Bag",
    total: "Rs 150",
    paymentMethod: "UPI",
    orderStatus: "Processing",
    pickupCountdown: "01h:23m:45s"
  };

  if (currentScreen === "orderStatus") {
    return (
      <OrderStatusScreen
        navigation={navigation}
        orderData={sampleOrderData}
        onNeedHelp={handleNeedHelp}
        onCancelOrder={handleCancelOrder}
        onAskFriend={handleAskFriend}
        onBackToHome={handleBackToHome}
      />
    );
  }

  if (actualPaymentStatus === "success") {
    return (
      <OrderSuccessScreen
        navigation={navigation}
        onTrackOrder={handleTrackOrder}
        onBackToHome={handleBackToHome}
      />
    );
  } else {
    return (
      <OrderFailedScreen
        navigation={navigation}
        onTryAgain={handleTryAgain}
        onBackToHome={handleBackToHome}
      />
    );
  }
};

export default PaymentResultHandler;
