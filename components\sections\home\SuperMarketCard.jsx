import React from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from "react-native";
import { AppImages } from "../../../utils/AppImages";
import { MaterialIcons } from "@expo/vector-icons";

const SCREEN_WIDTH = Dimensions.get("window").width;
const CARD_WIDTH = SCREEN_WIDTH * 0.4;

const supermarkets = [
  {
    id: "1",
    name: "<PERSON>",
    type: "Hyper Market",
    image: AppImages.SM1,
    logo: AppImages.LOGO,
    location: "Kazhakootam",
  },
  {
    id: "2",
    name: "Carrefour",
    type: "Super Market",
    image: AppImages.SM1,
    logo: AppImages.LOGO,
  },
  {
    id: "3",
    name: "Walmart",
    type: "Retail Store",
    image: AppImages.SM1,
    logo: AppImages.LOGO,
  },
];

const SuperMarketSlider = () => {
  const renderSuperMarketCard = ({ item }) => (
    <TouchableOpacity style={[styles.cardContainer, { width: CARD_WIDTH }]}>
      <View style={styles.card}>
        <Image source={item.image} style={styles.image} resizeMode="cover" />
        <View style={styles.logoContainer}>
          <Image source={item.logo} style={styles.logo} resizeMode="contain" />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.type}>{item.type}</Text>
          <View
            style={{
              flexDirection: "row",
              gap: 5,
              alignItems: "center",
              width: "100%",
            }}
          >
            {item.location && (
              <MaterialIcons name="location-on" size={16} color="#5F22D9" />
            )}
            <Text
              style={{
                color: "#666",
                fontSize: 12,
                fontFamily: "Poppins_400Regular",
              }}
            >
              {item.location ? item.location : ""}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={supermarkets}
      renderItem={renderSuperMarketCard}
      keyExtractor={(item) => item.id}
      horizontal
      showsHorizontalScrollIndicator={false}
      snapToAlignment="start"
      decelerationRate="fast"
      snapToInterval={CARD_WIDTH}
      contentContainerStyle={styles.sliderContainer}
    />
  );
};

const styles = StyleSheet.create({
  sliderContainer: {
    paddingHorizontal: 8,
    marginTop: 10,
  },
  cardContainer: {
    marginHorizontal: 8,
    shadowColor: "#3e3e3e",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
    marginBottom: 5,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 20,
    height: 200,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  image: {
    width: "100%",
    height: 100,
    borderRadius: 10,
  },
  logoContainer: {
    position: "absolute",
    top: 70,
    alignSelf: "center",
    width: 60,
    height: 60,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  logo: {
    width: 60,
    height: 60,
    borderRadius: 20,
    marginRight: 10,
    resizeMode: "contain",
  },
  textContainer: {
    padding: 16,
    paddingTop: 22,
    alignItems: "center",
  },
  name: {
    fontSize: 18,
    fontFamily: "Poppins_600SemiBold",
    color: "#000000",
  },
  type: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    color: "#000",
  },
});

export default SuperMarketSlider;
