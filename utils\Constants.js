import { Dimensions } from "react-native";
import { AppImages } from "./AppImages";

export const FULL_HEIGHT = Dimensions.get("window").height;
export const FULL_WIDTH = Dimensions.get("window").width;
export const profileWidth = FULL_WIDTH - FULL_WIDTH * 0.25;

export const SettingsOptions = [
  {
    icon: AppImages.UESERICON,
    title: "Account information",
    description: "Change your Account information",
  },
  {
    icon: AppImages.CARDICON,
    title: "Payment Methods",
    description: "Add your Credit & Debit cards",
  },
  {
    icon: AppImages.MENTION,
    title: "Invite your friends",
    description: "Get ₹100 for each invitation!",
  },
];

export const SettingsMoreOptions = [
  {
    icon: AppImages.SHOP_ICON,
    title: "Sign Up your store",
    description: "List your business in Plenti",
  },
  {
    icon: AppImages.Contact,
    title: "Contact Us",
    description: "Have questions or feedback?",
  },
  {
    icon: AppImages.STAR,
    title: "Rate Us",
    description: "You will receive daily updates",
  },
  {
    icon: AppImages.BOOK,
    title: "FAQ",
    description: "Frequently Asked Questions",
  },
];

export const CouponsData = [
  {
    id: 1,
    logo: AppImages.PAYPAL,
    title: "Flat 100 off using Paytm UPI",
  },
  {
    id: 2,
    logo: AppImages.Coupon1,
    title: "Upto 200 rs off on CRED",
    subtitle: "Express 03/27",
  },
  {
    id: 3,
    logo: AppImages.Coupon2,
    title: "Flat 75 off with IDBI card",
    subtitle: "IDBIFEAST",
  },
  {
    id: 4,
    logo: AppImages.Coupon3,
    title: "10% off using HDFC cards",
    subtitle: "HDFCCARD",
  },
  {
    id: 5,
    logo: AppImages.Coupon4,
    title: "20% off using AMEX cards",
    subtitle: "AMEX",
  },
];

export const ratingText = ["Poor", "Fair", "Good", "Very Good", "Excellent"];

export const reviewText = [
  "Value for Money",
  "Delicious Food",
  "Quick Pickup",
  "Friendly Staff",
  "Delicious Food",
];

export const data = [
  {
    id: "1",
    title: "Paragon Restaurant",
    subtitle: "Surprise Bag",
    price: "$15.30",
  },
  {
    id: "2",
    title: "Lulu HyperMarket",
    subtitle: "Surprise Bag",
    price: "$15.30",
  },
];

export const Pickup_Restaurants = [
  {
    id: "1",
    name: "Paragon Restaurant",
    image: AppImages.KFC,
    logo: AppImages.LOGO,
    rating: 4.5,
    distance: "3km",
    timing: "6:30pm - 10:30pm",
    price: "150",
    quantity: "500",
  },
  {
    id: "2",
    name: "Monal Restaurant",
    image: AppImages.RESTAURANTS,
    logo: AppImages.LOGO,
    rating: 4.5,
    distance: "5km",
    timing: "8:30pm - 1:30pm",
    price: "150",
    quantity: "500",
  },
  {
    id: "3",
    name: "Kabul Restaurant",
    image: AppImages.RESTAURANTS,
    logo: AppImages.LOGO,
    rating: 4.5,
    distance: "1km",
    timing: "7:30pm - 9:30pm",
    price: "150",
    quantity: "500",
  },
];

export const Special_Offers = [
  {
    id: "1",
    name: "Burger King",
    image: AppImages.BBURGER,
    rating: 4.5,
    price: "22.00",
  },
  {
    id: "2",
    name: "Pizza Hut",
    image: AppImages.PIZZA,
    rating: 4.8,
    price: "28.00",
  },
  {
    id: "3",
    name: "Burger King",
    image: AppImages.BBURGER,
    rating: 4.5,
    price: "22.00",
  },
];

export const carouselItem = [
  {
    id: 1,
    offer: "50% OFF ON",
    restaurant_name: "PIZZA HUT",
    image: AppImages.UPPERCUTPIZZA,
  },
  {
    id: 2,
    offer: "30% OFF ON",
    restaurant_name: "KFC",
    image: AppImages.UPPERCUTPIZZA,
  },
  {
    id: 3,
    offer: "60% OFF ON",
    restaurant_name: "MAC",
    image: AppImages.UPPERCUTPIZZA,
  },
];

export const WhatWentWrongQuestions = [
  "Accidental request",
  "I changed my mind",
  "I booked from the wrong place",
  "Pickup window is small",
  "I won’t be able to pick up the order",
  "I have a concern regarding quality",
];
