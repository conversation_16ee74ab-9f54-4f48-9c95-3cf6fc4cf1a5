import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

const CartDebugger = ({ visible = false }) => {
  const cart = useSelector((state) => state.cart);
  const cartItems = useSelector((state) => state.cart.cartItems);
  const checkoutData = useSelector((state) => state.cart.checkoutData);
  const paymentBreakdown = useSelector((state) => state.cart.paymentBreakdown);
  const selectedVendor = useSelector((state) => state.cart.selectedVendor);

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🐛 Cart Debug Info</Text>

      <Text style={styles.section}>Vendor Info:</Text>
      <Text style={styles.item}>
        ID: {selectedVendor?.id || checkoutData?.vendor_id || "None"}
      </Text>
      <Text style={styles.item}>Name: {selectedVendor?.name || "Unknown"}</Text>

      <Text style={styles.section}>Cart Items ({cartItems?.length || 0}):</Text>
      {cartItems?.map((item, index) => (
        <Text key={index} style={styles.item}>
          • {item.id}: qty {item.quantity}
        </Text>
      ))}

      <Text style={styles.section}>Checkout Data:</Text>
      <Text style={styles.item}>ID: {checkoutData?.checkout_id || "None"}</Text>
      <Text style={styles.item}>
        Items: {checkoutData?.checkout_items?.length || 0}
      </Text>
      <Text style={styles.item}>
        Vendor ID: {checkoutData?.vendor_id || "None"}
      </Text>
      <Text style={styles.item}>Status: {checkoutData?.status || "None"}</Text>

      <Text style={styles.section}>Payment:</Text>
      <Text style={styles.item}>
        Subtotal: ₹
        {paymentBreakdown?.subtotal ||
          checkoutData?.payment_breakdown?.subtotal ||
          0}
      </Text>
      <Text style={styles.item}>
        Platform Fee: ₹
        {paymentBreakdown?.platform_fee ||
          checkoutData?.payment_breakdown?.platform_fee ||
          0}
      </Text>
      <Text style={styles.item}>
        Total: ₹
        {paymentBreakdown?.final_amount ||
          checkoutData?.payment_breakdown?.final_amount ||
          0}
      </Text>

      <Text>
        Cart Variables
        {JSON.stringify(cart, null, 2)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    margin: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  section: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 5,
  },
  item: {
    fontSize: 12,
    marginLeft: 10,
    marginBottom: 2,
  },
});

export default CartDebugger;
