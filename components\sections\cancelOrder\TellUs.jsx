import { Ionicons } from "@expo/vector-icons";
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  Animated,
  Easing,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";

function TellUs({ TellUsModalVisible, setTellUsModalVisible }) {
  const [slideAnim] = useState(new Animated.Value(300));

  useEffect(() => {
    if (TellUsModalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0, // Slide in
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300, // Slide out
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    }
  }, [TellUsModalVisible]);

  return (
    <View>
      <Modal
        animationType="none"
        transparent={true}
        visible={TellUsModalVisible}
        onRequestClose={() => setTellUsModalVisible(false)}
      >
        <KeyboardAvoidingView
          style={styles.popupBackground}
          behavior={Platform.OS === "ios" ? "padding" : "height"} // Adjust behavior based on platform
        >
          <ScrollView contentContainerStyle={styles.scrollContainer}>
            <View style={styles.cardContainer}>
              <View style={styles.closeButtonContainer}>
                <TouchableOpacity
                  style={styles.cancelBtn}
                  onPress={() => setTellUsModalVisible(false)}
                >
                  <Ionicons name="close" size={20} color="#000" />
                </TouchableOpacity>
              </View>
              <Text style={styles.titleText}>What Went Wrong ?</Text>
              <TextInput
                placeholder="Tell Us"
                placeholderTextColor="#8e8e93"
                style={styles.textInput}
                multiline={true}
              />
              <TouchableOpacity>
                <Text style={styles.doneButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  popupBackground: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: "flex-end",
  },
  closeButtonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  cancelBtn: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: "#EEEEEE",
  },
  cardContainer: {
    width: "100%",
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  titleText: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 20,
    color: "#323232",
    textAlign: "center",
    paddingLeft: 20,
    paddingRight: 20,
  },
  textInput: {
    marginTop: 20,
    backgroundColor: "#F9F9F9",
    borderWidth: 1,
    borderColor: "#DFDFDF",
    padding: 15,
    paddingVertical: 15,
    borderRadius: 7,
    marginBottom: 15,
  },
  doneButtonText: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 16,
    color: "#FFFFFF",
    textAlign: "center",
    marginTop: 10,
    paddingTop: 15,
    paddingBottom: 15,
    borderRadius: 7,
    backgroundColor: "#0079F5",
  },
});

export default TellUs;
