import { Ionicons } from "@expo/vector-icons";
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Easing,
} from "react-native";
import { AppImages } from "../../../utils/AppImages";

function CancelOrderPopup({
  modalVisible,
  setModalVisible,
  setWhatWentWrongModalVisible,
}) {
  const [isOrderCancelled, setIsOrderCancelled] = useState(false);
  const [slideAnim] = useState(new Animated.Value(300));

  useEffect(() => {
    if (modalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      slideAnim.setValue(300);
    }
  }, [modalVisible]);

  const handleCancelOrder = () => {
    setIsOrderCancelled(true);
  };

  const handleCloseSuccessPopup = () => {
    setIsOrderCancelled(false);
    setModalVisible(false);
    setWhatWentWrongModalVisible(true);
  };

  return (
    <View>
      <Modal
        animationType="none"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(false);
          slideAnim.setValue(300);
        }}
        onDismiss={() => {
          slideAnim.setValue(300);
        }}
      >
        <View style={styles.popupBackground}>
          <Animated.View
            style={[
             
              { transform: [{ translateY: slideAnim }] },
            ]}
          >
            <View style={styles.cardContainer}>
              <View style={styles.headerContainer}>
                <TouchableOpacity
                  style={styles.cancelBtn}
                  onPress={() => setModalVisible(false)}
                >
                  <Ionicons name="close" size={20} color="#000" />
                </TouchableOpacity>
              </View>
              <Text style={styles.popupText}>
                Are you sure you want to cancel?
              </Text>
              <TouchableOpacity onPress={handleCancelOrder}>
                <Text style={styles.cancelButton}>Yes, Cancel the order</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.noButton}>No</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {isOrderCancelled && (
            <TouchableOpacity
              style={styles.absoluteCenter}
              activeOpacity={1}
              onPress={handleCloseSuccessPopup}
            >
              <View style={styles.successCardContainer}>
                <Image
                  source={AppImages.PATTERN}
                  style={styles.backgroundIMG}
                />
                <Image
                  source={AppImages.CANCEL_ORDER}
                  style={styles.successIMG}
                />
                <Text style={styles.successMessage}>Order Cancelled</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  popupBackground: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  cancelBtn: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: "#EEEEEE",
  },
  cardContainer: {
    width: "100%",
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    marginBottom: 10,
  },
  popupText: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 20,
    color: "#323232",
    textAlign: "center",
    paddingHorizontal: 20,
  },
  cancelButton: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 16,
    color: "#FFFFFF",
    textAlign: "center",
    marginTop: 10,
    paddingVertical: 15,
    borderRadius: 7,
    backgroundColor: "#FA3927",
  },
  noButton: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 16,
    color: "#323232",
    textAlign: "center",
    marginTop: 10,
    paddingVertical: 15,
    borderRadius: 7,
    backgroundColor: "#F9F9F9",
  },
  absoluteCenter: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  successCardContainer: {
    width: "90%",
    maxWidth: 400,
    backgroundColor: "#FFF",
    borderRadius: 20,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    elevation: 10,
    shadowColor: "#000",
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },
  backgroundIMG: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    borderRadius: 20,
    resizeMode: "cover",
  },
  successIMG: {
    width: 150,
    height: 150,
    marginVertical: 20,
    resizeMode: "contain",
  },
  successMessage: {
    fontSize: 26,
    color: "#6B50F6",
    marginVertical: 10,
  },
});

export default CancelOrderPopup;
