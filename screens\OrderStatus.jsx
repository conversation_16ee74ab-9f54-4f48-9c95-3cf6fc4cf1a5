import React, { useState } from "react";
import { View, Text, Image, StyleSheet, TouchableOpacity } from "react-native";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { AppImages } from "../utils/AppImages";
import SupportPopup from "../components/sections/Cart/SupportPopup";

const OrderStatus = ({ navigation }) => {
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingtext}>Your Order</Text>
        </View>
      </View>
      <View style={styles.secConatiner}>
        <View style={styles.codeConatiner}>
          <Text style={styles.codetext}>Your code</Text>
          <View style={styles.alginCenter}>
            <TouchableOpacity style={styles.codeButton}>
              <Text style={styles.codeButtonText}>AD21</Text>
            </TouchableOpacity>
            <Image source={AppImages.COPY} />
          </View>
        </View>
        <View style={styles.cardContainer}>
          <Image source={AppImages.LOGO} style={styles.logo} />

          <View style={styles.infoContainer}>
            <Text style={styles.restaurantName}>Paragon Restaurant</Text>
            <Text style={styles.location}>Kesavadasapuram</Text>
            <Text style={styles.distance}>3 Km</Text>
          </View>

          <Image source={AppImages.LOGO_LOCATION} style={styles.logoLocation} />
        </View>
        <View style={styles.orderContainer}>
          <View style={styles.card}>
            <View style={styles.row}>
              <Text style={styles.label}>Pickup Window</Text>
              <Text style={styles.label}>Order ID</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.value}>6:30-8:30 pm, Today</Text>
              <Text style={styles.value}>YX12358WD</Text>
            </View>
          </View>

          <View style={styles.card}>
            <View style={styles.row}>
              <Text style={styles.label}>Surprise Bag</Text>
              <Text style={styles.label}>Total</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.value}>1x Surprise Bag</Text>
              <Text style={styles.value}>Rs 150</Text>
            </View>
          </View>

          <View style={styles.card}>
            <Text style={styles.label}>Payment Method</Text>
            <Text style={styles.value}>UPI</Text>
          </View>
        </View>
        <View style={styles.countDownOuterConatiner}>
          <View style={styles.countDown}>
            <View style={styles.alignColCenter}>
              <Text style={styles.pickupFirsttext}>
                Your Pickup Window Starts in:
              </Text>
              <Text style={styles.pickupsecText}>01h:23m:45s</Text>
            </View>
          </View>
        </View>
        {modalVisible && (
          <SupportPopup
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
          />
        )}
        <View style={styles.supConatiner}>
          <TouchableOpacity onPress={() => setModalVisible(true)}>
            <View style={styles.supAlignCenter}>
              <Image source={AppImages.SUPPORT} />

              <Text style={styles.supportnText}>Need Help</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  secConatiner: {
    backgroundColor: "#FFF",
    height: "100%",
    padding: 20,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  headingtext: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    color: "#5F22D9",
  },
  codeButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 14,
    paddingHorizontal: 25,
    borderRadius: 7,
    alignItems: "center",
    shadowColor: "#6C33DC75",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
    marginLeft: 25,
  },
  codeButtonText: {
    color: "#FFFFFF",
    fontFamily: "Poppins_600SemiBold",
    fontSize: 16,
  },

  supportnText: {
    color: "#858992",
    fontFamily: "Poppins_400Regular",
    fontSize: 16,
  },

  cardContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    marginTop: 10,
    paddingHorizontal: 5,
    borderRadius: 12,
  },
  logo: {
    height: 60,
    width: 60,
    resizeMode: "contain",
  },
  logoLocation: {
    height: 30,
    width: 30,
    resizeMode: "contain",
  },
  infoContainer: {
    flex: 1,
    marginLeft: 14,
  },
  restaurantName: {
    fontSize: 15,
    fontFamily: "Poppins_500Medium",
    color: "#545556",
  },
  location: {
    fontSize: 14,
    fontFamily: "Poppins_400Medium",
    color: "#858992",
    marginTop: 1,
  },
  distance: {
    fontSize: 14,
    fontFamily: "Poppins_400Medium",
    color: "#858992",
    marginTop: 1,
  },
  orderContainer: {
    paddingHorizontal: 5,
    marginTop: 25,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    color: "#858992",
    fontFamily: "Poppins_400Regular",
  },
  value: {
    fontSize: 16,
    color: "#545556",
    fontFamily: "Poppins_500Medium",
  },
  countDown: {
    paddingVertical: 16,
    backgroundColor: "#5F22D9",
    borderRadius: 12,
    shadowColor: "#6C33DC75",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  codeConatiner: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
    gap: 5,
  },
  codetext: {
    color: "#5F22D9",
    fontWeight: 500,
    fontFamily: "Poppins_400Medium",
  },
  alginCenter: {
    flexDirection: "row",
    gap: 5,
    alignItems: "center",
  },
  countDownOuterConatiner: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  alignColCenter: {
    flexDirection: "column",
    gap: 5,
    justifyContent: "center",
  },
  pickupFirsttext: {
    textAlign: "center",
    color: "#FFFFFF",
    fontFamily: "Poppins_500Medium",
  },
  pickupsecText: {
    textAlign: "center",
    color: "#FFFFFF",
    fontFamily: "Poppins_500Medium",
  },
  supConatiner: {
    flex: 1,
    flexDirection: "column",
    justifyContent: "center",
    marginBottom: 50,
  },
  supAlignCenter: {
    flexDirection: "column",
    gap: 5,
    alignItems: "center",
  },
});

export default OrderStatus;
