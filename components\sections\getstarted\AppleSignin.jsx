import React from "react";
import { Platform, Alert, StyleSheet } from "react-native";
import * as AppleAuthentication from "expo-apple-authentication";
import { useDispatch } from "react-redux";
import { storeToken, setUser } from "../../../redux/slices/userSlice";
import { useNavigation } from "@react-navigation/native";
import { getMe, loginWithApple } from "../../../api/Auth";
import { FULL_WIDTH } from "../../../utils/Constants";

const AppleSignin = ({ buttonStyle }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const handleAppleLogin = async () => {
    let credential;

    try {
      credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      const identityToken = credential.identityToken;
      if (!identityToken) {
        throw new Error("Apple identity token is missing");
      }
      console.log("Apple credential obtained:", credential.identityToken);

      const res = await loginWithApple(identityToken);
      console.log("LoginWithApple API response:", res);

      if (res?.token) {
        storeToken(res.token);
        console.log("Token stored");

        await getMe();

        navigation.navigate("TabNavigator", { screen: "Discover" });
      }
    } catch (error) {
      console.error("Apple Sign-In Error:", error);

      if (error?.response?.data?.isEmailMissing) {
        navigation.navigate("AppleEmail", { data: credential });
      } else {
        Alert.alert(
          "Sign-In Error",
          error?.response?.data?.error || error?.message || "Try again"
        );
      }
    }
  };

  if (Platform.OS !== "ios") return null;

  return (
    <AppleAuthentication.AppleAuthenticationButton
      buttonType={AppleAuthentication.AppleAuthenticationButtonType.CONTINUE}
      buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
      cornerRadius={30}
      style={[{ padding: 27, width: FULL_WIDTH * 0.8, marginVertical: 6 }]}
      onPress={handleAppleLogin}
    />
  );
};

export default AppleSignin;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  button: {
    width: "80%",
    height: 44,
  },
});
