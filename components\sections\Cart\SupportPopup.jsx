import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Easing,
  TextInput,
} from "react-native";
import { AppImages } from "../../../utils/AppImages";

function SupportPopup({ modalVisible, setModalVisible }) {
  const [slideAnim] = useState(new Animated.Value(300)); 

  React.useEffect(() => {
    if (modalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0, 
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300, 
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    }
  }, [modalVisible]);

  return (
    <Modal
      animationType="none" 
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => setModalVisible(false)}
    >
      <View style={styles.popupBackground}>
      
        <Animated.View
          style={[
            styles.cardContainer,
            { transform: [{ translateY: slideAnim }] }, 
          ]}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <View></View>
            <Text
              style={{
                color: "#9796A1",
                fontFamily: "Poppins_500Medium",
                fontSize: 16,
              }}
            >
              Support
            </Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Ionicons name="close" size={20} color="#000" />
            </TouchableOpacity>
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginTop: 20,
              alignItems: "center",
              width: "100%",
            }}
          >
            <Text
              style={{
                fontSize: 16,
                fontFamily: "Poppins_400Regular",
              }}
            >
              Call Restaurant
            </Text>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                height: 40,
                width: 40,
                borderRadius: 20,
                backgroundColor: "#F1F3FF",
              }}
            >
              <Image source={AppImages.PHONEICON} />
            </View>
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginTop: 5,
              alignItems: "center",
              width: "100%",
            }}
          >
            <Text
              style={{
                fontSize: 16,
                fontFamily: "Poppins_400Regular",
              }}
            >
              Call Customer Support
            </Text>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                height: 40,
                width: 40,
                borderRadius: 20,
                backgroundColor: "#F1F3FF",
              }}
            >
              <Image source={AppImages.PHONEICON} />
            </View>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  popupBackground: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)", // Dimmed background stays fixed
    justifyContent: "flex-end", // Align popup at the bottom
  },
  cardContainer: {
    width: "100%",
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    position: "absolute", // Required for animation to work correctly
    bottom: 0, // Start at the bottom
  },
});

export default SupportPopup;
