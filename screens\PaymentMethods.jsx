import React from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { AppImages } from "../utils/AppImages";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";

const PaymentMethods = ({ navigation }) => {
  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingText}>Payment Methods</Text>
        </View>
      </View>
      <ScrollView style={styles.container1}>
        <Text style={styles.sectionTitle1}>Recommended</Text>
        <View style={styles.outercard}>
          <TouchableOpacity style={styles.card}>
            <View style={styles.iconContainer}>
              <Image source={AppImages.GOOGLE_PAY} style={styles.icon} />
            </View>
            <Text style={styles.label}>Google Pay UPI</Text>
            <Text style={styles.arrow}>›</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.card}>
            <View style={styles.iconContainer}>
              <Image source={AppImages.GOOGLE_PAY} style={styles.icon} />
            </View>
            <Text style={styles.label}>Google Pay UPI</Text>
            <Text style={styles.arrow}>›</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle1}>Cards</Text>
        <View style={styles.outercard}>
          <TouchableOpacity style={styles.card}>
            <View style={styles.iconContainer}>
              <Image source={AppImages.ADD_CARD} style={styles.icon} />
            </View>
            <Text style={styles.label}>Add New Card</Text>
            <Text style={styles.arrow}>›</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle1}>Wallets</Text>
        <View style={styles.outercard}>
          <TouchableOpacity style={styles.card}>
            <View style={styles.iconContainer}>
              <Image source={AppImages.MOBWIK} style={styles.icon} />
            </View>
            <Text style={styles.label}>Mobikwik</Text>
            <Text style={styles.arrow}>›</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.card}>
            <View style={styles.iconContainer}>
              <Image source={AppImages.PAYTM} style={styles.icon} />
            </View>
            <Text style={styles.label}>Paytm</Text>
            <Text style={styles.arrow}>›</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 15,
  },
  headingText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#5F22D9",
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  backButton: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
    position: "absolute",
    left: 16,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  secondConatiner: {
    marginTop: 10,
    backgroundColor: "#FFFFFF",
    height: "100%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    gap: 16,
  },
  container1: {
    flex: 1,
    backgroundColor: "#F8F9FA",
    paddingHorizontal: 16,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#8E8E8E",
    marginVertical: 12,
  },

  sectionTitle1: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: "#BABABA",
    marginVertical: 12,
    textAlign: "center",
    width: "100%",
  },

  card: {
    flexDirection: "row",
    alignItems: "center",
  },
  outercard: {
    flexDirection: "column",
    gap: 20,
    backgroundColor: "#FBFBFBBA",
    borderRadius: 12,
    padding: 10,
    paddingTop: 20,
    paddingBottom: 20,
    marginVertical: 8,
    shadowColor: "#6C33DC2B",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
    backgroundColor: "#fff",
    borderRadius: 10,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
  },
  icon: {
    width: 30,
    height: 30,
    resizeMode: "contain",
  },
  label: {
    flex: 1,
    fontFamily: "Poppins_400Regular",
    fontSize: 16,
    color: "#171725",
  },
  arrow: {
    fontSize: 25,
    color: "#111719",
  },
});

export default PaymentMethods;
