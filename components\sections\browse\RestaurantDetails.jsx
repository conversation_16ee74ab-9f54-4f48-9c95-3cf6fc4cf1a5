import { View, Text, Image, TouchableOpacity, StyleSheet } from "react-native";
import React from "react";
import { Icons } from "../../../utils/svgs";
import { convertTime } from "../home/<USER>";
import { AppColors } from "../../../utils/AppColors";

const RestaurantDetails = ({ selectedVendor, getVendorItems }) => {
  return (
    <View style={styles.seletedVendorContainer}>
      <View style={styles.infoContainer}>
        <Image
          source={{ uri: selectedVendor.logo_url }}
          style={styles.logoImage}
        />
        <View style={styles.textContainer}>
          <Text style={styles.restaurantName}>
            {selectedVendor?.vendor_name}
          </Text>
          <View style={styles.detailsContainer}>
            <Text style={styles.detailsText}>
              {selectedVendor.total_quantity} Surprise Bags left
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.line2}></View>

      <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
        <View style={{ flexDirection: "column", rowGap: 2 }}>
          <Text style={{ fontSize: 14, fontFamily: "Poppins_500Medium" }}>
            Meal Bags
          </Text>

          <View
            style={{
              flexDirection: "row",
              columnGap: 8,
              alignItems: "center",
            }}
          >
            <Text
              style={{
                fontSize: 12,
                color: AppColors.primaryColor,
                fontFamily: "Poppins_500Medium",
              }}
            >
              Rs {selectedVendor?.min_price}
            </Text>
            <Icons.DotIcon />
            <Text style={{ fontSize: 12, color: "#C1C7D0" }}>
              Pickup Window {convertTime(selectedVendor?.window_start_time)} -{" "}
              {convertTime(selectedVendor?.window_end_time)}
            </Text>
            <Icons.DotIcon />
          </View>
        </View>
        <View>
          <TouchableOpacity
            style={styles.orderButton}
            onPress={() => getVendorItems(selectedVendor)}
          >
            <Text style={styles.orderButtonText}>Details</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default RestaurantDetails;

const styles = StyleSheet.create({
  seletedVendorContainer: {
    position: "absolute",
    flexDirection: "column",
    bottom: 200,
    left: 16,
    right: 16,
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  infoContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  logoImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    resizeMode: "contain",
  },
  textContainer: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 5,
  },
  detailsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailsText: {
    color: "#666",
    marginRight: 10,
    marginLeft: 3,
  },
  line2: {
    height: 1,
    marginVertical: 16,
    backgroundColor: "rgba(176, 176, 176, 0.33)",
  },
  orderButton: {
    backgroundColor: AppColors.primaryColor,
    borderRadius: 14,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: "center",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  orderButtonText: {
    color: "#FFFFFF",
    fontFamily: "Poppins_500Medium",
    fontSize: 12,
  },
});
