import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { Icons } from "../../../utils/svgs";

const SearchFilters = ({ setModalVisible, setFilterData, filterData }) => {
  return (
    <View style={styles.headerContainer}>
      <View style={styles.inputContainer}>
        <MaterialIcons
          style={styles.searchIcon}
          name="search"
          size={24}
          color="#434B67"
        />

        <TextInput
          style={styles.input}
          placeholder="Find for food or restaurant..."
          value={filterData.searchQuery}
          onChangeText={(text) =>
            setFilterData({ ...filterData, searchQuery: text })
          }
        />
        <TouchableOpacity
          onPress={() => setFilterData({ ...filterData, searchQuery: "" })}
          style={styles.clearIconContainer}
        >
          <MaterialIcons name="clear" size={20} color="#434B67" />
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        onPress={() => setModalVisible(true)}
        style={styles.filter}
      >
        <Icons.Filter />
      </TouchableOpacity>
    </View>
  );
};

export default SearchFilters;

const styles = StyleSheet.create({
  inputContainer: {
    position: "relative",
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    flex: 1,
  },

  input: {
    minHeight: 51,
    borderColor: "#EFEFEF",
    borderWidth: 1,
    paddingLeft: 45,
    paddingRight: 15,

    borderRadius: 10,
    fontSize: 17,
    width: "95%",
  },

  searchIcon: {
    position: "absolute",
    left: 15,
    top: "50%",
    marginRight: 5,
    transform: [{ translateY: -10 }],
    width: 20,
    height: 20,
  },
  filter: {
    height: 51,
    width: 51,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    padding: 5,
    backgroundColor: "#fff",
    shadowColor: "#000",
    shadowOpacity: 0.03,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 3,
    elevation: 5,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 15,
    paddingRight: 15,
  },
  clearIconContainer: {
    position: "absolute",
    right: 25,
    top: "50%",
    marginRight: 5,
    transform: [{ translateY: -10 }],
    width: 20,
    height: 20,
  },
});
