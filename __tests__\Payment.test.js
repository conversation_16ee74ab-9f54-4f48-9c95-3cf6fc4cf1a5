import { createPaymentOrder, verifyPayment, dismissPayment } from '../api/Payment';
import axiosClient from '../AxiosClient';

// Mock axiosClient
jest.mock('../AxiosClient');

describe('Payment API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createPaymentOrder', () => {
    it('should create payment order successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            payment_order_id: 'po_123',
            razorpay_order_id: 'order_123',
            amount: 100
          }
        }
      };

      axiosClient.post.mockResolvedValue(mockResponse);

      const result = await createPaymentOrder('checkout_123');

      expect(axiosClient.post).toHaveBeenCalledWith('v1/user/payment/order/create', {
        checkout_id: 'checkout_123'
      });
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle payment order creation error', async () => {
      const mockError = new Error('Network error');
      axiosClient.post.mockRejectedValue(mockError);

      await expect(createPaymentOrder('checkout_123')).rejects.toThrow('Network error');
    });
  });

  describe('verifyPayment', () => {
    it('should verify payment successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Payment verified'
        }
      };

      axiosClient.post.mockResolvedValue(mockResponse);

      const result = await verifyPayment('order_123', 'pay_123', 'signature_123');

      expect(axiosClient.post).toHaveBeenCalledWith('v1/user/payment/verify', {
        razorpay_order_id: 'order_123',
        razorpay_payment_id: 'pay_123',
        razorpay_signature: 'signature_123'
      });
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('dismissPayment', () => {
    it('should dismiss payment successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Payment dismissed'
        }
      };

      axiosClient.delete.mockResolvedValue(mockResponse);

      const result = await dismissPayment('po_123');

      expect(axiosClient.delete).toHaveBeenCalledWith('v1/user/payment/dismiss?payment_order_id=po_123');
      expect(result).toEqual(mockResponse.data);
    });
  });
});
