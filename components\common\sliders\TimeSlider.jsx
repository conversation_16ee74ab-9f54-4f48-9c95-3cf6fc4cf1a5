import { View, Text, PanResponder, StyleSheet } from "react-native";
import React, { useRef, useState } from "react";
import { FULL_WIDTH } from "../../../utils/Constants";

const SLIDER_WIDTH_Time = FULL_WIDTH - 40;
const SLIDER_HEIGHT_Time = 6;
const THUMB_SIZE_Time = 20;

const now = new Date();

const START_DATE = new Date(
  now.getFullYear(),
  now.getMonth(),
  now.getDate() + 1,
  0,
  0,
  0,
  0
);

const END_DATE = new Date(START_DATE);
END_DATE.setDate(START_DATE.getDate() + 7);
END_DATE.setHours(23, 59, 59, 999);

const TimeSlider = ({
  minTime,
  setMinTime,
  maxTime,
  setMaxTime,
  startTime,
  endTime,
}) => {
  const [minTimePosition, setMinTimePosition] = useState(0);
  const [maxTimePosition, setMaxTimePosition] = useState(SLIDER_WIDTH_Time);

  const minThumbRef = useRef(minTimePosition);
  const maxThumbRef = useRef(maxTimePosition);

  const handleMinMove = (_, gesture) => {
    const newMinPosition = Math.min(
      Math.max(0, minThumbRef.current + gesture.dx),
      maxThumbRef.current - THUMB_SIZE_Time
    );
    minThumbRef.current = newMinPosition;
    setMinTimePosition(newMinPosition);
    setMinTime(calculateTime(newMinPosition));
  };

  const handleMaxMove = (_, gesture) => {
    const newMaxPosition = Math.max(
      Math.min(SLIDER_WIDTH_Time, maxThumbRef.current + gesture.dx),
      minThumbRef.current + THUMB_SIZE_Time
    );
    maxThumbRef.current = newMaxPosition;
    setMaxTimePosition(newMaxPosition);
    setMaxTime(calculateTime(newMaxPosition));
  };

  const minPanResponderTime = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      minThumbRef.current = minTimePosition;
    },
    onPanResponderMove: (event, gesture) => {
      requestAnimationFrame(() => handleMinMove(event, gesture));
    },
  });

  const maxPanResponderTime = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      maxThumbRef.current = maxTimePosition;
    },
    onPanResponderMove: (event, gesture) => {
      requestAnimationFrame(() => handleMaxMove(event, gesture));
    },
  });

  const calculateTime = (position) => {
    const percentage = position / SLIDER_WIDTH_Time;
    return Math.round(startTime + percentage * (endTime - startTime));
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  return (
    <View>
      <Text style={styles.headingPOPUP}>Pickup Window</Text>

      <View style={styles.sliderContainerTime}>
        <View style={styles.trackTime} />
        <View
          style={[
            styles.selectedTrackTime,
            {
              left: minTimePosition,
              width: maxTimePosition - minTimePosition,
            },
          ]}
        />
        <View
          style={[
            styles.thumbTime,
            { left: minTimePosition - THUMB_SIZE_Time / 2 },
          ]}
          {...minPanResponderTime.panHandlers}
        />
        <View
          style={[
            styles.thumbTime,
            { left: maxTimePosition - THUMB_SIZE_Time / 2 },
          ]}
          {...maxPanResponderTime.panHandlers}
        />
      </View>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text style={styles.label}> {formatTime(minTime)}</Text>
        <Text style={styles.label}>{formatTime(maxTime)}</Text>
      </View>
    </View>
  );
};

export default TimeSlider;

const styles = StyleSheet.create({
  headingPOPUP: {
    fontSize: 16,
    color: "#171725",
    fontFamily: "Poppins_500Medium",
    marginVertical: 10,
  },
  sliderContainerTime: {
    width: SLIDER_WIDTH_Time,
    height: THUMB_SIZE_Time,
    justifyContent: "center",
    position: "relative",
  },
  trackTime: {
    height: SLIDER_HEIGHT_Time,
    backgroundColor: "#E0E0E0",
    borderRadius: SLIDER_HEIGHT_Time / 2,
    position: "absolute",
    width: "100%",
  },
  selectedTrackTime: {
    height: SLIDER_HEIGHT_Time,
    backgroundColor: "#5F22D9",
    borderRadius: SLIDER_HEIGHT_Time / 2,
    position: "absolute",
  },
  thumbTime: {
    width: THUMB_SIZE_Time,
    height: THUMB_SIZE_Time,
    backgroundColor: "#5F22D9",
    borderRadius: THUMB_SIZE_Time / 2,
    position: "absolute",
  },
  label: {
    fontFamily: "Poppins_400Regular",
    fontSize: 12,
    color: "#5F22D9",
  },
});
