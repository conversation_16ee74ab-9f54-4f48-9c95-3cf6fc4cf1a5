import { createSlice } from "@reduxjs/toolkit";

const cartSlice = createSlice({
  name: "cart",
  initialState: {
    selectedItem: null,
    selectedVendor: null,
    cartItems: [],
    checkoutData: null,
    paymentBreakdown: null,
    isLoading: false,
    isUpdatingQuantity: false,
  },
  reducers: {
    setSelectedItem: (state, action) => {
      state.selectedItem = action.payload;
    },
    setSelectedVendor: (state, action) => {
      state.selectedVendor = action.payload;
    },
    setCartItems: (state, action) => {
      state.cartItems = action.payload;
    },
    setCheckoutData: (state, action) => {
      const newCheckoutData = action.payload;

      // Safety check: don't clear cart if new data is invalid
      if (!newCheckoutData) {
        console.warn("⚠️ Attempted to set null checkout data - keeping current state");
        return;
      }

      // If new data has no items but current state has items, keep current state
      // Also check if we're currently updating quantities
      if ((!newCheckoutData.checkout_items || newCheckoutData.checkout_items.length === 0) &&
          (state.cartItems.length > 0 || state.isUpdatingQuantity)) {
        console.warn("⚠️ New checkout data has no items but current cart has items or is updating - keeping current state");
        return;
      }

      state.checkoutData = newCheckoutData;
      if (newCheckoutData.checkout_items) {
        state.cartItems = newCheckoutData.checkout_items;
      }
      if (newCheckoutData.payment_breakdown) {
        state.paymentBreakdown = newCheckoutData.payment_breakdown;
      }
    },
    setPaymentBreakdown: (state, action) => {
      state.paymentBreakdown = action.payload;
    },
    updateItemQuantity: (state, action) => {
      const { itemId, quantity } = action.payload;
      const item = state.cartItems.find(item => item.id === itemId);
      if (item) {
        item.quantity = quantity;
      }
    },
    removeCartItem: (state, action) => {
      state.cartItems = state.cartItems.filter(item => item.id !== action.payload);
    },
    clearCart: (state) => {
      state.cartItems = [];
      state.checkoutData = null;
      state.paymentBreakdown = null;
    },
    setCartLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setUpdatingQuantity: (state, action) => {
      state.isUpdatingQuantity = action.payload;
    },
    addSuggestedItem: (state, action) => {
      const newItem = action.payload;
      const existingItem = state.cartItems.find(item => item.id === newItem.id);

      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        state.cartItems.push({ ...newItem, quantity: 1 });
      }
    },
  },
});

export const {
  setSelectedItem,
  setSelectedVendor,
  setCartItems,
  setCheckoutData,
  setPaymentBreakdown,
  updateItemQuantity,
  removeCartItem,
  clearCart,
  setCartLoading,
  setUpdatingQuantity,
  addSuggestedItem
} = cartSlice.actions;
export default cartSlice.reducer;
