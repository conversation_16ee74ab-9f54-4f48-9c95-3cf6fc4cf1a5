import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

const { width, height } = Dimensions.get("window");

const OrderFailedScreen = ({ navigation, onTryAgain, onBackToHome }) => {
  useEffect(() => {
    // Auto hide after 8 seconds
    const timer = setTimeout(() => {
      if (onBackToHome) {
        onBackToHome();
      } else if (navigation) {
        navigation.navigate("TabNavigator", { screen: "Discover" });
      }
    }, 8000);

    return () => clearTimeout(timer);
  }, [onBackToHome, navigation]);

  const handleBackToHome = () => {
    if (onBackToHome) {
      onBackToHome();
    } else if (navigation) {
      navigation.navigate("TabNavigator", { screen: "Discover" });
    }
  };

  const handleTryAgain = () => {
    if (onTryAgain) {
      onTryAgain();
    } else if (navigation) {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Back button */}
      <TouchableOpacity style={styles.backButton} onPress={handleBackToHome}>
        <Ionicons name="chevron-back" size={24} color="#000" />
      </TouchableOpacity>

      {/* Main failure icon */}
      <View style={styles.failureIconContainer}>
        <View style={styles.failureCircle}>
          <Text style={styles.exclamationMark}>!</Text>
        </View>
      </View>

      {/* Failure message */}
      <View style={styles.messageContainer}>
        <Text style={styles.title}>Oh Snap! Order Failed</Text>
        <Text style={styles.subtitle}>
          Looks like something went wrong{"\n"}while processing your request.
        </Text>
      </View>

      {/* Action buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.tryAgainButton} onPress={handleTryAgain}>
          <Text style={styles.tryAgainButtonText}>Please Try Again</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.homeButton} onPress={handleBackToHome}>
          <Ionicons name="arrow-back" size={16} color="#666" style={styles.homeButtonIcon} />
          <Text style={styles.homeButtonText}>Back to home</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  backButton: {
    position: "absolute",
    top: 50,
    left: 20,
    zIndex: 10,
    padding: 8,
  },
  failureIconContainer: {
    alignItems: "center",
    marginBottom: 60,
  },
  failureCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#FED7D7",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#FED7D7",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  exclamationMark: {
    fontSize: 48,
    fontWeight: "bold",
    color: "#E53E3E",
  },
  messageContainer: {
    alignItems: "center",
    marginBottom: 80,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#2D3748",
    textAlign: "center",
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    color: "#A0AEC0",
    textAlign: "center",
    lineHeight: 24,
  },
  buttonContainer: {
    width: "100%",
    alignItems: "center",
  },
  tryAgainButton: {
    backgroundColor: "#6C5CE7",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18,
    paddingHorizontal: 40,
    borderRadius: 30,
    width: "100%",
    marginBottom: 20,
    shadowColor: "#6C5CE7",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  tryAgainButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  homeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
  },
  homeButtonIcon: {
    marginRight: 6,
  },
  homeButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "500",
  },
});

export default OrderFailedScreen;
