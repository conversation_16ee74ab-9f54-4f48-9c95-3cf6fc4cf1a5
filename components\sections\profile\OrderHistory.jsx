import React from "react";
import { View, Text, Image, TouchableOpacity, StyleSheet } from "react-native";
import { AppImages } from "../../../utils/AppImages";

const PrevOrderCard = ({ navigation, order }) => (
  <View style={styles.card}>
    <View style={styles.row}>
      <TouchableOpacity style={styles.logoBtn}>
        <Image source={{ uri: order.logo_url }} style={styles.logo} />
      </TouchableOpacity>
      <View style={styles.details}>
        <Text style={styles.subtitle}>
          {order.quantity} Surprise Bag{order.quantity > 1 ? "s" : ""}
        </Text>
        <Text style={styles.title}>{order.vendor_name}</Text>
      </View>
      <View style={styles.price}>
        <Text style={styles.priceText}>Grand Total</Text>
        <Text style={styles.priceValue}>Rs {order.amount_paid}</Text>
      </View>
    </View>
    <View style={styles.action}>
      <TouchableOpacity
        style={styles.prevrateButton}
        onPress={() => navigation.navigate("ReviewRestaurant", { order })}
      >
        <Text style={styles.prevrateButtonText}>Rate</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.prevorderButton}
        onPress={() => navigation.navigate("OrderStatus", { order })}
      >
        <Text style={styles.prevorderButtonText}>Order Details</Text>
      </TouchableOpacity>
    </View>
  </View>
);

const OrderHistory = ({ navigation, pastOrders }) => (
  <View style={{ marginBottom: 30 }}>
    {pastOrders?.map((order, index) => (
      <PrevOrderCard
        key={order.order_id || index}
        navigation={navigation}
        order={order}
      />
    ))}
  </View>
);

const styles = StyleSheet.create({
  container: {
    marginTop: 10,
    backgroundColor: "#FFFFFF",
    height: "100%",
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 16,
    gap: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  code: {
    color: "#5F22D9",
    fontFamily: "Poppins_400Medium",
    fontSize: 16,
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 15,
    padding: 16,
    margin: 5,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 4,
    shadowOffset: { width: 2, height: 2 },
    elevation: 1,
  },
  row: {
    marginTop: 10,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  logoBtn: {
    padding: 7,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 4,
    shadowOffset: { width: 2, height: 2 },
    elevation: 1,
  },
  logo: {
    width: 50,
    height: 40,
  },
  details: {
    flex: 1,
    marginLeft: 10,
  },
  resttitle: {
    fontFamily: "Poppins_500Medium",
    fontSize: 16,
    color: "#000000",
  },
  title: {
    fontFamily: "Poppins_400Regular",
    fontSize: 12,
    color: "#000000",
  },
  subtitle: {
    fontFamily: "Poppins_400Regular",
    fontSize: 12,
    color: "#9796A1",
  },
  preOrders: {
    marginTop: 5,
    paddingLeft: 14,
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#111719",
  },
  price: {
    alignItems: "flex-end",
  },
  priceText: {
    fontFamily: "Poppins_400Regular",
    fontSize: 12,
    color: "#9796A1",
  },
  priceValue: {
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color: "#111719",
  },
  action: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 10,
  },
  rateButton: {
    flex: 1,
    backgroundColor: "#fff",
    paddingVertical: 12,
    borderRadius: 20,
    shadowColor: "#000", // Black shadow
    shadowOpacity: 0.05, // Lower opacity for a lighter shadow
    shadowRadius: 2, // Keep radius small for a subtle spread
    shadowOffset: { width: 0, height: 2 }, // Smaller vertical offset for a soft drop
    elevation: 1,
    alignItems: "center",
  },
  rateButtonText: {
    fontFamily: "Poppins_500Medium",
    color: "#111719",
    fontSize: 12,
    fontWeight: "bold",
  },
  orderButton: {
    flex: 1,
    backgroundColor: "#6C63FF",
    paddingVertical: 12,
    marginHorizontal: 5,
    borderRadius: 18,
    alignItems: "center",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  orderButtonText: {
    color: "#FFFFFF",
    fontFamily: "Poppins_500Medium",
    fontSize: 12,
  },
  prevrateButton: {
    flex: 1,
    backgroundColor: "#fff",
    paddingVertical: 12,
    borderRadius: 18,
    shadowColor: "#000", // Black shadow
    shadowOpacity: 0.05, // Lower opacity for a lighter shadow
    shadowRadius: 2, // Keep radius small for a subtle spread
    shadowOffset: { width: 0, height: 2 }, // Smaller vertical offset for a soft drop
    elevation: 1,
    alignItems: "center",
  },
  prevrateButtonText: {
    fontFamily: "Poppins_500Medium",
    color: "#111719",
    marginTop: 5,
    fontSize: 12,
    fontWeight: "bold",
  },
  prevorderButton: {
    flex: 1,
    backgroundColor: "#6C63FF",
    paddingVertical: 14,
    marginHorizontal: 5,
    borderRadius: 18,
    alignItems: "center",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  prevorderButtonText: {
    color: "#FFFFFF",
    fontFamily: "Poppins_500Medium",
    fontSize: 12,
  },
});

export default OrderHistory;
