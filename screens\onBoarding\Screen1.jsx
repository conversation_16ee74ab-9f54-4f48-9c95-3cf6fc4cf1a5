import React from "react";
import { View, StyleSheet, Text, Image } from "react-native";

import { AppColors } from "../../utils/AppColors";
import { AppImages } from "../../utils/AppImages";

const Screen1 = () => {
  return (
    <View style={styles.container}>
      <Image source={AppImages.OB1_UPPERCUTPIZZA} style={styles.upperImage} />
      <Image source={AppImages.OB1_BACKGROUNG} style={styles.lowerImage} />
      <View style={styles.headingContainer}>
        <Text style={styles.headingText}>
          India's first surplus Food Marketplace
        </Text>
      </View>
    </View>
  );
};

export default Screen1;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppColors.primaryColor,
  },
  upperImage: {
    width: "100%",
    height: "70%",
    resizeMode: "cover",
  },
  lowerImage: {
    width: "90%",
    height: "50%",
    position: "absolute",
    bottom: 0,
    resizeMode: "cover",
  },
  headingContainer: {
    position: "absolute",
    bottom: "20%",
    width: "100%",
    paddingLeft: 20,
    paddingRight: 110,
  },
  headingText: {
    fontSize: 36,
    fontFamily: "Poppins_700Bold",
    color: "#FFFFFF",
  },

});
