import React from "react";
import { View, StyleSheet } from "react-native";
import { FontAwesome } from "@expo/vector-icons"; // Importing FontAwesome icons

const StarRating = ({ rating, totalStars = 5 }) => {
  const renderStars = () => {
    const stars = [];
    for (let i = 0; i < totalStars; i++) {
      if (rating >= i + 1) {
        // Full star
        stars.push(
          <FontAwesome key={i} name="star" size={24} color="#5F22D9" />
        );
      } else if (rating > i && rating < i + 1) {
        // Half star
        stars.push(
          <FontAwesome
            key={i}
            name="star-half-full"
            size={24}
            color="#5F22D9"
          />
        );
      } else {
        // Empty star
        stars.push(
          <FontAwesome key={i} name="star-o" size={24} color="#5F22D9" />
        );
      }
    }
    return stars;
  };

  return <View style={styles.starContainer}>{renderStars()}</View>;
};

const styles = StyleSheet.create({
  starContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
});

export default StarRating;
