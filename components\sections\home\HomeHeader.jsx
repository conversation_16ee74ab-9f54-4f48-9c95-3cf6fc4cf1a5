import { View, Text, Pressable, StyleSheet, Image } from "react-native";

import { TouchableOpacity } from "react-native";
import { AppImages } from "../../../utils/AppImages";
import { useSelector } from "react-redux";
import { useEffect } from "react";

const HomeHeader = ({ navigation }) => {
  const user = useSelector((state) => state.user.user);
  const address = useSelector((state) => state.user.address);

  return (
    <View style={styles.headerContainer}>
      <TouchableOpacity style={styles.colmun}>
        <View style={styles.row}>
          <Text style={styles.text}>Current Location</Text>
          <Image style={styles.arrowDown} source={AppImages.ARROW_DOWN} />
        </View>
        <Text style={styles.loctionText}>{address.address}</Text>
      </TouchableOpacity>

      <Pressable onPress={() => navigation.navigate("Profile")}>
        {user?.user_profile_picture_url ? (
          <Image
            style={styles.profileIMG}
            source={{ uri: user.user_profile_picture_url }}
          />
        ) : (
          <Image style={styles.profileIMG} source={AppImages.DEFAULT_AVATAR} />
        )}
      </Pressable>
    </View>
  );
};

export default HomeHeader;

const styles = StyleSheet.create({
  text: {
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
    color: "#8C9099",
  },

  colmun: {
    flexDirection: "column",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    rowGap: 5,
  },
  loctionText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    color: "#5F22D9",
  },
  arrowDown: {
    marginLeft: 3,
    fontColor: "#8C9099",
  },
  profileIMG: {
    width: 45,
    height: 45,
    borderRadius: 10,
    overflow: "hidden",
  },
  rowCenter: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 15,
    paddingRight: 15,
    marginBottom: 16,
  },
  placeholderBox: {
    backgroundColor: "#e0e0e0",
    justifyContent: "center",
    alignItems: "center",
  },
});
