import React, { useEffect, useState, useRef } from "react";
import { View, Text, StyleSheet, Animated } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { Easing } from "react-native-reanimated";

const Toast = ({ type, message }) => {
  const [showToast, setShowToast] = useState(false);

  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(-50)).current;

  const icons = {
    success: "check-circle",
    error: "error",
  };

  const backgroundColors = {
    success: "#28a745",
    error: "#dc3545",
  };

  const toastIcon = icons[type] || "info";
  const backgroundColor = backgroundColors[type] || "#6c757d";

  useEffect(() => {
    if (message) {
      setShowToast(true);
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
      ]).start();

      const timer = setTimeout(() => {
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 0,
            duration: 300,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: -50,
            duration: 300,
            easing: Easing.ease,
            useNativeDriver: true,
          }),
        ]).start(() => setShowToast(false));
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [message]);

  if (!showToast) return null;

  return (
    <Animated.View
      style={[
        styles.toastContainer,
        { backgroundColor, opacity, transform: [{ translateY }] },
      ]}
    >
      <MaterialIcons
        name={toastIcon}
        size={24}
        color="white"
        style={styles.icon}
      />
      <Text style={styles.toastText}>{message}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    position: "absolute",
    top: 50,
    left: 20,
    right: 20,
    padding: 10,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    borderRadius: 8,
    zIndex: 1000,
  },
  toastText: {
    marginLeft: 10,
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  icon: {
    marginRight: 10,
  },
});

export default Toast;
