import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  selectedCoupon: null,
};

const couponSlice = createSlice({
  name: "coupon",
  initialState,
  reducers: {
    setSelectedCoupon: (state, action) => {
      state.selectedCoupon = action.payload;
    },
    clearSelectedCoupon: (state) => {
      state.selectedCoupon = null;
    },
  },
});

export const { setSelectedCoupon, clearSelectedCoupon } = couponSlice.actions;
export default couponSlice.reducer;
