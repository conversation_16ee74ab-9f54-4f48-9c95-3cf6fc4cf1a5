import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Modal,
  SafeAreaView,
  ScrollView,
  Animated,
  Easing,
} from "react-native";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { WhatWentWrongQuestions } from "../../../utils/Constants";

function WhatWentWrong({
  WhatWentWrongModalVisible,
  setWhatWentWrongModalVisible,
  setTellUsModalVisible,
}) {
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [slideAnim] = useState(new Animated.Value(300));

  const toggleSelection = (option) => {
    if (selectedOptions.includes(option)) {
      setSelectedOptions(selectedOptions.filter((item) => item !== option));
    } else {
      setSelectedOptions([...selectedOptions, option]);
    }
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => toggleSelection(item)}
    >
      <Text style={styles.itemText}>{item}</Text>
      <MaterialCommunityIcons
        name={
          selectedOptions.includes(item)
            ? "checkbox-marked-outline"
            : "checkbox-blank-outline"
        }
        size={24}
        color={selectedOptions.includes(item) ? "#009E60" : "#8e8e93"}
      />
    </TouchableOpacity>
  );

  useEffect(() => {
    if (WhatWentWrongModalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      slideAnim.setValue(300);
    }
  }, [WhatWentWrongModalVisible]);

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={WhatWentWrongModalVisible}
      onRequestClose={() => setWhatWentWrongModalVisible(false)}
    >
      <SafeAreaView style={styles.popupBackground}>
        <Animated.View style={[{ transform: [{ translateY: slideAnim }] }]}>
          <View style={styles.cardContainer}>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "flex-end",
              }}
            >
              <TouchableOpacity
                style={styles.cancelBtn}
                onPress={() => setWhatWentWrongModalVisible(false)}
              >
                <Ionicons name="close" size={20} color="#000" />
              </TouchableOpacity>
            </View>
            <Text
              style={{
                fontFamily: "Poppins_600SemiBold",
                fontSize: 20,
                color: "#323232",
                textAlign: "center",
                paddingLeft: 20,
                paddingRight: 20,
              }}
            >
              What Went Wrong ?
            </Text>
            <ScrollView contentContainerStyle={styles.questionContainer}>
              <FlatList
                data={WhatWentWrongQuestions}
                renderItem={renderItem}
                keyExtractor={(item, index) => index.toString()}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
              <View style={styles.separator} />
              <TouchableOpacity
                onPress={() => {
                  setTellUsModalVisible(true);
                  setWhatWentWrongModalVisible(false);
                }}
                style={styles.itemContainer}
              >
                <Text style={styles.itemText}>More</Text>
                <Ionicons
                  name="chevron-forward-outline"
                  size={20}
                  color="#111719"
                />
              </TouchableOpacity>
              <View style={styles.separator} />
              <TouchableOpacity
                onPress={() => setWhatWentWrongModalVisible(false)}
              >
                <Text
                  style={{
                    fontFamily: "Poppins_600SemiBold",
                    fontSize: 16,
                    color: "#323232",
                    textAlign: "center",
                    marginTop: 40,
                    paddingTop: 15,
                    paddingBottom: 15,
                    borderRadius: 7,
                    backgroundColor: "#F9F9F9",
                    marginBottom: 20,
                  }}
                >
                  Done
                </Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </Animated.View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  popupBackground: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  cardContainer: {
    backgroundColor: "#ffffff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "100%",
    marginBottom: 0,
    paddingBottom: 0,
  },
  cancelBtn: {
    marginTop: 20,
    marginRight: 20,
    padding: 4,
    borderRadius: 12,
    backgroundColor: "#EEEEEE",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  title: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 20,
    color: "#323232",
  },
  questionContainer: {
    paddingHorizontal: 16,
  },
  itemContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  itemText: {
    fontSize: 16,
    color: "#333333",
  },
  separator: {
    height: 1,
    backgroundColor: "#e0e0e0",
    marginVertical: 8,
  },
});

export default WhatWentWrong;
