import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import UpcomingOrders from "../components/sections/profile/UpcomingOrders";
import OrderHistory from "../components/sections/profile/OrderHistory";
import { getRecentOrders } from "../api/Orders";

const YourOrders = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState("Upcoming");
  const [activeOrders, setActiveOrders] = useState([]);
  const [pastOrders, setPastOrders] = useState([]);

  useEffect(() => {
    const getActiveOrders = async () => {
      try {
        const res = await getRecentOrders(true);
        setActiveOrders(res);
      } catch (error) {
        console.error(error);
      }
    };

    const getPastOrders = async () => {
      try {
        const res = await getRecentOrders(false);
        setPastOrders(res);
      } catch (error) {
        console.error(error);
      }
    };

    getActiveOrders();
    getPastOrders();
  }, []);

  const renderContent = () => {
    if (activeTab === "Upcoming") {
      return (
        <UpcomingOrders activeOrders={activeOrders} navigation={navigation} />
      );
    } else {
      return <OrderHistory pastOrders={pastOrders} navigation={navigation} />;
    }
  };

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingText}>Your Orders</Text>
        </View>
      </View>
      <View style={styles.upperContainer}>
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "Upcoming" && styles.activeTab]}
            onPress={() => setActiveTab("Upcoming")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "Upcoming" && styles.activeTabText,
              ]}
            >
              Upcoming
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "History" && styles.activeTab]}
            onPress={() => setActiveTab("History")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "History" && styles.activeTabText,
              ]}
            >
              History
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      <ScrollView style={styles.container1}>
        <View
          style={{
            width: "100%",
          }}
        >
          {renderContent()}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  headingText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#5F22D9",
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },

  container1: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
  },
  upperContainer: {
    backgroundColor: "#FFFFFF",
    alignItems: "center",
    paddingTop: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  tabContainer: {
    flexDirection: "row",
    borderRadius: 25,
    padding: 3,
    borderWidth: 1,
    borderColor: "#F2EAEA",
  },
  tab: {
    paddingVertical: 10,
    paddingHorizontal: 25,
    borderRadius: 25,
  },
  activeTab: {
    backgroundColor: "#5F22D9",
  },
  tabText: {
    fontSize: 16,
    color: "#5F22D9",
    textAlign: "center",
  },
  activeTabText: {
    color: "#FFFFFF",
  },
  contentContainer: {
    marginTop: 20,
    alignItems: "center",
  },
  contentText: {
    fontSize: 16,
    color: "#333",
  },
});

export default YourOrders;
