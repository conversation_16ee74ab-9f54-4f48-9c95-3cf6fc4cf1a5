import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  TextInput,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import restaurants from "../Data/Restaurants";
import { AppImages } from "../utils/AppImages";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";

const ChangePassword = ({ navigation }) => {
  const [visibility, setVisibility] = useState({
    password: false,
    newPassword: false,
    confirmPassword: false,
  });

  const toggleVisibility = (field) => {
    setVisibility((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };
  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={{ backgroundColor: "#FFFFFF", height: "100%" }}>
        <View style={styles.container}>
          <BackButton navigation={navigation} />
          <View style={styles.titleContainer}>
            <Text style={styles.headingtext}>Change Password</Text>
          </View>
        </View>
        <View style={styles.line}></View>

        <View style={styles.inputsViews}>
          <View style={styles.inputOuterContainer}>
            <Text style={styles.label}>Password</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Enter your password"
                secureTextEntry={!visibility.password}
                placeholderTextColor="#A0AEC0"
              />
              <TouchableOpacity
                onPress={() => toggleVisibility("password")}
                style={styles.iconContainer}
              >
                <Ionicons
                  name={visibility.password ? "eye-outline" : "eye-off-outline"}
                  size={20}
                  color="#A0AEC0"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.inputsViews}>
          <View style={styles.inputOuterContainer}>
            <Text style={styles.label}>New Password</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Enter new password"
                secureTextEntry={!visibility.newPassword}
                placeholderTextColor="#A0AEC0"
              />
              <TouchableOpacity
                onPress={() => toggleVisibility("newPassword")}
                style={styles.iconContainer}
              >
                <Ionicons
                  name={
                    visibility.newPassword ? "eye-outline" : "eye-off-outline"
                  }
                  size={20}
                  color="#A0AEC0"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.inputsViews}>
          <View style={styles.inputOuterContainer}>
            <Text style={styles.label}>Confirm Password</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Re-enter new password"
                secureTextEntry={!visibility.confirmPassword}
                placeholderTextColor="#A0AEC0"
              />
              <TouchableOpacity
                onPress={() => toggleVisibility("confirmPassword")}
                style={styles.iconContainer}
              >
                <Ionicons
                  name={
                    visibility.confirmPassword
                      ? "eye-outline"
                      : "eye-off-outline"
                  }
                  size={20}
                  color="#A0AEC0"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} activeOpacity={0.8}>
            <Text style={styles.buttonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  headingtext: {
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#323643",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  line: {
    width: "100%",
    height: 1,
    marginTop: 8,
    backgroundColor: "#F4F5F7",
  },
  line1: {
    width: "100%",
    height: 1,
    marginTop: 25,
    backgroundColor: "#F4F5F7",
  },
  inputsViews: {
    paddingLeft: 30,
    paddingRight: 30,
  },
  inputOuterContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 14,
    color: "#C1C7D0",
    marginBottom: 8,
    fontFamily: "Poppins_400Regular",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F7F9FC",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "#172B4D",
    fontFamily: "Poppins_400Regular",
  },
  iconContainer: {
    marginLeft: 8,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 30,
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
  },

  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#6B50F64D",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ChangePassword;
