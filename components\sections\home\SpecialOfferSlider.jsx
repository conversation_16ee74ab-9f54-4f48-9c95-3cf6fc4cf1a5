import React from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";

import {  Special_Offers } from "../../../utils/Constants";

const SCREEN_WIDTH = Dimensions.get("window").width;
const CARD_WIDTH = SCREEN_WIDTH * 0.75;

const SpecialOfferSlider = ({ navigation }) => {
  const renderRestaurantCard = ({ item }) => (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() => navigation.navigate("ListingNonVeg", { item })}
      style={styles.cardContainer}
    >
      <View style={styles.imageContainer}>
        <View
          style={{
            flexDirection: "row",
            gap: 15,
            alignItems: "center",
          }}
        >
          <View style={{ width: 110, height: 130, overflow: "hidden" }}>
            <Image source={item.image} style={styles.mainImage} />
          </View>

          <View
            style={{
              flexDirection: "column",
              gap: 4,
            }}
          >
            <View
              style={{
                flexDirection: "row",
                gap: 2,
                alignItems: "center",
              }}
            >
              <MaterialIcons name="star" size={16} color="#FFD700" />
              <Text
                style={{
                  color: "#FFB8AE",
                }}
              >
                {item.rating}
              </Text>
            </View>
            <Text
              style={{
                fontFamily: "Poppins_600SemiBold",
                fontSize: 16,
                color: "#FFFFFF",
              }}
            >
              {item.name}
            </Text>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: "#FFB8AE",
                }}
              >
                $
              </Text>
              <Text
                style={{
                  color: "#FFF",
                }}
              >
                {item.price}
              </Text>
            </View>
            <TouchableOpacity style={styles.cardBtn}>
              <Text
                style={{
                  color: "#FFF",
                  fontFamily: "Poppins_500Medium",
                }}
              >
                Buy Now
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={Special_Offers}
      renderItem={renderRestaurantCard}
      keyExtractor={(item) => item.id}
      horizontal
      showsHorizontalScrollIndicator={false}
      snapToAlignment="start"
      decelerationRate="fast"
      snapToInterval={CARD_WIDTH}
      contentContainerStyle={styles.sliderContainer}
    />
  );
};

const styles = StyleSheet.create({
  sliderContainer: {
    paddingLeft: 15,
    paddingRight: 15,
    marginTop: 8,
    height: 130,
    marginBottom: 10,
  },
  cardContainer: {
    width: CARD_WIDTH,
    marginRight: 15,
    borderRadius: 20,
    marginBottom: 10,
    height: 190,
    shadowColor: "#3e3e3e",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  imageContainer: {
    width: "100%",
    backgroundColor: "#5F22D9",
    height: 130,
    borderRadius: 20,
    borderRadius: 20,
    overflow: "hidden",
    position: "relative",
    paddingLeft: 10,
    paddingRight: 20,
    paddingBottom: 20,
  },
  mainImage: {
    height: "100%",
    width: "100%",
    resizeMode: "contain",
  },
  cardBtn: {
    backgroundColor: "#51D6CA",
    width: 80,
    height: 30,
    borderRadius: 15,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default SpecialOfferSlider;
