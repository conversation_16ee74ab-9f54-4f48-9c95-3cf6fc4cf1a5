{"expo": {"name": "plenti-app", "slug": "plenti-app", "version": "1.0.0", "orientation": "portrait", "scheme": "plenti-app", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": false, "usesAppleSignIn": true, "config": {"googleMapsApiKey": "AIzaSyDFDTcim6p0fmoAfdHV7DERJci1C6SEh8s"}, "googleServicesFile": "./GoogleService-Info.plist", "bundleIdentifier": "com.tixertech.plenti", "infoPlist": {"NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location to show nearby restaurants.", "NSLocationUsageDescription": "This app needs access to your location to show nearby restaurants.", "NSLocationWhenInUseUsageDescription": "This app needs access to your location to show nearby restaurants.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to let you select and upload a profile image.", "ITSAppUsesNonExemptEncryption": false, "CFBundleURLTypes": [{"CFBundleURLSchemes": ["com.googleusercontent.apps.298219936678-usdjajd0n7e71ese1djqvqijkk3rocb9"]}]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "./google-services.json", "package": "com.tixertech.plenti", "config": {"googleMaps": {"apiKey": "AIzaSyACX14RZvTAVHtlgKsQfhYtWtwZjTSlyiE"}}}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "e7d126bb-56e8-4e0e-9de4-63798e1cfdc7"}}, "plugins": ["expo-web-browser", ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.298219936678-hh46l5ha8icih4cie5m2nvm6hm59h63r"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow Plenti-app to use your location. This is needed to show you resaurants near you.", "isAndroidForegroundServiceEnabled": false}], "expo-notifications", "@react-native-firebase/app", "@react-native-firebase/messaging", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}], "expo-apple-authentication"]}}