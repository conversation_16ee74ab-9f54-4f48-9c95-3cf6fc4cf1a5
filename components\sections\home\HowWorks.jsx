import React from "react";
import { Image, Text, View, StyleSheet, Pressable } from "react-native";
import { AppImages } from "../../../utils/AppImages";
import { OpenWebsite } from "../../../utils/WebLinks";

function HowWorks() {
  return (
    <View>
      <View style={styles.heroContainer}>
        <View style={styles.heroContainer1}>
          <View style={{ flex: 3 }}>
            <Text style={styles.heroText}>How Plenti Works ?</Text>
            <Text style={styles.heroText1}>
              Get an overview of what we are what we do
            </Text>
            <View style={styles.rowCenter}>
              <Image
                style={{
                  resizeMode: "contain",
                }}
                source={AppImages.Arrow_Right}
              />
              <Pressable onPress={() => OpenWebsite()}>
                <Text style={styles.heroButton}>Go To Website</Text>
              </Pressable>
            </View>
          </View>
          <View style={styles.heroIMGContainer}>
            <Image style={styles.heroIMG} source={AppImages.LOGOSM} />
          </View>
        </View>
      </View>
    </View>
  );
}

export default HowWorks;

const styles = StyleSheet.create({
  heroContainer: {
    paddingLeft: 15,
    paddingRight: 15,
    marginTop: 10,
  },
  rowCenter: {
    flexDirection: "row",
    gap: 3,
    alignItems: "center",
  },
  heroContainer1: {
    flexDirection: "row",
    alignItems: "center",
    paddingLeft: 15,
    height: 148,
    borderRadius: 20,
    backgroundColor: "#5F22D9",
    overflow: "hidden",
  },
  heroText: {
    color: "white",
    fontFamily: "Poppins_400Medium",
    fontSize: 18,
    fontWeight: "800",
    letterSpacing: -0.67,
    width: "100%",
  },
  heroText1: {
    fontFamily: "Poppins_400Medium",
    color: "white",
    fontWeight: 400,
    fontSize: 14,
    lineHeight: 16,
    marginVertical: 8,
  },
  heroButton: {
    color: "white",
    fontSize: 16,
    fontFamily: "Poppins_400Medium",

    fontWeight: "900",
    lineHeight: 21.29,
    marginLeft: 5,
  },
  heroIMGContainer: {
    flex: 2,
    marginRight: -1,
  },
  heroIMG: {
    resizeMode: "contain",
    width: "100%",
  },
});
