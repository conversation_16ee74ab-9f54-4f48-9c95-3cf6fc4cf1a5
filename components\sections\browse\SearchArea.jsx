import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableOpacity,
} from "react-native";
import { Icons } from "../../../utils/svgs";
import { AppColors } from "../../../utils/AppColors";
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";
import { useDispatch } from "react-redux";
import { setAddress } from "../../../redux/slices/userSlice";
import * as Location from "expo-location";

const SearchArea = () => {
  const [isTyping, setIsTyping] = useState(false); // Track if the user is typing
  const dispatch = useDispatch();

  const handleCitySelection = (data, details) => {
    dispatch(
      setAddress({
        latitude: details.geometry.location.lat,
        longitude: details.geometry.location.lng,
        address: details.address_components[0].long_name,
      })
    );

    // You can also dismiss the keyboard after selection
    dismissKeyboard();
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
    setIsTyping(false);
  };

  const handlePermission = async () => {
    const { status } = await Location.getForegroundPermissionsAsync();
    if (status === "granted") {
      return true;
    } else if (status === "undetermined") {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === "granted";
    }
    return false;
  };

  const getCoordinates = async () => {
    const hasPermission = await handlePermission();
    if (hasPermission) {
      const { coords } = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = coords;
      const address = await getAddress(latitude, longitude);
      return {
        latitude,
        longitude,
        address,
      };
    } else {
      const { latitude, longitude } = {
        latitude: 8.4996555,
        longitude: 76.9242809,
      };
      const address = await getAddress(latitude, longitude);
      return {
        latitude,
        longitude,
        address,
      };
    }
  };

  const getAddress = async (latitude, longitude) => {
    try {
      const response = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      if (response.length > 0) {
        const { city, street } = response[0];
        return `${street}, ${city}`;
      }
      return null;
    } catch (error) {
      console.error("Error getting address:", error);
      return null;
    }
  };

  const handleCurrentLocation = async () => {
    const address = await getCoordinates();
    if (address) {
      dispatch(setAddress(address));
    }
    // You can also dismiss the keyboard after using current location
    dismissKeyboard();
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <View style={styles.bottomContainer}>
        <View style={styles.inputContainer}>
          <Icons.SearchIcon />
          <GooglePlacesAutocomplete
            placeholder="Search for city or Location"
            enablePoweredByContainer={false}
            query={{
              key: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY,
              language: "en",
              components: "country:in", // Restrict to India
            }}
            styles={{
              container: {
                // flex: 1,
                backgroundColor: "transparent",
              },
              textInputContainer: {
                // flex: 1,
                backgroundColor: "transparent",
                borderWidth: 0,
                borderTopColor: "transparent",
                borderBottomColor: "transparent",
              },
              textInput: {
                fontSize: 16,
                color: "#000",
                backgroundColor: "#F0F0F1",
                borderRadius: 10,
              },
            }}
            fetchDetails={true} // This allows us to get the detailed response
            onPress={(data, details = null) =>
              handleCitySelection(data, details)
            }
          />
        </View>

        <TouchableOpacity
          style={styles.currentLocationContainer}
          onPress={handleCurrentLocation}
        >
          <Icons.PinIcon />
          <Text style={styles.text}>Use my current Location</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

export default SearchArea;

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    backgroundColor: "transparent",
  },
  bottomContainer: {
    width: "100%",
    paddingHorizontal: 24,
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  currentLocationContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 12,
    marginBottom: 32,
    columnGap: 10,
  },
  text: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: AppColors.primaryColor,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F0F0F1",
    borderRadius: 10,
    paddingHorizontal: 18,
    paddingVertical: 6,
    width: "100%",
  },
});
