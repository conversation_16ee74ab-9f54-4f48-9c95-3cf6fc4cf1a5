import { View, TextInput, StyleSheet } from "react-native";
import React from "react";
import { Icons } from "../../../utils/svgs";

const MapSearchbar = ({ onFocus, onBlur }) => {
  return (
    <View style={styles.inputContainer}>
      <Icons.SearchIcon />
      <TextInput
        style={styles.input}
        placeholder="Search for city or Location"
        placeholderTextColor="#A0A0A0"
        onFocus={onFocus}
        onBlur={onBlur}
      />
    </View>
  );
};

export default MapSearchbar;

const styles = StyleSheet.create({
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F0F0F1",
    borderRadius: 10,
    paddingHorizontal: 18,
    paddingVertical: 12,
    width: "100%",
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "#000",
  },
});
