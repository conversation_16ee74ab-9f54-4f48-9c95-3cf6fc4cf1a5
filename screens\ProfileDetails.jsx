import React, { useEffect, useState } from "react";
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import GlobalStyles from "../styles/GlobalStyles";
import { AppImages } from "../utils/AppImages";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useSelector } from "react-redux";

function ProfileDetails({ navigation }) {
  const [activeInput, setActiveInput] = useState(null);
  const user = useSelector((state) => state.user.user);

  const handleFocus = (inputName) => {
    setActiveInput(inputName);
  };

  const handleBlur = () => {
    setActiveInput(null);
  };

  return (
    <KeyboardAwareScrollView contentContainerStyle={{ flexGrow: 1 }}>
      <View
        style={{
          backgroundColor: "#FFFFFF",
          height: "100%",
        }}
      >
        <View style={styles.container}>
          <Image source={AppImages.E5} style={styles.leftCircle} />
          <Image source={AppImages.E4} style={styles.middleCircle} />
          <Image source={AppImages.E3} style={styles.rightCircle} />

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color="#000" />
          </TouchableOpacity>

          <View style={styles.profileContainer}>
            <Image
              source={{ uri: user.user_profile_picture_url }}
              style={styles.profileImage}
            />
            <TouchableOpacity style={styles.cameraButton}>
              <Ionicons name="camera" size={16} color="#B3B3B3" />
            </TouchableOpacity>
          </View>
        </View>
        <View
          style={{
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text
            style={{
              fontSize: 20,
              fontFamily: "Poppins_500Medium",
            }}
          >
            User Name
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "Poppins_400Regular",
              color: "#9796A1",
              marginTop: 5,
            }}
          >
            Edit Profile
          </Text>
        </View>
        <View style={styles.container1}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Full name</Text>
            <TextInput
              style={
                activeInput === "fullName" ? styles.inputavtive : styles.input
              }
              placeholder="Full name"
              defaultValue="User Name"
              placeholderTextColor="#999"
              onFocus={() => handleFocus("fullName")}
              onBlur={handleBlur}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>E-mail</Text>
            <TextInput
              style={
                activeInput === "email" ? styles.inputavtive : styles.input
              }
              placeholder="E-mail"
              defaultValue="<EMAIL>"
              keyboardType="email-address"
              placeholderTextColor="#999"
              onFocus={() => handleFocus("email")}
              onBlur={handleBlur}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Phone Number</Text>
            <TextInput
              style={
                activeInput === "phoneNumber"
                  ? styles.inputavtive
                  : styles.input
              }
              placeholder="Phone Number"
              value={user.phone_number}
              keyboardType="phone-pad"
              placeholderTextColor="#999"
              onFocus={() => handleFocus("phoneNumber")}
              onBlur={handleBlur}
            />
          </View>
        </View>
        <View
          style={{
            paddingLeft: 28,
            paddingRight: 28,
            marginTop: 40,
            alignItems: "center",
          }}
        >
          <TouchableOpacity style={styles.button} activeOpacity={0.8}>
            <Text style={styles.buttonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 200,
    backgroundColor: "#FFFFFF",
    overflow: "hidden",
    position: "relative",
  },
  leftCircle: {
    position: "absolute",
    zIndex: 1,
    opacity: 0.8,
    resizeMode: "contain",
  },
  middleCircle: {
    position: "absolute",
    width: 140,
    height: 140,
    top: -50,
    left: "25%",
    zIndex: 0,
    opacity: 0.8,
    resizeMode: "contain",
  },
  rightCircle: {
    position: "absolute",
    right: -20,
    top: -30,
    zIndex: 1,
    opacity: 0.8,
    resizeMode: "contain",
    transform: [{ rotate: "10deg" }],
  },
  backButton: {
    position: "absolute",
    top: 40,
    left: 35,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 8,
    zIndex: 2,
    shadowColor: "#AB4CFE",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.35,
    shadowRadius: 8,
    elevation: 8,

    ...Platform.select({
      android: {
        elevation: 8,
      },
    }),
  },
  profileContainer: {
    position: "absolute",
    bottom: 20,
    left: "50%",
    transform: [{ translateX: -50 }],
    alignItems: "center",
    zIndex: 2,
    shadowColor: "#FFE5B4",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#FFC107",
    borderWidth: 7,
    borderColor: "white",
  },
  cameraButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "white",
    borderRadius: 16,
    padding: 6,
    borderWidth: 2,
    borderColor: "#FFF8EE",
  },
  container1: {
    padding: 16,
    gap: 24,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    color: "#9796A1",
    marginLeft: 4,
  },
  inputavtive: {
    borderWidth: 1,
    borderColor: "#5F22D9",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
  },
  input: {
    borderWidth: 1,
    borderColor: "#EEEEEE",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
  },
  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#FFB4B4",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ProfileDetails;
