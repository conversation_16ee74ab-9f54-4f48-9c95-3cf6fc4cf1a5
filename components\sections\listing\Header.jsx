import { View, Text, Image, TouchableOpacity, StyleSheet } from "react-native";
import React from "react";
import { AppImages } from "../../../utils/AppImages";
import { Ionicons } from "@expo/vector-icons";

const Header = ({ bgImage, navigation }) => {
  return (
    <View style={styles.container}>
      <View style={styles.backgroundImageContainer}>
        <Image source={{ uri: bgImage }} style={styles.backgroungIMG} />
      </View>
      <View style={styles.topRow}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <View style={styles.heartContainer}>
          <Image source={AppImages.LIKE} />
        </View>
      </View>
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: 268,
    overflow: "hidden",
    position: "relative",
  },
  backgroundImageContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: 268,
    minHeight: 268,
    opacity: 0.8,
    zIndex: -1,
    borderBottomLeftRadius: 54,
    borderBottomRightRadius: 54,
    overflow: "hidden",
  },
  backgroungIMG: {
    resizeMode: "cover",
    width: "100%",
    height: "100%",
  },
  topRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 28,
    marginTop: 40,
  },
  heartContainer: {
    backgroundColor: "#5F22D9",
    padding: 8,
    borderRadius: 20,
  },
  backButton: {
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    borderRadius: 12,
    padding: 8,
  },
});
