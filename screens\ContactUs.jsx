import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ScrollView,
  Linking,
  Alert,
} from "react-native";
import { AppImages } from "../utils/AppImages";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { SocialIcons } from "../utils/svgs";

const contactMethods = [
  {
    id: 1,
    title: "Email Us",
    subtitle: "<EMAIL>",
    icon: AppImages.Email,
    actionType: "email",
  },
  {
    id: 2,
    title: "Chat on Whatsapp",
    subtitle: "+91-8084222212",
    icon: AppImages.Whatsapp,
    actionType: "whatsapp",
  },
  {
    id: 3,
    title: "Call Us",
    subtitle: "+91-9443322113",
    icon: AppImages.Phone,
    actionType: "call",
  },
];

const socials = [
  {
    id: 1,
    icon: <SocialIcons.Facebook />,
    link: "https://www.facebook.com/PlentiIndia",
    actionType: "facebook",
  },
  {
    id: 2,
    icon: <SocialIcons.Twitter />,
    link: "https://x.com/PlentiOfficial?t=VgYkkOGewfmzyYDIVp1gEw&s=08",
    actionType: "twitter",
  },
  {
    id: 3,
    icon: <SocialIcons.Youtube />,
    link: "https://www.youtube.com/@PlentiOfficial",
    actionType: "youtube",
  },
  {
    id: 4,
    icon: <SocialIcons.Instagram />,
    link: "https://www.instagram.com/plentiofficial/",
    actionType: "instagram",
  },
  {
    id: 5,
    icon: <SocialIcons.LinkedIn />,
    link: "https://www.linkedin.com/company/plentiindia/",
    actionType: "linkedin",
  },
];

const ContactUs = ({ navigation }) => {
  const handleContactPress = async (item) => {
    let url = "";
    const { actionType, subtitle } = item;

    switch (actionType) {
      case "email":
        url = `mailto:${subtitle}`;
        break;
      case "whatsapp":
        const whatsappNumber = subtitle.replace(/[^\d+]/g, "").startsWith("+")
          ? subtitle.substring(1).replace(/[^\d]/g, "")
          : subtitle.replace(/[^\d]/g, "");
        url = `https://wa.me/${whatsappNumber}`;
        break;
      case "call":
        url = `tel:${subtitle}`;
        break;
      default:
        console.warn("Unknown contact action type");
        return;
    }

    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert(
          "Error",
          `Unable to open this URL. The app might not be installed or the URL is invalid: ${url}`
        );
      }
    } catch (error) {
      console.error("Failed to open URL:", error);
      Alert.alert("Error", "An unexpected error occurred.");
    }
  };

  const handleSocialLinkPress = async (socialItem) => {
    const url = socialItem.link;
    if (!url) {
      Alert.alert("Error", "No link provided for this social icon.");
      return;
    }

    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert(
          "Error",
          `Unable to open this URL. Please ensure you have an app that can handle it or check the link: ${url}`
        );
      }
    } catch (error) {
      console.error("Failed to open URL:", error);
      Alert.alert(
        "Error",
        "An unexpected error occurred while trying to open the link."
      );
    }
  };

  const renderCard = ({ item }) => (
    <TouchableOpacity
      style={styles.card}
      onPress={() => handleContactPress(item)}
    >
      <View style={styles.iconContainer}>
        <Image source={item.icon} style={styles.icon} />
      </View>
      <View style={{ flex: 1 }}>
        <Text style={styles.cardTitle}>{item.title}</Text>
        <Text style={styles.cardSubtitle}>{item.subtitle}</Text>
      </View>
      <Image
        source={AppImages.NEXT}
        style={{
          height: 10,
          width: 10,
          resizeMode: "contain",
        }}
      />
    </TouchableOpacity>
  );

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <ScrollView style={{ backgroundColor: "#FFFFFF" }}>
        <View style={styles.container}>
          <BackButton navigation={navigation} />
          <View style={styles.titleContainer}>
            <Text style={styles.headingtext}>Contact Us</Text>
          </View>
        </View>

        <FlatList
          data={contactMethods}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderCard}
          scrollEnabled={false}
          contentContainerStyle={styles.cardList}
        />

        <Text style={styles.socialLabel}>Our Social Media</Text>
        <View style={styles.socialRow}>
          {socials.map((icon, index) => (
            <TouchableOpacity
              key={icon.id}
              style={styles.socialIconContainer}
              onPress={() => handleSocialLinkPress(icon)}
            >
              {icon.icon}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  headingtext: {
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#323643",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  cardList: {
    gap: 12,
    paddingHorizontal: 16,
    marginTop: 20,
    marginBottom: 10,
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F8F8F8",
    padding: 14,
    borderRadius: 14,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  iconContainer: {
    backgroundColor: "#6B50F64D",
    padding: 12,
    borderRadius: 50,
    marginRight: 14,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: "contain",
  },
  cardTitle: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    color: "#000",
  },
  cardSubtitle: {
    fontSize: 13,
    color: "#888",
    fontFamily: "Poppins_400Regular",
  },
  arrow: {
    fontSize: 18,
    color: "#999",
  },
  socialLabel: {
    textAlign: "center",
    marginTop: 30,
    marginBottom: 16,
    fontSize: 14,
    color: "#888",
    fontFamily: "Poppins_500Medium",
  },
  socialRow: {
    flexDirection: "row",
    justifyContent: "center",
    marginHorizontal: 40,
    columnGap: 20,
    marginBottom: 30,
  },
  socialIconContainer: {
    backgroundColor: "#5F22D9",
    width: 34,
    height: 34,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 30,
  },
  socialIcon: {
    width: 15,
    height: 15,
    resizeMode: "contain",
  },
});

export default ContactUs;
