import React from "react";
import {
  Image,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import GlobalStyles from "../styles/GlobalStyles";
import { AppImages } from "../utils/AppImages";

function Checkout({ navigation }) {
  return (
    <SafeAreaView style={GlobalStyles.androidSafeArea}>
      <View
        style={{
          backgroundColor: "#FFFFFF",
          height: "100%",
        }}
      >
        <View style={styles.container}>
          <Image source={AppImages.E6} style={styles.rightCircle} />

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color="#000" />
          </TouchableOpacity>

          <Text style={styles.mainHeading}>Checkout</Text>
        </View>

        <View style={styles.container1}>
          <Image source={AppImages.E7} style={styles.bottomLeft} />

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Full name</Text>
            <TextInput
              style={styles.input}
              placeholder="Full name"
              defaultValue="User name"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>E-mail</Text>
            <TextInput
              style={styles.input1}
              placeholder="E-mail"
              defaultValue="<EMAIL>"
              keyboardType="email-address"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Phone Number</Text>
            <TextInput
              style={styles.input1}
              placeholder="Phone Number"
              defaultValue="+91 940986 8786"
              keyboardType="phone-pad"
              placeholderTextColor="#999"
            />
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} activeOpacity={0.8}>
            <Text style={styles.buttonText}>Pay Now</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 200,
    backgroundColor: "#FFFFFF",
    overflow: "hidden",
    position: "relative",
  },
  rightCircle: {
    position: "absolute",
    right: -5,
    zIndex: 1,
    opacity: 0.8,
    resizeMode: "contain",
  },
  bottomLeft: {
    position: "absolute",
    top: 300,
    zIndex: 1,
  },
  backButton: {
    position: "absolute",
    top: 40,
    left: 35,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 8,
    zIndex: 2,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,

    ...Platform.select({
      android: {
        elevation: 8,
      },
    }),
  },
  mainHeading: {
    position: "absolute",
    top: 50,
    right: 150,
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  profileContainer: {
    position: "absolute",
    bottom: 20,
    left: "50%",
    transform: [{ translateX: -50 }],
    alignItems: "center",
    zIndex: 2,
    shadowColor: "#FFE5B4",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#FFC107",
    borderWidth: 7,
    borderColor: "white",
  },
  cameraButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "white",
    borderRadius: 16,
    padding: 6,
    borderWidth: 2,
    borderColor: "#FFF8EE",
  },
  container1: {
    marginTop: 70,
    position: "relative",
    padding: 16,
    gap: 24,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    color: "#9796A1",
    marginLeft: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: "#5F22D9",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
  },
  input1: {
    borderWidth: 1,
    borderColor: "#EEEEEE",
    borderRadius: 12,
    paddingTop: 22,
    paddingRight: 22,
    paddingBottom: 22,
    paddingLeft: 15,
    fontSize: 16,
    color: "#000",
    backgroundColor: "white",
    zIndex: 2,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 30,
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
  },
  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: "#FFB4B4",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default Checkout;
