import { View, Text, StyleSheet } from "react-native";
import React from "react";
import { AppColors } from "../../../utils/AppColors";

const Divider = ({ text }) => {
  return (
    <View style={styles.container}>
      <View style={styles.line} />
      <Text style={styles.text}>{text}</Text>
    </View>
  );
};

export default Divider;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    width: "80%",
    alignItems: "center",
    marginVertical: 20, // Adjust spacing as needed
    justifyContent: "center",
  },
  line: {
    flex: 1,
    height: 0.7,
    backgroundColor: AppColors.greyColor,
  },
  text: {
    position: "absolute",
    backgroundColor: "white", // Matches the screen background to "hide" the line behind
    paddingHorizontal: 10,
    fontSize: 14,
    color: AppColors.blackColor,
    textAlign: "center",
  },
});
